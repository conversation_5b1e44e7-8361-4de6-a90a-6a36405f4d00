const path = require('path');
const fs = require('fs');

const PROD_CONTAINER_TAGS = [
    {
        "key": "Name",
        "value": "web-esc-task-definition-prod"
    },
    {
        "key": "env",
        "value": "production"
    },
    {
        "key": "type",
        "value": "ecs-task"
    },
]

const prod_env_variables = [
    'sails_hookTimeout',
    'DATABASE_URL',
    'READER_DATABASE_URL',
    'STORAGE_DATABASE_URL',
    'INTERACTION_LOG_ENABLED',
    'DATA_ENCRYPTION_KEY',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'AWS_REGION_SLS',
    'LAMBDA_SINGAL_PDF_FN',
    'NOTIFICATION_FN_KEY',
    'LAMBDA_QUEUE_PUSH_MESSAGE_MAILS_FN',
    'LAMBDA_QUEUE_SITE_MESSAGE_MAILS_FN',
    'LAMBDA_MULTIPLE_PDF_FN',
    'LAMBDA_ZIPPED_IMGS_FN',
    'LAMBDA_MERGE_PDF_FN',
    'LAMBDA_DOC_TO_PDF_FN',
    'LAMBDA_VIDEO_THUMBNAIL_FN',
    'INNDEX_S3_BUCKET',
    'S3_BUCKET',
    'SOURCE_MAIL_ADDRESS',
    'CONTACT_MAIL_ADDRESS',
    'BASE_URL',
    'PUBLIC_URL',
    'URL_SUFFIX',
    'NEW_PROJECT_MAIL_ALERT_ADDRESSES',
    'SUPPORT_MAIL_ADDRESSES',
    'EXCLUDED_EMAIL_ADDRESSES',
    'TEST_USER_EMAIL_ADDRESSES',
    'CUT_OFF_HOUR',
    'REFRESH_COUNTRIES_LIST_META',
    'CLEAN_UP_OLD_API_ACCESS_LOGS',
    'DVLA_API_HOST_URL',
    'DVLA_API_KEY',
    'SMART_SHEET_API_KEY',
    'POPPLER_BIN_PATH',
    'SECURE_AUTH_KEY',
    'OPTIMA_LISTENER_KEY',
    'OPTIMA_INTEGRATION_SERVER',
    'OPTIMA_GATEWAY_SECRET_KEY',
    'CITB_V2_API_SERVER',
    'RTW_CHECK_API_HOST',
    'WEBHOOK_LISTENER_KEY',
    'RTW_STATUS_UPDATE_LISTENING_FN_HOST',
    'SENTRY_DNS',
    'CAPTCHA_SECRET_KEY',
    'PRO_CORE_LIVE_MODE',
    'PRO_CORE_APP_CLIENT_ID',
    'PRO_CORE_APP_CLIENT_SECRET',
    'AZURE_INNDEX_TENANT_ID',
    'AZURE_APP_CLIENT_ID',
    'AZURE_APP_CLIENT_SECRET',
    'AZURE_SSO_TENANT_IDs',
    'POWERBI_INNDEX_TENANT_ID',
    'POWERBI_CLIENT_ID',
    'POWERBI_CLIENT_SECRET',
    'POWERBI_GROUP_ID',
    'INNDEX_SLACK_BOT_HOOK_URL',
    'INNDEX_FIRST_INDUCTION_HOOK_URL',
    'DISTANCE_METRIX_KEY',
    'CITB_API_SERVER',
    'CITB_API_P_KEY',
    'COUNTRY_LAYER_KEY',
    'ACCU_WEATHER_API_KEY',
    'HOURLY_ACCU_WEATHER_API_KEY',
    'MAX_THUMBNAIL_ITERATION_PER_CRON',
    'MAX_ITERATION_PER_CRON',
    'EXECUTION_TIME_LOGGING',
    'SLOW_API_LOGGING_THRESHOLD',
    'REFRESH_TOKEN_SECRET',
    'ENC_SECRET_KEY',
    'MAPBOX_SECRET_ACCESS_KEY',
    'LAMBDA_EB_CRON_SCHEDULER',
    'LAMBDA_EB_CRON_DELETE',
    'LAMBDA_ONE_TIME_EVENT_SCHEDULER',
];

const PROD_CONTAINER_SECRETS = prod_env_variables.map(key => {
    return {
        "name": key,
        "valueFrom": `arn:aws:secretsmanager:$AWS_DEFAULT_REGION:$AWS_ACCOUNT_ID_NUMBER:secret:prod/api-server/env-list-V0VtSd:${key}::`
    };
});

let default_task_definition = require('./default-web-task-definition.json');

default_task_definition.family = 'inndex-prod-web-task-def';
default_task_definition.tags = PROD_CONTAINER_TAGS;
(default_task_definition.containerDefinitions || []).map(container => {
    container.secrets = PROD_CONTAINER_SECRETS;
    container.environment.push({
        "name": "SKIP_LOADING_ENV_CONF",
        "value": "true"
    });
    container.logConfiguration = {
        "logDriver": "awsfirelens",
        "options": {
            "LabelKeys": "container_name,ecs_task_definition,source,ecs_cluster,container_id",
            "Labels": "{job=\"prod-api\",env=\"prod\"}",
            "LineFormat": "key_value",
            "Name": "loki",
            "RemoveKeys": "ecs_task_arn",
            "Url": "$AWS_FIRELENS_GRAFANA_LOKI_URL"
        }
    };
});

// https://repost.aws/knowledge-center/ecs-container-log-destinations-fargate
// extra container to route logs
default_task_definition.containerDefinitions.push({
    "name": "log_router",
    "image": "grafana/fluent-bit-plugin-loki:2.0.0-amd64",
    "cpu": 0,
    "memoryReservation": 100,
    "portMappings": [],
    "essential": true,
    "environment": [],
    "mountPoints": [],
    "volumesFrom": [],
    "user": "0",
    "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
            "awslogs-create-group": "true",
            "awslogs-group": "inndex-prod-web-container",
            "awslogs-region": "$AWS_DEFAULT_REGION",
            "awslogs-stream-prefix": "firelens"
        }
    },
    "firelensConfiguration": {
        "type": "fluentbit",
        "options": {
            "enable-ecs-log-metadata": "true"
        }
    }
});
// console.log(JSON.stringify(default_task_definition, null, 4));

fs.writeFileSync(path.join(process.cwd(), `task-definition.json`), JSON.stringify(default_task_definition, null, 4));
