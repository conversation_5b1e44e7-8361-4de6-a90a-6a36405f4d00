/**
 * Created by spatel on 19/3/19.
 */
const slowAPILoggingThreshold = (process.env.SLOW_API_LOGGING_THRESHOLD && +process.env.SLOW_API_LOGGING_THRESHOLD) || false;
const {displayDateFormat_DD_MM_YYYY, ALMAS_APIs} = sails.config.constants;
const {
    APP_ENV,
    OPTIMA_SERVER_TIMEZONE: optima_timezone,
    OPTIMA_GATEWAY_SECRET_KEY,
    OPTIMA_INTEGRATION_SERVER: optima_integration_server_url
} = (sails.config.custom);
const _sortBy = require('lodash/sortBy');
const HttpService = require('./HttpService');
const {errorObject} = require('./ResponseService');
const AccessLogService = require('./AccessLogService');
const EmailService = require('./EmailService');
const moment = require('moment');
const momentTz = require('moment-timezone');
const whilst = require('async/whilst');
const parallelLimit = require('async/parallelLimit');
const TimeLogService = require('./TimeLogService');
const {storeOptimaVehicleEvents} = require('./VehicleService');
const {
    getProjectTimezone,
} = require('./DataProcessingService');

const getAllActiveSettings = async () => {
    let rawResult = await sails.sendNativeQuery(`SELECT site_id,
                                                        STRING_AGG(DISTINCT key, ' ') as keys,
                                                        STRING_AGG(DISTINCT project_ref::text, ',') as projects,
                                                        biometric_source
                                                 FROM optima_setting
                                                 GROUP BY biometric_source, site_id
                                                 HAVING (site_id IS NOT NULL)
                                                    AND biometric_source = 'optima';`
    );
    return (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];
};

const getOptimaConnectionStatus = async (optima_setting, endpoint = 'checkStatus') => {
    sails.log.info(`Checking status with optima server, project: ${optima_setting.project_ref}`);
    let api_response = await callOptimaBoxGateway(optima_setting, {
        endpoint: endpoint,
        method: 'GET',
    }, {});
    // sails.log.info('Response from n/w call', api_response.status);
    return api_response
};


const getAllReadersList = async (optima_setting) => {
    let readers = [];
    sails.log.info('fetching readers list');
    if(optima_setting.enrolment_type === 'facial'){
        // use facial enrolment reader API
        let readersPage = await callOptimaBoxGateway(optima_setting, {
            endpoint: 'Enrolment/reader',
            method: 'GET',
        }, {});

        if (readersPage.success && readersPage.status === 200) {
            readers = readersPage.data || [];
        }else{
            sails.log.info('Optima readers call got failed');
            throw new Error('Failed while fetching readers list');
        }
    }else {
        // use fingerprint enrolment readers list API
        let readersFirstPage = await callOptimaBoxGateway(optima_setting, {
            endpoint: 'Lecteur/Page/0',
            method: 'GET',
        }, {});

        if (readersFirstPage.success && readersFirstPage.status === 200) {
            let responseData = readersFirstPage.data;
            if (responseData.totalCount) {
                readers = responseData.result;
                for(let i=1; i < responseData.pageCount; i++) {
                    let readersNextPage = await callOptimaBoxGateway(optima_setting, {
                        endpoint: `Lecteur/Page/${i}`,
                        method: 'GET',
                    }, {});
                    responseData = readersNextPage.data;
                    if (readersNextPage.success && readersNextPage.status === 200) {
                        readers = [...readers, ...responseData.result];
                    }
                }
            }
        }else{
            sails.log.info('Optima readers call got failed');
            throw new Error('Failed while fetching readers list');
        }
    }
    sails.log.info('total readers count:', readers.length);
    return readers;
};

/**
 *
 * @param optimaSetting <{site_id, key, project_ref}>
 * @param endpoint
 * @param method
 * @param bodyOrParam
 * @param apiVersion
 * @returns {Promise<{data: {}, error: boolean, message: string}|{headers: *, data: *, success: boolean, status: *}|{result: string, headers: *, data: *, error: boolean, status: *}|*>}
 */
const callOptimaBoxGateway = async (optimaSetting, {endpoint, method}, bodyOrParam, apiVersion = 'v4') => {
    let called_at = (new Date()).getTime();
    let headers = {
        'secretKey': OPTIMA_GATEWAY_SECRET_KEY,
        'action': endpoint,
        'method': method,
        'apiVersion': apiVersion,
        'optima': `${APP_ENV}:${optimaSetting.site_id}`,
        'optimaToken': optimaSetting.key
    };
    let TIMEOUT = (endpoint === 'Enrolment') ? 20500 : 15000;
    // sails.log.info('gateway call were', headers);
    let endpoint_base_url = optima_integration_server_url;
    let gateway_response = await HttpService.makePOST(endpoint_base_url, bodyOrParam, headers, true, TIMEOUT);
    let finished_at = (new Date()).getTime();

    AccessLogService.interactionLog(AccessLogService.EXTERNAL_SERVICE_ID.OPTIMA, optimaSetting.site_id, !!gateway_response.success, gateway_response.status, {
        method: 'POST',
        url: endpoint_base_url,
        payload: bodyOrParam,
        headers,
        response_body: gateway_response.data,
        response_headers: (gateway_response.headers || {})
    }, (finished_at - called_at), 'req.api-action-name-here', 'rest').catch(sails.log.error);

    sails.log.info(`Response from gateway, target: [${optimaSetting.site_id}] status: ${gateway_response.status} seconds: ${(finished_at - called_at) / 1000} ${method}: ${endpoint}`);
    // We will get 503 from gateway when target box is offline
    if(gateway_response.status === 503){
        gateway_response.offline = true;
        if (HttpService.typeOf(gateway_response.data, 'string')) {
            gateway_response.data = `Service is temporarily unavailable, Please try again later.`;
        }
    }
    return gateway_response;
};

const ACCEPTED_EVENT_NATURE_IDs = [
    0,  // Default event
    5,   // Access card event
    59, // ANPR Vehicle event
];

/**
 *  Hard coded setting, for below `project + unit + reader + nature_id` event will be assumed as Badge Accepted.
 *
 * @type {[{unit_name: string, project_ref: number, reader_name: string, nature_id: number, override: {natureId: number}}]}
 */
const SUSPENDED_EVENTS_SETTINGS = [
    {
        project_ref: 3076,
        nature_id: 2,       // Badge suspended
        unit_name: `Pod 133`,
        reader_name: `Welfare Entrance`,
        override: {
            natureId: 0,
        }
    },
    {
        project_ref: 3076,
        nature_id: 2,       // Badge suspended
        unit_name: `Pod 133`,
        reader_name: `Welfare Exit`,
        override: {
            natureId: 0,
        }
    },
    {
        project_ref: 24,
        nature_id: 2,       // Badge suspended
        unit_name: `Pod 133`,
        reader_name: `Welfare Exit`,
        override: {
            natureId: 0,
        }
    },
    {
        project_ref: 24,
        nature_id: 2,       // Badge suspended
        unit_name: `Pod 133`,
        reader_name: `Welfare Entrance`,
        override: {
            natureId: 0,
        }
    }
];

const isVehicleEvent = event => (event.registrationNumber && (event.registrationNumber).toString().trim().length && event.registrationNumber !== 'null');

const saveEvents = async (os, events = []) => {
    // if(+os.project_ref === 1){try{require('fs').writeFileSync(require('path').resolve(process.cwd(), `../json_dump-${os.project_ref}-${(new Date()).getTime()}.json`), JSON.stringify(events));}catch (e) { console.error('Writing dump',e);}}
    let tz = os.tz || optima_timezone;
    sails.log.info(`Listener: got total event records: ${events.length}, tz: ${tz}`);
    // let suspended_override = SUSPENDED_EVENTS_SETTINGS.find(o => o.project_ref === +os.project_ref);
    const override_list = SUSPENDED_EVENTS_SETTINGS.filter(o => o.project_ref === +os.project_ref);
    let {batchRecords, vehicleRecords} = events.reduce((out, r) => {
        let timestamp = null;
        try {
            timestamp = momentTz.tz(r.dateTimeEvent, 'YYYY-MM-DDTHH:mm:ss.SSS', tz).utc();
            // timestamp = moment(r.dateTimeEvent, 'YYYY-MM-DDTHH:mm:ss.SSS');
            timestamp = (timestamp && timestamp.isValid() && timestamp.unix() > 0) ? timestamp.unix() : null;
        } catch (e) {
            sails.log.error('Invalid dateTime received while syncing', r);
            sails.log.error(e);
        }
        const suspended_override = override_list.find(o =>
            (o.nature_id === r.natureId) &&
            (o.unit_name === r.unitName) &&
            (o.reader_name === r.readerName)
        );
        if (suspended_override && suspended_override.override) {
            sails.log.info(`Listener: site_id: ${os.site_id}, overriding secondary_id:"${r.id}" event natureId from: ${r.natureId} to:`, suspended_override.override);
            r = {
                ...r,
                ...(suspended_override.override || {}),
            }
        }

        let event_type = TimeLogService.determineEventType(r.readerName);
        let isVehicle = isVehicleEvent(r);

        let baseEvent = {
            badge_number: r.badgeNumber,
            secondary_id: r.id,
            nature: r.nature,
            nature_id: r.natureId,
            event_date_time: timestamp,
            reader_id: r.readerId,
            reader_name: r.readerName,
            automatism_id: r.automatismId,
            automatism_name: r.automatismName,
            unit_id: r.unitId,
            unit_name: r.unitName,
            entity: r.entity,
            event_type,

            project_ref: +os.project_ref,
        };
        if (ACCEPTED_EVENT_NATURE_IDs.includes(baseEvent.nature_id) && baseEvent.event_date_time) {
            // Acceptable events
            if (isVehicle) {
                // vehicle events
                baseEvent.registration_number = (r.registrationNumber).toString().trim().toUpperCase();
                out.vehicleRecords.push(baseEvent);
            }else if (!isVehicle && baseEvent.badge_number) {
                // user events
                baseEvent.temperature = r.temperature;
                baseEvent.mask = r.mask;
                out.batchRecords.push(baseEvent);
            }
        }
        return out;
    }, {
        batchRecords: [],
        vehicleRecords: [],
    });

    let outcome = {
        insertedRecords: [],
        vehicleEvents: [],
    };

    sails.log.info(`Listener: Total valid records to insert are: ${batchRecords.length}`);
    if(batchRecords.length) {
        // @NOTE: This won't work for NON-induction based projects
        let {updated_events} = await TimeLogService.expandBadgeEventWithUserInfo(batchRecords, +os.project_ref);
        // @todo: sorting records by event date time will help?

        let insertedRecords = await sails.models.badgeevent.createEach(updated_events);
        sails.log.info(`Listener: Done, project: ${os.project_ref} insert success`, insertedRecords.length);
        try {
            // prepare daily log
            let expanded_log = TimeLogService.onlyValidOptimaLogs(insertedRecords);
            TimeLogService.notifyOnHighTemperatureEntry([os.project_ref], expanded_log).catch(sails.log.error);
            let projectInfo = await sails.models.project_reader.findOne({
                where: {id: +os.project_ref},
                select: ['custom_field']
            });
            await TimeLogService.ingestTimeLogs(expanded_log, projectInfo);
        } catch (dailyLogError) {
            sails.log.error(`Listener: daily log creation failed`, dailyLogError);
        }
        outcome.insertedRecords = insertedRecords;
    }

    if(vehicleRecords.length){
        outcome.vehicleEvents = await storeOptimaVehicleEvents(os, vehicleRecords);
    }
    return outcome;

};

const eventListener = async (payload) => {
    let called_at = (new Date()).getTime();
    let {site_id, events} = (payload || {});
    if (!site_id || !events || !events.length) {
        sails.log.warn(`Listener: site_id: ${site_id}, Invalid request body`);
        return {error: true, code: 400, message: 'Invalid request body', data: {}};
    }
    sails.log.info(`Listener: site_id: ${site_id}, received events count: ${events.length}, now: ${moment().format()}`);
    let settings = await sails.models.optimasetting_reader.find({
        where: {
            site_id,
            biometric_source: 'optima'
        },
        sort: ['id ASC']
    });
    let [setting] = settings; // process with first setting only..
    if (!setting) {
        sails.log.warn(`Listener: site_id: ${site_id}, Site Id info not found`);
        return {error: true, code: 404, message: 'Site Id info not found', data: {site_id}};
    }
    sails.log.info(`Listener: site_id: ${site_id}, belongs to project: ${setting.project_ref}`);
    let {insertedRecords, vehicleEvents} = await saveEvents(setting, events);
    let finished_at = (new Date()).getTime();
    let ms_taken = (finished_at - called_at);
    sails.log.info(`Listener: site_id: ${site_id}, total insert: (${(insertedRecords || []).length}+${(vehicleEvents || []).length}) seconds: ${ms_taken/1000}, now: ${moment().format('HH:mm:ss')}`);
    if(slowAPILoggingThreshold && (ms_taken) >= slowAPILoggingThreshold){
        sails.log.warn(`Slow execution [POST /api/optima/events-listener?site_id=${site_id}] ${ms_taken}ms`);
    }
    return {
        saved: (insertedRecords || []).length,
        vehicles: (vehicleEvents || []).length,
        total: events.length
    };
};


const createBadge = async (optima_setting, {lastName, firstName, groupId, statusId, timeSlotsId, badgeNumber}) => {
    let createRequest = {
        lastName, firstName, groupId,
        statusId, timeSlotsId,
        badgeNumber,
    };
    if (createRequest.firstName) {
        createRequest.firstName = createRequest.firstName.replace(/[&\/\\#,+()$~%.'":*?<>{}]/g, '').substring(0, 10);
    }
    if (createRequest.lastName) {
        createRequest.lastName = createRequest.lastName.replace(/[&\/\\#,+()$~%.'":*?<>{}]/g, '').substring(0, 10);
    }
    sails.log.info(`create optima badge, site_id: ${optima_setting.site_id}`, createRequest);
    let api_response = await callOptimaBoxGateway(optima_setting, {
        endpoint: 'Badge',
        method: 'POST',
    }, createRequest);

    if (api_response.status === 201) {
        sails.log.info(`Badge created, site_id: ${optima_setting.site_id}, badge #`, api_response.data);
        if(api_response.data && api_response.data.success && api_response.data.badge){
            return api_response.data;
        }
        return {badge: api_response.data};
    }

    sails.log.info(`Badge creation failed, site_id: ${optima_setting.site_id}, status: ${api_response.status}`);
    if(api_response.status === 500 && api_response.data && api_response.data.message){
        // case where we were receiving JSON response `{"message": "Something went horribly, horribly wrong while servicing your request.", "statusCode": 500}`
        api_response.data.message = sails.__('optima_badge_creation_error');
    }
    return api_response;
};

const updateBadge = async (optima_setting, badgeNumber, payload) => {
    sails.log.info(`Updating badge, site_id: ${optima_setting.site_id}, badge # ${badgeNumber}`);
    let api_response = await callOptimaBoxGateway(optima_setting, {
        endpoint: `Badge/${badgeNumber}`,
        method: 'PUT',
    }, payload);

    if (api_response.success && api_response.status === 200) {
        sails.log.info(`Updated badge, site_id: ${optima_setting.site_id}, badge # ${badgeNumber}`);
        return {success: true, data: api_response.data};
    }
    return api_response;
};

const deleteBadge = async (optima_setting, badgeNumber) => {
    sails.log.info(`Deleting badge, site_id: ${optima_setting.site_id}, badge # ${badgeNumber}`);
    let api_response = await callOptimaBoxGateway(optima_setting, {
        endpoint: `Badge/${badgeNumber}`,
        method: 'DELETE',
    }, {});

    if (api_response.success && api_response.status === 200) {
        sails.log.info(`Deleted badge, site_id: ${optima_setting.site_id}, badge # ${badgeNumber}`);
        return {success: true};
    }
    return api_response;
};

const remoteEnrolFace = async (optima_setting, badgeNumber, {faceS3Url, base64}) => {
    if (!faceS3Url && !base64) {
        return {success: false, code: `face_url_missing`};
    }
    sails.log.info(`[Optima] remote enrol, site_id: ${optima_setting.site_id}, badge # ${badgeNumber}`);
    let faceBase64 = base64;
    if(faceS3Url){
        let imageBuffer = await HttpService.fetchS3UrlAsBuffer(faceS3Url);
        faceBase64 = Buffer.from(imageBuffer.data).toString('base64');
    }
    /*
    const get_payload = {
        "badgeNo": "6"
    };
    const post_payload = {
        "badgeNo": "6",
        "base64Photo": "/9j/4AAQSkZJRgABAQAASABIAAD",
    };
    */

    let api_response = await callOptimaBoxGateway(optima_setting, {
        endpoint: ALMAS_APIs.REMOTE_ENROLMENT_API,
        method: 'POST',
    }, {
        "badgeNo": badgeNumber,
        "base64Photo": faceBase64,
    }, 'v5');

    if (api_response.success && api_response.status === 200) {
        // {"badgeNo": 7, "fileSize": "29.9853515625 KB", "remoteEnrolment": "success"}
        sails.log.info(`remote enrolment successful, site_id: ${optima_setting.site_id}, badge # ${badgeNumber}`);
        return {success: true, data: api_response.data};
    }
    return api_response;
};

const getRemoteEnrolment = async (optima_setting, badgeNumber) => {
    sails.log.info(`[Optima] get remote enrol, site_id: ${optima_setting.site_id}, badge # ${badgeNumber}`);

    let api_response = await callOptimaBoxGateway(optima_setting, {
        endpoint: ALMAS_APIs.REMOTE_ENROLMENT_API,
        method: 'GET',
    }, {
        "badgeNo": badgeNumber,
    }, 'v5');

    // 200
    // {"badgeNo":2,"fileType":"NA","base64":"No enrolment found"}

    // 200
    // {"badgeNo":7,"fileType":"jpg","base64":"/9j/2wBDAAYEBQYFBAYGBQYHBwYIChAKCu/Mvo5wHntUpH58v8Amv8A6jUo+/Z"}
    if (api_response.success && api_response.status === 200) {
        sails.log.info(`get remote enrolment data, site_id: ${optima_setting.site_id}, badge # ${badgeNumber}, fileType: ${api_response.data.fileType}`);
        api_response.data.base64 = _constructFullBase64Image(api_response.data || {});
        return {success: true, data: api_response.data};
    }
    return api_response;
};

const _constructFullBase64Image = ({fileType, base64}) => {
    switch (fileType) {
        case 'jpg':
            return `data:image/jpeg;base64,${base64}`;
        default:
            return base64;
    }
};

/**
 * get optima access group by label
 *
 * @param optima_setting
 * @param group_label
 * @returns {Promise<{error: boolean, message: *}|{id}>}
 */
const getOptimaAccessGroup = async (optima_setting, group_label = 'Deny Access') => {
    if(!optima_setting || !optima_setting.site_id || !optima_setting.key) {
        return errorObject(sails.__('no_bio_metric_configuration_found'));
    }
    sails.log.info(`fetching "${group_label}" access group of ${optima_setting.project_ref}, site_id: ${optima_setting.site_id}`);
    let {success, status, data} = await callOptimaBoxGateway(optima_setting, {
        endpoint: 'Groups',
        method: 'GET',
    }, {});
    let deny_access_group = {};
    if (success && status === 200) {
        deny_access_group = (data.groups || []).find((access_group => (access_group.libelle === group_label))) || {};
    }
    if(!deny_access_group.id) {
        return errorObject(`Deny access groups NOT found`);
    }
    return deny_access_group;
};

module.exports = {
    getRemoteEnrolment,
    remoteEnrolFace,
    // fetch all optima settings group By host

    // get events
    // store events
    // get more events
    // move on if no records found
    // move to next settings
    // follow same

    syncEvents: async (finalCallbackFn) => {

        let maxThreshold = (sails.config.custom.MAX_ITERATION_PER_CRON || 50);
        // let ACCEPTED_NATURE_VALUES = ['accepted fingerprint', 'badge accepted', 'accepted', 'fingerprint accepted'];

        try{
            let allSettings = await getAllActiveSettings();
            if (allSettings.length) {
                sails.log.info(`Total ${allSettings.length} configurations found, max allowed threshold: ${maxThreshold}`);

                let parallelJobs = [];
                for (let i = 0, len = allSettings.length; i < len; i++) {
                    let optima_setting = allSettings[i];
                    optima_setting.key = optima_setting.keys.split(' ').shift();
                    optima_setting.project_ref = (optima_setting.projects || '').split(',').shift() || 0;


                    parallelJobs.push((function (os, batchNo) {
                        return function (callback) {
                            sails.log.info(`Processing batchNo: ${batchNo} --> project: ${os.project_ref} site_id: ${os.site_id}`);

                            let lastRecordsInsertedCount;
                            let lastReceivedRecordsCount;
                            let totalRecordsInserted = 0;
                            let currentIteration = 0;

                            whilst(() => {
                                // we can save last empty request by checking count from `batchResponse.data == 500`
                                return (lastReceivedRecordsCount === undefined || lastReceivedRecordsCount >= 50) && currentIteration <= maxThreshold;
                            }, async (cb) => {

                                currentIteration++;

                                sails.log.info(`Processing batchNo: ${batchNo}:${currentIteration}, next iteration`);
                                try{

                                    let called_at = (new Date()).getTime();
                                    // let batchResponse = require(process.cwd() + '/local/facial-events-dump.json');
                                    let batchResponse = await callOptimaBoxGateway(os, {
                                        endpoint: 'Evenement',
                                        method: 'GET',
                                    }, {});
                                    let finished_at = (new Date()).getTime();
                                    sails.log.info(`Time taken by site: "${os.site_id}" batchNo: ${batchNo}:${currentIteration} seconds: ${(finished_at - called_at)/1000}, status: ${batchResponse.status}`);

                                    // when there are some records in response
                                    if (batchResponse.success && HttpService.typeOf(batchResponse.data, 'array') && batchResponse.data.length) {
                                        sails.log.info(`Processing batchNo: ${batchNo}:${currentIteration}, got total event records: ${batchResponse.data.length}`);
                                        lastReceivedRecordsCount = batchResponse.data.length;
                                        let {insertedRecords} = await saveEvents(os, batchResponse.data);
                                        lastRecordsInsertedCount = (insertedRecords || []).length;
                                        totalRecordsInserted += lastRecordsInsertedCount;
                                        cb(null, totalRecordsInserted);

                                        // when there are no records in response
                                        // but it is success response
                                    } else if(batchResponse.success && HttpService.typeOf(batchResponse.data, 'array') && batchResponse.data.length === 0){
                                        lastReceivedRecordsCount = 0;
                                        lastRecordsInsertedCount = 0;
                                        // No more records
                                        sails.log.info(`Processing batchNo: ${batchNo}:${currentIteration}, No more records`);
                                        cb(null, totalRecordsInserted);

                                        // when API call gets failed.
                                    } else {
                                        lastReceivedRecordsCount = 0;
                                        cb(batchResponse, totalRecordsInserted);
                                    }
                                }catch (singleBatchFailedError) {
                                    sails.log.error(`One of batch process failed for batchNo: ${batchNo}:${currentIteration}`);
                                    sails.log.error(`skipping this batch set`, os, singleBatchFailedError);
                                    return cb(singleBatchFailedError, totalRecordsInserted);
                                }

                            }, (exception, totalInserts) => {
                                // After processing all badge events of this optima setting

                                sails.log.info(`Completed batchNo: ${batchNo} total inserted: ${totalInserts}, total iterations: ${currentIteration}`);
                                if(exception){
                                    let readable_setting = `projects: ${os.projects} site_id: ${os.site_id}`;
                                    sails.log.error(`sync failed batch: ${batchNo},`, readable_setting, (exception.unreachable ? `HTTP status:${exception.status} message: "${exception.message}"` : exception));
                                }
                                // if even this Job Queue got error
                                // Don't pass it back, else All other queue will halt
                                callback(null, {
                                    project_ref: +os.project_ref,
                                    site_id: os.site_id,
                                    batchNo: batchNo,
                                    // iterations: currentIteration,
                                    totalInserts
                                });
                            });

                        };
                    })(optima_setting, i));

                }

                // Process All optima setting in parallel
                // two at a time
                parallelLimit(parallelJobs, 2, function (err, results) {
                    if(err){
                        // This shouldn't be logged
                        sails.log.error(`One of parallel job failed, This shouldn't be logged...`, err);
                        return finalCallbackFn(null);
                    }
                    // results from all parallel job
                    sails.log.info('All parallel jobs completed');
                    console.table(_sortBy(results, ['totalInserts']));
                    finalCallbackFn(null, results);
                });
            } else {
                sails.log.info(`No configurations found.`);
                finalCallbackFn(null, [])
            }
        }catch (syncEventError) {
            sails.log.error(`Something went wrong in cron job`, syncEventError);
            finalCallbackFn(syncEventError)
        }

    },

    getOptimaConnectionStatus,

    checkAllOptimaServerStatus: async () => {
        let allSettings = await getAllActiveSettings();
        sails.log.info(`Total ${allSettings.length} optima configurations found at ${moment().format()}`);
        if (!allSettings.length) {
            return [];
        }
        let results = [];
        for (let i = 0; i < allSettings.length; i++) {
            let row = allSettings[i];

            let api_response = await getOptimaConnectionStatus({
                site_id: row.site_id,
                key: row.keys.split(' ')[0]
            });
            results.push({
                status: api_response.status,
                error: api_response.error,
                message: api_response.message,
                data: api_response.data,
                projects: row.projects,
                site_id: row.site_id,
            });
        }

        let stable = results.filter(r => !r.error);
        let unstable = results.filter(r => r.error);
        sails.log.info(`Total unreachable machines ${unstable.length}`);
        let messages = [];
        messages.push(`*Optima status check (${moment().format(displayDateFormat_DD_MM_YYYY)}):*`);

        if(unstable.length){
            let ids = unstable.map(r => (r.projects && +r.projects));
            sails.log.info(`fetching unreachable projects info`, ids);
            let projects = await sails.models.project_reader.find({where: {id : ids}, select: ['id', 'name']})
            messages.push(`Unreachable projects: :sos:`);
            messages.push(`\`\`\`ID : SiteId : Name : Status : Message\n`);
            unstable.map(r => {
                let p = projects.find(p => p.id === (+r.projects)) || {};
                let msg = r.message;
                if(r.status === 503 && r.data){
                    msg = (r.data.status);
                }
                messages.push(`${r.projects} : ${r.site_id} : ${p.name} : ${r.status} : ${msg}`);
            });
            messages.push(`\`\`\` \n`);
        }
        if(stable.length){
            messages.push(`Stable projects: *${stable.map(r => r.projects).join(', ')}*`);
            if(!unstable.length) {
                messages.push(`All :ok: :heavy_check_mark:`);
            }
        }

        let output = {
            results,
            message: messages.join('\n')
        };
        await EmailService.innDexSlackNotifier(output, sails.config.custom.INNDEX_SLACK_BOT_HOOK_URL);
        return output;
    },

    getAllReadersList,

    importOptimaLogs: async (project_optima_setting, eventRows = []) => {
        /*
        "temperature": null,
        "mask": null,
        "registrationNumber": "",
        "id": 331,
        "nature": "Badge accepted",
        "natureId": 0,
        "dateTimeEvent": "2021-06-17T11:27:08.000",
        "readerId": 1,
        "readerName": "Facial Entrance",
        "automatismId": null,
        "automatismName": null,
        "unitId": 1,
        "unitName": "Network 1",
        "badgeNumber": 5,
        "entity": "Bruce-Huds Jeremy",
        "matricule": null

        // optima_host {host:port}
        // secondary_id: evenementId
        // badge_number: numeroRessource
        // nature: ?
        // nature_id: 0
        // event_date_time: dateEvt
        // reader_id: lecteurId
        // reader_name: ?
        // automatism_id: ?
        // automatism_name: = 'MANUAL_IMPORT'
        // unit_id: ?
        // unit_name: ?
        // entity: porteurPrenom + porteurNom
        // temperature: null
        // event_type -----
        // company_ref ----
        // user_ref ----
        // user_revision_ref ----
        // project_ref -> get from request
        */
        if(!project_optima_setting.has_optima_source){
            sails.log.info(`Project doesn't have optima as bio-metric source`);
            return [];
        }
        let tz = project_optima_setting.tz || optima_timezone;

        // let suspended_override = SUSPENDED_EVENTS_SETTINGS.find(o => o.project_ref === +project_optima_setting.project_ref);
        const override_list = SUSPENDED_EVENTS_SETTINGS.filter(o => o.project_ref === +project_optima_setting.project_ref);
        sails.log.info(`Importing optima logs of project: ${project_optima_setting.project_ref}, tz: ${tz}`);
        let batchRecords = eventRows.map(r => {
            let timestamp = null;
            try {
                timestamp = momentTz.tz(r.dateTimeEvent, 'YYYY-MM-DDTHH:mm:ss.SSS', tz).utc();
                // timestamp = moment(r.dateTimeEvent, 'YYYY-MM-DDTHH:mm:ss.SSS');
                timestamp = (timestamp && timestamp.isValid() && timestamp.unix() > 0) ? timestamp.unix() : null;
            } catch (e) {
                sails.log.error('Invalid dateTime received while syncing', r);
                sails.log.error(e);
            }
            const suspended_override = override_list.find(o =>
                (o.nature_id === r.natureId) &&
                (o.unit_name === r.unitName) &&
                (o.reader_name === r.readerName)
            );
            if (suspended_override && suspended_override.override) {
                sails.log.info(`Importing: overriding secondary_id:"${r.id}" event natureId from: ${r.natureId} to:`, suspended_override.override);
                r = {
                    ...r,
                    ...(suspended_override.override || {}),
                }
            }

            let event_type = TimeLogService.determineEventType(r.readerName);

            return {
                optima_host: '',
                secondary_id: r.id,
                badge_number: r.badgeNumber,
                nature: r.nature,
                nature_id: r.natureId,
                event_date_time: timestamp,
                reader_id: r.readerId,
                reader_name: r.readerName,
                automatism_id: r.automatismId,
                automatism_name: r.automatismName,
                unit_id: r.unitId,
                unit_name: r.unitName,
                entity: r.entity,
                temperature: r.temperature,
                mask: r.mask,
                event_type,
            }
        }).filter(r => ACCEPTED_EVENT_NATURE_IDs.includes(r.nature_id) && r.badge_number);

        sails.log.info(`Total valid records are: ${batchRecords.length}`);

        if(!batchRecords.length){
            return [];
        }

        // filter and find non-existing records.
        let badgeEventsOrFilter = batchRecords.reduce((filter, row) => {
            filter.push({
                secondary_id: row.secondary_id,
                badge_number: row.badge_number,
                event_date_time: row.timestamp,
                reader_id: row.reader_id,
            });

            return filter;
        }, []);

        sails.log.info(`Looking for existing badge-events logs with OR filter: ${badgeEventsOrFilter.length}`);
        const badgeEventsOrStatement = (badgeEventsOrFilter.length > 1) ? {or: badgeEventsOrFilter} : badgeEventsOrFilter.pop();
        let existing_badge_events = await sails.models.badgeevent.find({
            where: {
                project_ref: project_optima_setting.project_ref,
                ...(badgeEventsOrStatement || {}),
            },
            select: ['id', 'secondary_id', 'badge_number', 'event_date_time']
        });

        sails.log.info(`Total existing badge-events are: ${existing_badge_events.length}`);

        let all_new_badge_events = batchRecords.filter(row => {
            let db_record = existing_badge_events.find(db => (
                +db.secondary_id === +row.secondary_id &&
                +db.badge_number === +row.badge_number
            ));

            return (!(db_record && db_record.id));
        });

        sails.log.info(`Total valid records to insert are: ${all_new_badge_events.length}`);
        if(all_new_badge_events.length){
            let {updated_events} = await TimeLogService.expandBadgeEventWithUserInfo(all_new_badge_events, project_optima_setting.project_ref);

            let insertedRecords = await sails.models.badgeevent.createEach(updated_events);
            sails.log.info(`Done inserting, success`, insertedRecords.length);

            // prepare daily log
            let expanded_log = TimeLogService.onlyValidOptimaLogs(insertedRecords);
            let projectInfo = await sails.models.project_reader.findOne({
                where: {id: project_optima_setting.project_ref},
                select: ['custom_field']
            });
            TimeLogService.ingestTimeLogs(expanded_log, projectInfo).then(result => {
                sails.log.info('Successfully processed imported logs');
            }).catch(er => {
                sails.log.error('Failed while processing imported logs');
                sails.log.error('Error is', er);
            });

            return insertedRecords;
        }

        return [];
    },

    callOptimaBoxGateway,

    eventListener,
    getOptimaAccessGroup,
    createBadge,
    updateBadge,
    deleteBadge,

};
