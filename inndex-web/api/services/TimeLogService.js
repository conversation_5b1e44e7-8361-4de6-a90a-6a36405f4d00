/**
 * Created by spatel on 15/12/19.
 */

const meterToMile = 0.000621371;
const dbDateFormat = 'YYYY-MM-DD';
const {
    fall_back_timezone,
    INNDEX_SETTING_KEY: {TEST_PROJECT_IDs},
    displayDateFormat_D_MMM_Y,
    displayDateFormat_DD_MM_YYYY,
    displayTimeFormat_HH_mm_ss,
    dbDateFormat_YYYY_MM_DD_HH_mm_ss,
    maxPermissibleTemperature
} = sails.config.constants;
const _groupBy = require('lodash/groupBy');
const _sortBy = require('lodash/sortBy');
const _orderBy = require('lodash/orderBy');
const _uniq = require('lodash/uniq');
const dayjs = require('dayjs');
const duration = require('dayjs/plugin/duration');
dayjs.extend(duration);
let moment = require("moment");
const momentTz = require('moment-timezone');
const _get = require('lodash/get');
const { startDailyShiftFatigueMgtProcess, startDailyTotalFatigueMgtProcess } = require('./FatigueManagementService');
const {getLatestUserRevision} = require('./UserRevisionService');
const HttpService = require('./HttpService');
const {
    EVENT_TYPE,
    VALID_SOURCES,
} = require('./ServiceConstants');
const {
    populateUserRefs,
    getProjectDistrict,
} = require('./DataProcessingService');
const {allProjectAdminsByOneOfDesignations} = require('./TokenUtil');
const {
    getUserFullName,
    getDailyTimeEventV2,
    getVisitorsTimeLogForDates,
    expandUserDocs,
    getActiveTravelTime,
    showDurationAsHours,
    getTotalTravelDuration,
    getTotalTime,
    populateEmployerRefs, populateJobRoles,
    getTotalTravelDistance,
    showDuration,
    isFirstAider, haveFireMarshal, havePts, haveCSCS, haveSMSTSOrSSSTSDoc,
    getProjectTimezone,
} = require('./DataProcessingService');
const {sendMail} = require('./EmailService');

const getShiftConfigOfUser = async (user_ref, project_ref, event_date_time, visitor_ref = null, type = undefined) => {
    if(!user_ref && !visitor_ref){
        return [];
    }
    // ((o.valid_from <= now && o.valid_till >= now) || (o.valid_from <= now && o.valid_till === null));
    return await sails.models.userworkingshift.find({
        type: type,
        ...(visitor_ref ? {visitor_ref} : {user_ref}),
        project_ref: project_ref,
        valid_from: {'<=': event_date_time}
    }).sort([
        {valid_from: 'DESC'},
        {id: 'DESC'},
    ]).limit(1);
};

const deriveShiftConfig = (log, projectTz, only_start_time = false) => {
    // if(0){
        // Old logic. // Disabled
        let start_of_hr = momentTz.unix(+log.event_date_time).tz(projectTz).minute(0).second(0);//.tz(fall_back_timezone); // .subtract(3, 'h') // 3 hr is of `tolerance_hrs`
        let auto_shift_hr = start_of_hr.hour();

        return only_start_time ? {
            start_time: auto_shift_hr,
        } : {
            project_ref: +log.project_ref,
            visitor_ref: log.visitor_ref,
            user_ref: log.user_ref,
            start_time: auto_shift_hr,
            default_hrs: 10,
            tolerance_hrs: 0,
            valid_from: start_of_hr.unix(),
            type: 0,
            valid_till: null,
        };
    // }
    /*
    // @notes: Using same timezone for all projects
    let event = momentTz.unix(+log.event_date_time);
    let event_uk = event.clone().tz(fall_back_timezone);
    sails.log.info('User interaction', event_uk.format(dbDateFormat_YYYY_MM_DD_HH_mm_ss), `[UTC ${event.format(dbDateFormat_YYYY_MM_DD_HH_mm_ss)}]`, event_uk.isDST() ? 'DST on' : '');
    let start_of_hr = event.clone().minute(0).second(0).hour();
    let valid_from = event.clone().minute(0).second(0).unix();
    let tolerance_hr = 0;
    let offset_hrs = (event_uk.utcOffset())/60;
    if(offset_hrs || event_uk.isDST()){
        // sails.log.info('Timezone offset is', event_uk.utcOffset())
        tolerance_hr = event_uk.hour() + offset_hrs;
        sails.log.info('UTC start_of_hr:', start_of_hr, 'offset_hrs:', offset_hrs, 'will use:', -offset_hrs, 'tolerance_hr:', tolerance_hr);

        start_of_hr = -offset_hrs;
    }
    let x = event.clone().subtract(start_of_hr, 'h');
    // sails.log.info(`event`, x.format(f1))
    sails.log.info(`Show interaction on`, x.format(dbDateFormat_YYYY_MM_DD_HH_mm_ss), `shift on [${(start_of_hr + tolerance_hr)}:00]`)

    return only_start_time ? {
        start_time: start_of_hr,
    } : {
        project_ref: +log.project_ref,
        visitor_ref: log.visitor_ref,
        user_ref: log.user_ref,
        start_time: start_of_hr,
        default_hrs: 10,
        tolerance_hrs: tolerance_hr,
        valid_from: valid_from,
        type: 0,
        valid_till: null,
    };*/
};

const getLastEventOfUserByLog = async ({project_ref, user_ref, visitor_ref, event_date_time}) => {
    let all_logs = [];
    sails.log.info('tls: Searching recent time events of user/visitor');
    let geo_fence_logs = await sails.models.usertimelog.find({
        project_ref: +project_ref,
        user_ref: user_ref,
        visitor_ref: visitor_ref,
        event_date_time: {'<': event_date_time},
    }).sort('event_date_time DESC').limit(1);
    if (geo_fence_logs && geo_fence_logs.length) {
        all_logs = geo_fence_logs;
    }
    if (user_ref) {
        let badge_logs = await sails.models.badgeevent.find({
            project_ref: +project_ref,
            user_ref: user_ref,
            event_type: {'!=': EVENT_TYPE.UNKNOWN},
            event_date_time: {'<': event_date_time},
        }).sort('event_date_time DESC').limit(1);
        if (badge_logs && badge_logs.length) {
            all_logs = [...all_logs, ...onlyValidOptimaLogs(badge_logs)];
        }
    }

    sails.log.info('tls: Recent logs count of user/visitor', all_logs.length);
    if (!all_logs.length) {
        return null;
    }
    let [last_time_log] = all_logs.sort((a, b) => (+b.event_date_time) - (+a.event_date_time));
    return last_time_log;
};

const getAutoShiftHour = async (log, existingShiftConfigHr, projectTz, previousShiftStartedOn = undefined) => {
    // if shift time is empty, round up event time to floor hours
    let shift_config = deriveShiftConfig(log, projectTz);
    if(existingShiftConfigHr === shift_config.start_time){
        // No point of processing further, shift will be same As before
        sails.log.info('tls: No point of processing further, shift will be same As before, i.e.', existingShiftConfigHr);
        return existingShiftConfigHr;
    }

    // fetch last ONE events of user from all sources (optima + geo-fence + touch-byte)
    let last_time_log = await getLastEventOfUserByLog(log);
    if (!last_time_log) {
        sails.log.info('tls: first visit on-site use-case, user:', log.user_ref);
        let user_working_shift = await sails.models.userworkingshift.create(shift_config);
        return user_working_shift.start_time;
    }

    let hrs_since_last_event = dayjs.unix(+log.event_date_time).diff(dayjs.unix(+last_time_log.event_date_time), 'h');

    sails.log.info(`tls: last log of type: ${last_time_log.event_type}, "${hrs_since_last_event}" hrs since time: ${+last_time_log.event_date_time}`);

    let create_new_shift = false;
    if(last_time_log.event_type === EVENT_TYPE.IN && log.event_type === EVENT_TYPE.IN && (hrs_since_last_event > 15)){
        // - If user does 2 consecutive ins; (IN...IN) (so no out in between them) ;
        // if the time between events is > 15 hours then counts as a new shift
        create_new_shift = true;
    }
    /*
    // Can't limits hrs here as User might actually doing overtime?
    else if(last_time_log.event_type === EVENT_TYPE.IN && log.event_type === EVENT_TYPE.OUT){
        // - If user had an IN and doing an OUT (IN...OUT)
        // - do nothing with shift
    }*/
    else if(last_time_log.event_type === EVENT_TYPE.OUT && log.event_type === EVENT_TYPE.IN && (hrs_since_last_event > 6)){
        // - If last OUT and current IN (OUT...IN)
        // if the time between events is > 6 hours then counts as a new shift
        create_new_shift = true;
    }
    /*else if(last_time_log.event_type === EVENT_TYPE.OUT && log.event_type === EVENT_TYPE.OUT && (hrs_since_last_event > 15)){
        // - If last OUT and current OUT (OUT...OUT)
        // if the time between events is > 15 hours then counts as a new shift
        create_new_shift = true;
    }*/

    if(create_new_shift){
        let user_working_shift = await sails.models.userworkingshift.create(shift_config);
        return user_working_shift.start_time;
    }

    sails.log.info('tls: NO need to create NEW auto shift, use existing one, i.e.', existingShiftConfigHr);
    return existingShiftConfigHr;
};

// project.default_in_duration
const groupRequestDataSet = async (list, project) => {
    let projectTz = getProjectTimezone(project);
    let auto_shift_override = (project && project.custom_field && project.custom_field.auto_shift_override !== undefined && project.custom_field.auto_shift_override !== null) ? project.custom_field.auto_shift_override : false;
    let sortedList = _sortBy(list, ['event_date_time']);
    let default_cut_off_hour = (isNaN(sails.config.custom.CUT_OFF_HOUR) ? 0 : sails.config.custom.CUT_OFF_HOUR);
    for (let i = 0, len = sortedList.length; i < len; i++) {
        let log = sortedList[i];

        // sails.log.info('\n\n');sails.log.info(JSON.stringify({event_date_time: log.event_date_time, type: log.event_type, t: dayjs.unix(+log.event_date_time).format()}));
        // @todo: spatel: This DB call be optimized and cached
        let [config] = await getShiftConfigOfUser(log.user_ref, log.project_ref, log.event_date_time, log.visitor_ref);
        let few_hrs_before = dayjs.unix(+log.event_date_time).subtract(6, 'h').unix();
        sails.log.info(`tls: Processing an ${log.event_type} Old config shift is:`, (config && config.start_time), 'valid from:', (config && config.valid_from), 'few_hrs_before', few_hrs_before, `a_s_o: ${auto_shift_override}`);

        let shift_time = (config) ? config.start_time : default_cut_off_hour;

        if ([EVENT_TYPE.IN, EVENT_TYPE.OUT].includes(log.event_type) && (!config || +config.valid_from < few_hrs_before)) {

            if(auto_shift_override !== false){
                shift_time = (auto_shift_override === true) ? default_cut_off_hour : auto_shift_override;
                sails.log.info('tls: Valid shift config not found for user, got auto_shift_override, using', shift_time);
            }else{
                sails.log.info('tls: Valid shift config not found, auto generating one, existingShiftConfigHr', shift_time);
                shift_time = await getAutoShiftHour(log, shift_time, projectTz);
                config = null;      // previous config is not used anymore.
                sails.log.info('tls: Got Auto shift as', shift_time);
            }
        }

        let t = momentTz.unix(+log.event_date_time).tz(projectTz);//.utc();
        sails.log.info(`tls: User(${log.user_ref}) | Visitor(${log.visitor_ref}) shift id: ${(config && config.id)} shift_time: ${shift_time} event: ${t.format()}`);
        // identify user's shift.
        // get starting point of that shift.
        // get_date(timestamp + start_at)
        let t2 = t.clone().tz(projectTz).subtract(shift_time, 'h');
        if(t2.isDST() !== t.isDST()){
            sails.log.info(`tls: t2 ${t2.format()} (DST: ${t2.isDST()}) === + ${shift_time} === t ${t.format()} (DST: ${t.isDST()})`);
            // Change of DST here
            // https://github.com/moment/moment/issues/4743#issuecomment-811306874
            // DST: OFF => ON
            if(t.isDST() && t.hour() === shift_time){
                // t2 = t2.add(1, 'h');
                t2 = t2.clone().add(12, 'h').startOf('day').minute(t2.minute());

            }
        }
        sails.log.info(`tls: User(${log.user_ref}) | Visitor(${log.visitor_ref}) ${log.event_type} for day: ${t2.format()} Tz: ${projectTz}`);
        // console.log(t.format() + '  ===> ' + t2.format('YYYY-MM-DD'));
        sortedList[i].shift_time = shift_time;
        sortedList[i].day_of_yr = t2.format('YYYY-MM-DD');
    }
    return _groupBy(sortedList, (l) => {
        return JSON.stringify({
            day_of_yr: l.day_of_yr,
            user_ref: l.user_ref ? l.user_ref : null,
            visitor_ref: l.visitor_ref ? l.visitor_ref : null,
            project_ref: l.project_ref
        })
    });
};

/**
 *  Create chunks of user sessions on site.
 * @param type
 * @param timestamp
 * @param existing_log
 * @returns {[]}
 */
const buildDurations = (type, timestamp, existing_log = {}) => {

    // duration: {start, end, is_automated, seconds}
    //timestamp = (new Date(+timestamp * 1000));

    let durations = existing_log.durations || [];
    if (type === EVENT_TYPE.IN) {
        if (existing_log.last_out) {
            // user was out, Going In.
            // create new duration record

            durations.push({start: timestamp, end: null});
        } else if (existing_log.recent_in && durations.length) {
            // don't have last out but have existing IN
            // Double IN ?
            // we can either self close previous IN
            // OR
            // replace previous IN with this one

            // // replace previous duration record
            let last = durations.pop();
            last.start = timestamp;
            durations.push(last);
        } else {
            // first IN
            // create new duration record
            durations.push({start: timestamp, end: null});
        }
    } else if (type === EVENT_TYPE.OUT) {

        if (existing_log.recent_in && durations.length) {
            // user was in, Going OUT. Will update out for last duration record.

            // will also be case of double OUT, later one will be considered
            let last = durations.pop();
            last.end = timestamp;
            if (last.start) {
                last.seconds = timestamp - last.start;
            }
            durations.push(last);

        } else if (!existing_log.recent_in) {
            // missed while going IN, now going OUT
            // create new duration record with
            // should be create duration record? or keep it null
            durations.push({start: null, end: timestamp});
        }
    } else if(type === EVENT_TYPE.INTERACTION){

    }
    return durations;
};

const generate_total_sec = (durations) => {
    return (durations || []).reduce((sec, duration) => {
        if (duration.seconds) {
            if (!sec) {
                sec = 0;
            }
            sec += parseInt(duration.seconds)
        }
        return sec;
    }, null);
};

const processLogBatch = async (batch, project, force_clean = false) => {
    try {

        let groups = await groupRequestDataSet(batch, project);
        // console.log(JSON.stringify(groups, null, 2));
        let groupKeys = Object.keys(groups);
        let daily_logs = [];
        let processed_days = [];
        for (let i = 0, len = groupKeys.length; i < len; i++) {
            let groupMeta = groupKeys[i];

            let {day_of_yr, user_ref, visitor_ref, project_ref} = JSON.parse(groupMeta);
            processed_days.push({day_of_yr, user_ref, visitor_ref, project_ref});

            // get user_daily_log for day_of_yr
            // let existing_log = await sails.models.userdailylog.findOne({
            //     day_of_yr,
            //     user_ref,
            //     visitor_ref,
            //     project_ref
            // });
            let [existing_log] = await sails.models.userdailylog.find({
                where: {
                    day_of_yr,
                    user_ref,
                    visitor_ref,
                    project_ref
                },
                sort: ['id DESC'],
                limit: 1
            });
            if(existing_log){
                existing_log.day_of_yr = moment(existing_log.day_of_yr).format(dbDateFormat);
            }
            // let existing_log = user_daily_log.find(l => l.day_of_yr === day_of_yr && l.user_ref === user_ref && l.project_ref === project_ref);

            // re-generate existing duration from events if needed?
            let totalLogs = (groups[groupMeta] || []);
            if (!force_clean && existing_log && existing_log.events && existing_log.events.length) {
                totalLogs = [...existing_log.events, ...totalLogs];
            }
            totalLogs = _sortBy(totalLogs, ['event_date_time']);

            let db_record = existing_log ? Object.assign({}, existing_log) : {};
            existing_log = undefined;

            totalLogs.forEach(l => {
                let source = l.source ? l.source : VALID_SOURCES.OPTIMA;
                if(l.user_location){
                    source = VALID_SOURCES.GEO_FENCE;
                }else if(l.door){
                    source = VALID_SOURCES.TOUCHBYTE;
                }
                // this is being used for OptimaController.expandDailyLogInfo
                let compactLog = {event_type: l.event_type, event_date_time: l.event_date_time, source, id: l.id};

                // l.event_date_time = (new Date(+l.event_date_time * 1000));
                // Not Needed for now. As we don't deduct OUT time from total time.
                let durations = buildDurations(l.event_type, l.event_date_time, existing_log);
                // let durations = [];

                if (l.event_type === EVENT_TYPE.IN) {
                    // processing logic for IN type event
                    if (existing_log && existing_log.day_of_yr) {
                        // if record already exists, update it

                        if (!existing_log.first_in) {
                            existing_log.first_in = l.event_date_time;
                            // User made an OUT earlier and then making IN now, in this situation earlier out is of no use.
                            if(existing_log.last_out && (+l.event_date_time > +existing_log.last_out)){
                                existing_log.last_out = null;
                            }
                        }
                        existing_log.recent_in = l.event_date_time;
                        existing_log.durations = durations;
                        existing_log.events.push(compactLog);

                    } else {
                        // create new log for day_of_yr
                        existing_log = {
                            id: db_record.id,
                            user_ref: user_ref,
                            visitor_ref: visitor_ref,
                            project_ref: project_ref,
                            company_ref: l.company_ref ? l.company_ref : (db_record.company_ref ? db_record.company_ref : null),
                            user_revision_ref: l.user_revision_ref ? l.user_revision_ref : (db_record.user_revision_ref ? db_record.user_revision_ref : null),

                            day_of_yr: day_of_yr,
                            first_in: l.event_date_time,
                            last_out: null,
                            recent_in: l.event_date_time,
                            total_in_sec: null,

                            shift_info: {}, // shift_time
                            durations: durations,
                            events: [compactLog], // this is being used for OptimaController.expandDailyLogInfo
                            // processed_log_ids: [],
                            // in_time_type: db_record.in_time_type || 1,
                        };
                    }

                } else if (l.event_type === EVENT_TYPE.OUT) {
                    // processing logic for OUT type event

                    if (existing_log && existing_log.day_of_yr) {
                        // if record already exists, update it

                        existing_log.last_out = l.event_date_time;
                        existing_log.durations = durations;
                        existing_log.events.push(compactLog);

                    } else {
                        // create new log for day_of_yr
                        existing_log = {
                            id: db_record.id,
                            user_ref: user_ref,
                            visitor_ref: visitor_ref,
                            project_ref: project_ref,
                            company_ref: l.company_ref ? l.company_ref : (db_record.company_ref ? db_record.company_ref : null),
                            user_revision_ref: l.user_revision_ref ? l.user_revision_ref : (db_record.user_revision_ref ? db_record.user_revision_ref : null),

                            day_of_yr: day_of_yr,
                            first_in: null,
                            last_out: l.event_date_time,
                            recent_in: null,
                            total_in_sec: null,

                            shift_info: {},// shift_time
                            durations: durations,
                            events: [compactLog], // this is being used for OptimaController.expandDailyLogInfo
                            // processed_log_ids: [],
                            // in_time_type: db_record.in_time_type || 1,
                        };
                    }

                } else if (l.event_type === EVENT_TYPE.INTERACTION) {
                    // processing logic for single reader based Site - RC603

                    if (existing_log && existing_log.day_of_yr) {
                        // if record already exists, consider new interaction as OUT
                        compactLog.event_type = EVENT_TYPE.OUT;

                        existing_log.last_out = l.event_date_time;
                        existing_log.durations = durations;
                        existing_log.events.push(compactLog);

                    } else {
                        compactLog.event_type = EVENT_TYPE.IN;
                        // create new log for day_of_yr
                        existing_log = {
                            id: db_record.id,
                            user_ref: user_ref,
                            visitor_ref: visitor_ref,
                            project_ref: project_ref,
                            company_ref: l.company_ref ? l.company_ref : (db_record.company_ref ? db_record.company_ref : null),
                            user_revision_ref: l.user_revision_ref ? l.user_revision_ref : (db_record.user_revision_ref ? db_record.user_revision_ref : null),

                            day_of_yr: day_of_yr,
                            first_in: l.event_date_time,
                            last_out: null,
                            recent_in: l.event_date_time,
                            total_in_sec: null,

                            shift_info: {}, // shift_time
                            durations: durations,
                            events: [compactLog], // this is being used for OptimaController.expandDailyLogInfo
                            // processed_log_ids: [],
                            // in_time_type: db_record.in_time_type || 1,
                        };
                    }
                }

            });

            if (existing_log) {
                // @note: disabling logic to `total time - break time`
                // existing_log.total_in_sec = generate_total_sec(existing_log.durations);

                // for now we want..  just the difference between first in and last out that day
                if (existing_log.first_in && existing_log.last_out && existing_log.last_out > existing_log.first_in) {
                    existing_log.total_in_sec = (existing_log.last_out - existing_log.first_in);
                }
            }

            if (existing_log) {
                daily_logs.push(existing_log);
            }
        }

        return {
            success: true,
            daily_logs,
            processed_days,
        };
    } catch (exception) {
        sails.log.error('tls: Failed to process logs batch', exception);
        return {
            success: false,
            daily_logs: [],
            processed_days: [],
            exception
        };
    }

};

const storeProcessedLogs = async (batch = []) => {
    try {
        let recordsToCreate = (batch || []).filter(l => !l.id);
        sails.log.info('tls: storeProcessedLogs: Total records to create', recordsToCreate.length);
        let new_daily_logs = await sails.models.userdailylog.createEach(recordsToCreate);

        let recordsToUpdate = (batch || []).filter(l => l.id);
        sails.log.info('tls: storeProcessedLogs: Total records to update', recordsToUpdate.length);

        let updated_daily_logs = [];
        for (let i = 0, len = recordsToUpdate.length; i < len; i++) {
            let r = recordsToUpdate[i];
            let id = recordsToUpdate[i].id;
            delete r['id'];
            delete r['user_ref'];
            delete r['visitor_ref'];
            delete r['project_ref'];
            delete r['createdAt'];
            delete r['updatedAt'];
            updated_daily_logs.push(await sails.models.userdailylog.updateOne({id}).set(r));
        }

        return {
            success: true,
            new_daily_logs,
            updated_daily_logs,
        };
    } catch (exception) {
        sails.log.error('tls: Failed to store daily_logs', exception);
        return {
            success: false,
            new_daily_logs: [],
            updated_daily_logs: [],
            exception,
        };
    }

};

const reIndexTimeLogs = async (searchRequest, target_sources = [], force_clean = false, skip_fatigue = false, remove_empty_days = false) => {
    let all_logs = [];
    if (!searchRequest.filter) {
        searchRequest.filter = {};
    }

    let filter = {
        event_date_time: {'>=': searchRequest.from_timestamp, '<': searchRequest.to_timestamp},
        ...(searchRequest.filter)
    };
    sails.log.info('tls: Search filter is', filter, 'target_sources: ', target_sources, `force_clean: ${force_clean}, skip_fatigue ${skip_fatigue}, remove_empty_days ${remove_empty_days}`);
    if (target_sources.includes(VALID_SOURCES.GEO_FENCE)) {
        let logs = await sails.models.usertimelog.find(filter).sort('event_date_time ASC');
        if (logs && logs.length) {
            all_logs = logs;
        }
    }
    if (target_sources.includes(VALID_SOURCES.OPTIMA)) {
        let logs = await sails.models.badgeevent.find(filter).sort('event_date_time ASC');
        if (logs && logs.length) {
            all_logs = [...all_logs, ...onlyValidOptimaLogs(logs)];
        }
    }
    if (target_sources.includes(VALID_SOURCES.TOUCHBYTE)) {
        let logs = await sails.models.touchbytelog.find(filter).sort('event_date_time ASC');
        if (logs && logs.length) {
            all_logs = [...all_logs, ...onlyValidOptimaLogs(logs)];
        }
    }

    sails.log.info('tls: Total logs to process are', all_logs.length);
    if (all_logs.length) {
        // sails.log.info('tls: Processing logs', JSON.stringify(all_logs));
        let start_date = moment.unix(+searchRequest.from_timestamp);
        let to_date = moment.unix(+searchRequest.to_timestamp);
        let all_days = allDaysBetween(start_date, to_date);

        // prepare daily log
        // while force_cleaning we should clean non-affected days, that are b/w from_timestamp & to_timestamp

        // let unique_dates = _uniq(
        //     all_logs.map(l =>
        //         JSON.stringify({
        //             day_of_yr: moment.unix(+l.event_date_time).format('YYYY-MM-DD'),
        //             user_ref: l.user_ref,
        //             project_ref: l.project_ref
        //         })
        //     )
        // ).map(json => JSON.parse(json));
        let projectId = 0;
        let unique_dates = _uniq(
            all_logs.reduce((days, l) => {
                projectId = l.project_ref;
                let d = all_days.map(d =>
                    JSON.stringify({
                        day_of_yr: d,
                        user_ref: l.user_ref ? l.user_ref : null,
                        visitor_ref: l.visitor_ref ? l.visitor_ref : null,
                        project_ref: l.project_ref
                    }));
                days.push(...d);
                return days;
            }, [])).map(json => JSON.parse(json));
        let projectInfo = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['custom_field']
        });
        let result = await ingestTimeLogs(all_logs, projectInfo, force_clean, skip_fatigue);
        // result should also tell dates for which logs are re-created
        // let days_to_clean = (unique_dates - result.processed_days)
        // remove logs
        let non_processed_dates = unique_dates.filter(ud =>
            (result.processed_days || []).findIndex(d =>
                (d.day_of_yr === ud.day_of_yr && d.user_ref === ud.user_ref && d.visitor_ref === ud.visitor_ref && d.project_ref === ud.project_ref)
            ) === -1
        );

        //sails.log.info('unique dates', JSON.stringify(unique_dates));
        //sails.log.info('processed dates', JSON.stringify(result.processed_days));
        //sails.log.info('non processed dates', JSON.stringify(non_processed_dates));

        sails.log.info('tls: reIndexTimeLogs: Total processed dates', result.processed_days.length);
        sails.log.info('tls: reIndexTimeLogs: Total non-processed dates', non_processed_dates.length);
        if (remove_empty_days && non_processed_dates.length) {
            let deleted = await sails.models.userdailylog.destroy({
                where: {
                    or: non_processed_dates
                }
            });
            sails.log.info('tls: reIndexTimeLogs: Deleted non-processed date', non_processed_dates.length, JSON.stringify(deleted, null, 4));
        }

        sails.log.info('tls: reIndexTimeLogs: Status', result.success);
        result.count = all_logs.length;
        return result;
    }
    return {success: true, count: 0};
};

const ingestTimeLogs = async (logs, project, force_clean = false, skip_fatigue = false) => {
    let processedData = await processLogBatch(logs, project, force_clean);
    if (processedData.success) {
        // sails.log.info('store records:', JSON.stringify(processedData.processed_days, null, 4));
        // sails.log.info('store records:', JSON.stringify(processedData.daily_logs, null, 4));
        let result = await storeProcessedLogs(processedData.daily_logs);
        if(!skip_fatigue && result.success) {
            if(result.new_daily_logs.length > 0) {
                startDailyShiftFatigueMgtProcess(result.new_daily_logs);
            }
            if(result.updated_daily_logs.length > 0) {
                startDailyTotalFatigueMgtProcess(result.updated_daily_logs);
            }
        }

        sails.log.info('tls: ingestTimeLogs: done ?', result.success);
        return {
            ...result,
            processed_days: processedData.processed_days,
        };
    } else {
        return processedData;
    }
};

const reActOnShiftConfigChange = async (project_optima_setting, workingShift) => {
    let from_time = moment.unix(+workingShift.valid_from).subtract(1, 'd');
    let searchRequest = {
        from_timestamp: from_time.unix(),
        to_timestamp: moment().unix(),
        filter: {
            // user_ref: workingShift.user_ref,
            project_ref: workingShift.project_ref,
        }
    };
    if(workingShift.visitor_ref){
        searchRequest.filter.visitor_ref = workingShift.visitor_ref;
    }else{
        searchRequest.filter.user_ref = workingShift.user_ref;
    }

    // Call re-indexing for both, as we support both source at a time.
    let target_sources = [VALID_SOURCES.GEO_FENCE];
    if(!workingShift.visitor_ref){
        if(project_optima_setting.has_optima_source && project_optima_setting.key && project_optima_setting.key.length){ //@todo: spatel: project_optima_setting.has_optima_source
            target_sources.push(VALID_SOURCES.OPTIMA);
        }else if(project_optima_setting.has_touch_byte_source){
            target_sources.push(VALID_SOURCES.TOUCHBYTE);
        }
    }
    sails.log.info('Re-indexing sources:', target_sources);
    return await reIndexTimeLogs(searchRequest, target_sources, true, false, true);
};

const allDaysBetween = (start_date, to_date) => {
    let all_days = [];
    //console.log(start_date.format());
    while (start_date.isSameOrBefore(to_date, 'day')) {
        all_days.push(start_date.clone().format('YYYY-MM-DD'));
        start_date.add(1, 'd');
        //console.log(start_date.format());
    }
    return all_days;
};

const expandBadgeEventWithUserInfo = async (badge_events = [], project_ref) => {
    let need_recheck = [];
    try {
        sails.log.info('Listener: Total badge events to expand: ', badge_events.length);
        if(project_ref){
            let unique_badges = _uniq(badge_events.map(l => l.badge_number));
            let user_inductions = await sails.models.inductionrequest_reader.find({
                where: {
                    optima_badge_number: unique_badges,
                    project_ref: project_ref,
                }, select: ['status_code', 'user_ref', 'project_ref', 'optima_badge_number']
            });
            user_inductions = await populateUserRefs(user_inductions, 'user_ref', []);

            let data_map = user_inductions.filter(i => i.user_ref).reduce((info_map, induction) => {
                let user_id = (induction.user_ref && induction.user_ref.id);
                info_map[user_id] = {
                    user_ref: user_id,
                    badge_number: induction.optima_badge_number,
                    company_ref: induction.user_ref && induction.user_ref.parent_company,
                    project_ref: induction.project_ref,
                    user_revision_ref: null,
                };
                return info_map;
            }, {});

            let unique_user_ids = Object.keys(data_map);
            sails.log.info('Listener: Total unique_users are:', unique_user_ids.length);
            for(let i = 0, len = unique_user_ids.length; i < len; i++){
                let revision = await getLatestUserRevision(unique_user_ids[i]);
                data_map[unique_user_ids[i]].user_revision_ref = revision.id;
            }

            let updated_events = badge_events.map(e => {
                e.project_ref = project_ref;
                let details = (Object.values(data_map)).filter(u => +u.badge_number === +e.badge_number);
                if (details.length > 1) {
                    sails.log.error(`Listener: Multiple user with same badge: ${e.badge_number} project: ${project_ref}`, details);
                    let str = JSON.stringify({
                        badge_number: e.badge_number,
                        optima_host: e.optima_host,
                        project_ref,
                    });
                    need_recheck.push(str);
                }else if(!details.length){
                    sails.log.info(`Listener: Badge: ${e.badge_number} not-found in project: ${project_ref}, skipping.`);
                }
                if (details.length !== 1) {
                    return e;
                }
                let detail = details.shift();
                e.user_ref = detail.user_ref || null;
                e.user_revision_ref = detail.user_revision_ref || null;
                e.company_ref = detail.company_ref || null;
                // e.project_ref = detail.project_ref || null;
                return e;
            });

            need_recheck = _uniq(need_recheck).map(r => JSON.parse(r));
            if (need_recheck && need_recheck.length) {
                sails.log.error('Listener: Total need_recheck', JSON.stringify(need_recheck));
            }

            return {
                need_recheck,
                updated_events
            };
        }

        sails.log.info(`Using fallback logic, expandBadgeEventWithUserInfo`);

        let optima_setting = [];
        let optima_hosts = _uniq(badge_events.map(l => l.optima_host));
        if (optima_hosts && optima_hosts.length) {

            let startingNoOfEscaped = 0;
            let rawResult = await sails.sendNativeQuery(`SELECT project_ref, host, port FROM optima_setting WHERE biometric_source = 'optima' AND host || ':' || port IN (${optima_hosts.map(() => {
                startingNoOfEscaped++;
                return `$${startingNoOfEscaped}`;
            }).join(',')});`, [...optima_hosts]);

            if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
                optima_setting = rawResult.rows;
            }

        }

        sails.log.info('Total optima setting', optima_setting.length);

        let unique_users = _uniq(badge_events.map(l => JSON.stringify({
            host: l.optima_host,
            badge_number: l.badge_number
        }))).map(string => JSON.parse(string));
        sails.log.info('Total unique_users', unique_users.length);

        let unique_users_with_project = unique_users.reduce((list, l) => {

            let expanded = optima_setting.filter(s => `${s.host}:${s.port}` === l.host).map(s => {
                let log = Object.assign({}, l);
                log.project_ref = s.project_ref;
                return log;
            });

            list.push(...expanded);
            return list;
        }, []);
        sails.log.info('Total unique_users_with_project', unique_users_with_project.length);


        let whereOrClause = unique_users_with_project.map(l => {
            return {
                optima_badge_number: l.badge_number,
                project_ref: l.project_ref,
            }
        });
        //sails.log.info('where OR? ',JSON.stringify(whereOrClause, null, 4), whereOrClause.length);
        let inductions = await sails.models.inductionrequest.find({
            where: {
                or: whereOrClause
            }, select: ['status_code', 'user_ref', 'project_ref', 'optima_badge_number']
        });
        inductions = await populateUserRefs(inductions, 'user_ref', []);

        let unique_badge_events = unique_users_with_project.map(l => {
            let induction_form = inductions.find(i => i.project_ref === l.project_ref && +i.optima_badge_number === +l.badge_number);
            if (induction_form) {
                l.user_ref = induction_form.user_ref.id;
                l.company_ref = induction_form.user_ref.parent_company;
                l.project_ref = induction_form.project_ref;
            }
            return l;
        }).filter(l => l.user_ref);

        sails.log.info('Total unique_badge_events', unique_badge_events.length);

        for(let i = 0, len = unique_badge_events.length; i < len; i++){
            let revision = await getLatestUserRevision(unique_badge_events[i].user_ref);
            unique_badge_events[i].user_revision_ref = revision.id;
        }

        let updated_events = badge_events.map(e => {

            let details = unique_badge_events.filter(u => +u.badge_number === +e.badge_number && u.host === e.optima_host);
            if (details.length > 1) {
                sails.log.error('This needs Manual Check', details);
            }
            if (details.length !== 1) {
                let str = JSON.stringify({
                    badge_number: e.badge_number,
                    optima_host: e.optima_host,
                });
                need_recheck.push(str);
                return e;
            }
            let detail = details.shift();
            e.user_ref = detail.user_ref || null;
            e.user_revision_ref = detail.user_revision_ref || null;
            e.company_ref = detail.company_ref || null;
            e.project_ref = detail.project_ref || null;
            return e;
        });

        sails.log.info('Total updated_events', updated_events.length);

        need_recheck = _uniq(need_recheck).map(r => JSON.parse(r));
        if (need_recheck && need_recheck.length) {
            sails.log.error('Total need_recheck', JSON.stringify(need_recheck));
        }
        return {
            // count: badge_events.length,
            // optima_setting,
            // unique_users,
            // unique_badge_events,
            updated_events,
            need_recheck,
        };
    } catch (e) {
        sails.log.error('Failed while expanding badge events', e);
        return {
            error: e,
            count: badge_events.length,
            updated_events: badge_events
        };
    }
};

const onlyValidOptimaLogs = (logs = []) => {
    // sails.log.info('Total count before filtering', logs.length);
    return logs.filter(l => l.user_ref && l.project_ref);
};

/**
 * @deprecated As may cause slow performance when time logs are older than 16th March (one without ID into events)
 * @param projectId
 * @param daily_logs
 * @param removeEventsKey
 * @param loadTemperatureInfo
 * @param use_visitor_ref
 * @returns {Promise<*[]>}
 */
const expandDailyLogInfo = async (projectId, daily_logs, removeEventsKey = false, loadTemperatureInfo = false, use_visitor_ref = false) => {
    // Note: all events column is required.
    let clockInsOrFilters = {optima: [], geoFence: [], touchByte: []};

    daily_logs = (daily_logs || []).map((row) => {

        // Find first clock IN & their source
        for (let i = 0, len = (row.events || []).length; i < len; i++) {
            let event = (row.events || [])[i];
            if (!row.source) {
                row.source = event.source;                  // This is being used, to filter records by source, in controller
            }

            // No need to continue Loop, If there is no IN event.
            if (!row.clock_in) {
                break;
            }

            if (loadTemperatureInfo && event.event_type === EVENT_TYPE.IN && event.event_date_time === row.clock_in) {
                // First Clock IN
                // for default users
                let filter = {
                    event_date_time: +event.event_date_time,
                    user_ref: row.user_id,
                };
                if(event.id){
                    filter = {
                        id: event.id,
                    };
                }

                if (use_visitor_ref) {
                    // for visitors
                    if(event.id){
                        filter = {
                            id: event.id,
                        };
                    }else{
                        filter = {
                            event_date_time: +event.event_date_time,
                            visitor_ref: row.visitor_id,
                        };
                    }
                }
                if (event.source === VALID_SOURCES.OPTIMA) {
                    // optima based IN
                    clockInsOrFilters.optima.push(filter);
                } else if (event.source === VALID_SOURCES.GEO_FENCE) {
                    // geo-fence based IN
                    clockInsOrFilters.geoFence.push(filter);
                } else if (event.source === VALID_SOURCES.TOUCHBYTE) {
                    // TouchByte based IN
                    clockInsOrFilters.touchByte.push(filter);
                }
                break;
            }

        }
        return row;
    });

    if(!loadTemperatureInfo){
        return daily_logs;
    }
    sails.log.info('Query "IN" records to load clock_in_temperature');
    sails.log.info('optima filter', clockInsOrFilters.optima);
    sails.log.info('geoFence filter', clockInsOrFilters.geoFence);
    // sails.log.info('touchByte filter', clockInsOrFilters.touchByte);
    if (clockInsOrFilters.optima.length) {
        let optima_badge_events = await sails.models.badgeevent.find({
            where: {
                event_type: [EVENT_TYPE.IN, EVENT_TYPE.INTERACTION],
                project_ref: +projectId,
                ...(clockInsOrFilters.optima.length > 1 ? {or: clockInsOrFilters.optima} : clockInsOrFilters.optima.pop())
            },
            select: ['temperature', 'mask', 'event_date_time', 'user_ref']
        });

        sails.log.info('Attaching optima clock IN temperature values');
        daily_logs = daily_logs.map(log => {
            if (!log.clock_in) {
                return log;
            }

            let badge_event_row = (optima_badge_events.find(event => (
                event.event_date_time === log.clock_in &&
                event.user_ref === log.user_id
            ))) || {};
            log.clock_in_temperature = badge_event_row.id ? badge_event_row.temperature : log.clock_in_temperature;
            log.clock_in_mask = badge_event_row.id ? badge_event_row.mask : log.clock_in_mask;
            if (removeEventsKey) {
                // events are not needed in response
                log.events = undefined;
            }
            return log;
        });
    }
    if (clockInsOrFilters.geoFence.length) {
        let inntime_events = await sails.models.usertimelog.find({
            where: {
                event_type: [EVENT_TYPE.IN, EVENT_TYPE.INTERACTION],
                project_ref: +projectId,
                ...(clockInsOrFilters.geoFence.length > 1 ? {or: clockInsOrFilters.geoFence} : clockInsOrFilters.geoFence.pop())
            },
            select: ['temperature', 'event_date_time', 'user_ref', 'visitor_ref']
        });

        sails.log.info('Attaching innTime clock IN temperature values');
        daily_logs = daily_logs.map(log => {
            if (!log.clock_in) {
                return log;
            }

            let event_row = (inntime_events.find(event => (
                event.event_date_time === log.clock_in &&
                (
                    (!use_visitor_ref && event.user_ref === log.user_id) ||
                    (use_visitor_ref && event.visitor_ref === log.visitor_id)
                )
            ))) || {};
            log.clock_in_temperature = event_row.id ? event_row.temperature : log.clock_in_temperature;
            if (removeEventsKey) {
                // events are not needed in response
                log.events = undefined;
            }
            return log;
        });
    }
    // Touch Byte doesn't have temperature implementation
    /*
    if(clockInsOrFilters.touchByte.length){
    }*/
    return daily_logs;
};

const IN_TIME_TYPE = {
    ACTUAL_ON_SITE_TIME: 1,
    DEFAULT_ON_SITE_TIME: 2,
};

const notifyOnHighTemperatureEntry = async (projects, userEvents) => {
    let entryEvents = userEvents.filter(evt => ((evt.event_type === EVENT_TYPE.IN || evt.event_type === EVENT_TYPE.INTERACTION) && evt.temperature && (+evt.temperature >=  maxPermissibleTemperature)));
    if(!entryEvents.length){
        return false;
    }
    sails.log.info(`ALERT projects{${projects.join(',')}} got users with high temperature, count: ${entryEvents.length}`);
    // user entries events with temperature >= 37.8
    // notify nominated managers
    for (let i = 0, len = projects.length; i < len; i++) {
        let projectId = +projects[i];
        let projectEvents = entryEvents.filter(evt => evt.project_ref === projectId);
        if(!projectEvents.length){
            continue;
        }

        // In case same user had generated multiple events.
        // get Unique events list
        let uniqueProjectUsers = projectEvents.reduce((list, evt) => {
            list[evt.user_ref] = evt;
            return list;
        }, {});

        let projectUserResult = await allProjectAdminsByOneOfDesignations(projectId, [ 'nominated']);
        let user_ids = Object.keys(uniqueProjectUsers);
        let users_info = await sails.models.user_reader.find({select: ['first_name', 'middle_name', 'last_name'], where: {id: user_ids}});

        for (let j = 0; j < user_ids.length; j++) {
            let user_event = uniqueProjectUsers[user_ids[j]];
            let user_info = users_info.find(r => r.id === +user_ids[j]) || {};
            let user_full_name = getUserFullName(user_info);
            let subject = `High Temperature Warning: ${user_full_name}`;

            // All common template variables
            let template_variables = {
                title: subject,
                mail_body: 'high-temperature-entry-notification',
                recipient_first_name: '',
                user_full_name: user_full_name,
                user_first_name: user_info.first_name,
                user_temperature: (+user_event.temperature).toFixed(1),

                layout: false
            };
            sails.log.info(`processing notification for user: ${user_info.id} project: ${projectId}`);
            for (let k = 0; k < projectUserResult.length; k++) {
                let nomManager = projectUserResult[k].user_ref || {};
                let event_date_time = momentTz.unix(+user_event.event_date_time).tz(nomManager.timezone);

                let emailHtml = await sails.renderView('pages/mail/mail-content', {
                    ...(template_variables),

                    recipient_first_name: nomManager.first_name,
                    event_time: event_date_time.format(displayTimeFormat_HH_mm_ss),
                    event_date: event_date_time.format(displayDateFormat_DD_MM_YYYY),
                });
                sails.log.info('Sending mail to', nomManager.email, 'Subject:', subject);
                await sendMail(subject, [nomManager.email], emailHtml);
            }
        }
    }
    sails.log.info(`All notification sent`);
    return true;
};

const getFakeLocation = () => {
    let coordinates = [
        {"lat": 51.522759, "long":-0.077837, "distance": "0.4682", "name": "Main Gate", "is_fake": true},
        {"lat": 51.521733, "long":-0.078240, "distance": "0.4012", "name": "Main Gate", "is_fake": true},
        {"lat": 51.521379, "long":-0.078540, "distance": "0.4822", "name": "Main Gate", "is_fake": true},
        {"lat": 51.518292, "long":-0.079698, "distance": "0.4632", "name": "Front Desk", "is_fake": true},
        {"lat": 51.517928, "long":-0.080036, "distance": "0.4742", "name": "Front Desk", "is_fake": true},
        {"lat": 51.516646, "long":-0.079507, "distance": "0.4652", "name": "Front Desk", "is_fake": true},
        {"lat": 51.516319, "long":-0.082490, "distance": "0.4362", "name": "Parking Area Gate", "is_fake": true},
        {"lat": 51.516312, "long":-0.081439, "distance": "0.4262", "name": "Parking Area Gate", "is_fake": true},
        {"lat": 51.516472, "long":-0.079465, "distance": "0.4082", "name": "Parking Area Gate", "is_fake": true},
        {"lat": 51.516721, "long": -0.081084,"distance": "0.4262", "name": "S Block Gate", "is_fake": true},
        {"lat": 51.517272, "long": -0.080708,"distance": "0.4082", "name": "S Block Gate", "is_fake": true},
    ];
    return coordinates[Math.floor(Math.random()*coordinates.length)];
};

const createFakeEvent = async (targetUsers = [], event_type = 'IN', log_type = 'user') => {

    try {
        let event_date_time = moment().unix();

        sails.log.info(`creating "${event_type}" type fake "${log_type}" time log, total:`, (targetUsers || []).length);
        let fakeLogs = targetUsers.map(user => {
            return {
                ...(log_type == 'user' && {user_ref: user.user_ref}),
                ...(log_type == 'visitor' && {visitor_ref: user.visitor_ref}),
                project_ref: user.project_ref,
                company_ref: user.company_ref,
                user_location: getFakeLocation(),
                event_date_time,
                event_type,
                attachment_ref: null,
            }
        });
        if(log_type == 'user') {
            for(let i = 0, len = fakeLogs.length; i < len; i++){
                let revision = await getLatestUserRevision(fakeLogs[i].user_ref);
                fakeLogs[i].user_revision_ref = revision.id;
            }
        }
        if (fakeLogs.length) {
            let newRecords = await sails.models.usertimelog.createEach(fakeLogs);
            await ingestTimeLogs(newRecords, {});
        }
    } catch (e) {
        sails.log.info('Failed while creating fake event..', e);
    }
};

const generateFakeTimeLog = async (event_type) => {
    try {
        let test_projects = await sails.models.inndexsetting_reader.findOne({name: TEST_PROJECT_IDs});

        if (test_projects && test_projects.value && test_projects.value.length) {
            sails.log.info('Generating fake time log for projects:', test_projects.value);
            let fake_inductions = await sails.models.inductionrequest_reader.find({
                where: {
                    project_ref: test_projects.value,
                    status_code: [2, 6]
                }
            });
            sails.log.info('found test inductions:', (fake_inductions || []).length);
            let target_users = (fake_inductions || []).map(i => {
                let company_ref = _get(i, 'additional_data.user_info.parent_company.id', _get(i, 'additional_data.user_info.parent_company', null));
                return {
                    user_ref: i.user_ref,
                    project_ref: i.project_ref,
                    company_ref: company_ref,
                }
            }).filter(u => u.company_ref);

            await createFakeEvent(target_users, event_type);

            //Generating fake visitor log.
            let fake_visitors = await sails.models.visitor_reader.find({where:{ project_ref: test_projects.value }});
            sails.log.info('found test visitors:', (fake_visitors || []).length);
            if(fake_visitors.length){
                let target_visitors = (fake_visitors || []).map(i => {
                    return {
                        visitor_ref: i.id,
                        project_ref: i.project_ref,
                        company_ref: i.employer_ref,
                    }
                }).filter(u => u.company_ref);
                await createFakeEvent(target_visitors, event_type, 'visitor');
            }
        }
    } catch (err) {
        sails.log.info('Failed while generating fake time logs..', err);
    }

};

const READER_SUFFIX = {
    ENTRANCE: 'entrance',
    EXIT: 'exit',
    INTERACTION: 'interaction',
};


const processUserLogsWithInductions = async (project, logs, induction_requests, {
    total_break_time,
    total_time,
    total_travel_time,
    total_travel_distance,
    total_time_wo_break,
    total_time_included_duration,
    total_has_SMSTS_or_SSSTS,
    total_is_first_adider,
    total_has_fire_marshal,
    total_has_pts,
    total_has_cscs,
    total_adjustment_minutes,
}, timezone = 'UTC', include_breaks) => {
    let projectDistricts = await getProjectDistrict(project.id)
    let user_inductions = induction_requests.reduce((list, ir) => {
        if (!list[ir.user_ref]) {
            // sails.log.info(`User "${ir.user_ref}" induction "${ir.id}" has: `, (ir.user_doc_ids || []).map(d => d.name));
            ir.has_SMSTS_or_SSSTS = haveSMSTSOrSSSTSDoc(ir.user_doc_ids) || null;
            ir.is_first_adider = isFirstAider(ir.user_doc_ids) || null;
            ir.has_fire_marshal = haveFireMarshal(ir.user_doc_ids) || null;
            ir.has_pts = havePts(ir.user_doc_ids) || null;
            ir.has_cscs = haveCSCS(ir.user_doc_ids) || null;

            list[ir.user_ref] = ir;
        }

        return list;
    }, {});

    let records = logs.map(log => {
        let dayOfYr = moment(log.day_of_yr, dbDateFormat);
        let adjusted_sec = (+log.adjustment || 0) * 60;
        log.duration_in_sec = (+log.duration_in_sec ? (+log.duration_in_sec + adjusted_sec) : adjusted_sec);
        let reference_induction_record = user_inductions[log.user_id] || {};
        let additional_data = reference_induction_record.additional_data || {};
        let travelTimeOverride = getActiveTravelTime(reference_induction_record, dayOfYr);
        let travel_time = travelTimeOverride.travel_time || {};
        let total_duration_instance = getTotalTime(log.duration_in_sec, travel_time, true);

        // Calculate total break took by user
        let total_break = (log.durations || []).reduce((sum, currentValue, currentIndex, list) => {
            if (currentIndex === 0) {
                return sum;
            }
            let previousVal = list[currentIndex - 1];
            if (previousVal.end && currentValue.start) {
                const break_seconds = ((+currentValue.start) - (+previousVal.end));
                sum = sum + break_seconds;
            }
            return sum;
        }, 0);

        // Calculate total time without break (actual work time) = total time - break time
        let total_wo_break = +log.duration_in_sec - total_break;
        if(total_break && include_breaks && total_duration_instance){
            // Time with total work time + travel time
            total_duration_instance = total_duration_instance.subtract((+total_break), 'seconds');
        }

        let travel_time_minutes = getTotalTravelDuration(travel_time);
        total_travel_time = (total_travel_time + (travel_time_minutes ? (+travel_time_minutes) : 0));
        let distance_traveled = getTotalTravelDistance(travel_time);
        total_travel_distance = (total_travel_distance + (distance_traveled ? (+distance_traveled) : 0));
        total_break_time = (total_break_time + (total_break ? (+total_break) : 0));

        // Total time include work time and break time both
        total_time = (total_time + (log.duration_in_sec ? (+log.duration_in_sec) : 0));
        total_time_wo_break = (total_time_wo_break + (total_wo_break ? (+total_wo_break) : 0));
        total_time_included_duration = total_duration_instance ? total_time_included_duration.add(total_duration_instance) : total_time_included_duration;
        if (reference_induction_record.has_SMSTS_or_SSSTS) {
            total_has_SMSTS_or_SSSTS[reference_induction_record.user_ref] = 'Yes';
        }
        if (reference_induction_record.is_first_adider) {
            total_is_first_adider[reference_induction_record.user_ref] = 'Yes';
        }
        if (reference_induction_record.has_fire_marshal) {
            total_has_fire_marshal[reference_induction_record.user_ref] = 'Yes';
        }
        if (reference_induction_record.has_pts) {
            total_has_pts[reference_induction_record.user_ref] = 'Yes';
        }
        if (reference_induction_record.has_cscs) {
            total_has_cscs[reference_induction_record.user_ref] = 'Yes';
        }
        if (log.adjustment) {
            total_adjustment_minutes = total_adjustment_minutes + (+log.adjustment);
        }

        // sails.log.info(`log ${log.user_id} for date: ${dayOfYr.format(displayDateFormat_D_MMM_Y)}`, JSON.stringify(log.durations, null, 2), 'total sec: ', total_seconds_of_work);
        return {
            date: dayOfYr.format(displayDateFormat_D_MMM_Y),
            project_number: (project && project.id) ? project.project_number : '',
            project_name: (project && project.name) ? project.name : '',
            first_name: additional_data && additional_data.user_info && additional_data.user_info.first_name,
            last_name: additional_data && additional_data.user_info && additional_data.user_info.last_name,
            employee_number: (additional_data && additional_data?.employment_detail && additional_data?.employment_detail?.employee_number) || '-',
            name: additional_data && additional_data.user_info && additional_data.user_info.name,
            company: additional_data && additional_data.employment_detail && additional_data.employment_detail.employer,
            type_of_employment: (additional_data && additional_data.employment_detail && additional_data.employment_detail.type_of_employment) || '',
            employment_company: additional_data && additional_data.employment_detail && additional_data.employment_detail.employment_company ? additional_data.employment_detail.employment_company : '',
            employer: (additional_data && additional_data.employment_detail && additional_data.employment_detail.employer),
            job_role: additional_data && additional_data.employment_detail && additional_data.employment_detail.job_role,
            inndex_id: log.user_id,
            in: log.clock_in ? momentTz.unix(+log.clock_in).tz(timezone).format('HH:mm:ss') : '',
            out: log.clock_out ? momentTz.unix(+log.clock_out).tz(timezone).format('HH:mm:ss') : '',
            total_break: total_break ? showDuration(total_break) : '-',
            total_break_sec: total_break,
            total: log.duration_in_sec ?  +dayjs.duration(+log.duration_in_sec,'seconds').asHours().toFixed(2) : '-',
            total_sec: log.duration_in_sec,
            total_wo_break: total_wo_break ? showDuration(total_wo_break) : '-',
            total_wo_break_sec: total_wo_break,
            travel_time: (travel_time_minutes ? +dayjs.duration(travel_time_minutes,'minutes').asHours().toFixed(2) : travel_time_minutes),
            distance_traveled,
            travel_method: travelTimeOverride.travel_method || '-',
            total_with_travel_time: total_duration_instance ? +dayjs.duration(total_duration_instance.asMilliseconds()).asHours().toFixed(2) : '',
            total_with_travel_time_sec: total_duration_instance? total_duration_instance: 0,
            has_SMSTS_or_SSSTS: reference_induction_record.has_SMSTS_or_SSSTS ? 'Yes' : '-',
            is_first_adider: reference_induction_record.is_first_adider ? 'Yes' : '-',
            has_fire_marshal: reference_induction_record.has_fire_marshal ? 'Yes' : '-',
            pts_number: reference_induction_record.has_pts ? reference_induction_record.has_pts.doc_number : '-',
            cscs_number: reference_induction_record.has_cscs ? reference_induction_record.has_cscs.doc_number : '-',
            adjusted_min: (log.adjustment > 0 ? '+' : '') + (log.adjustment || ''),
            adjusted_min_comment: (log.comments && log.comments.length) ? log.comments.filter(c => c.origin === 'admin' && c.module === 'members').map(c => c.note).join(' ') : '',
            // temperature: log.clock_in_temperature ? log.clock_in_temperature + ' °C' : null,
            raw_clock_in_declarations: (log.clock_in_declarations) ? log.clock_in_declarations : null,
            manual_time_comments: (log.manual_time_comments) ? log.manual_time_comments : null,
            raw_clock_out_declarations: (log.clock_out_declarations) ? log.clock_out_declarations : null,
            clock_out_comment: (log.clock_out_comment) ? log.clock_out_comment : null,
            day_start: dayOfYr.startOf('day').valueOf(),
            day_timestamp: +log.clock_in,
            is_user: true,
            district: reference_induction_record.district,
            local_worker: (projectDistricts.length && reference_induction_record.district && projectDistricts.includes(reference_induction_record.district)) ? 'Yes' : 'No',
        }
    });

    return {
        records,
        total: {
            total_break_time,
            total_time,
            total_travel_time,
            total_travel_distance,
            total_time_wo_break,
            total_time_included_duration,
            total_has_SMSTS_or_SSSTS,
            total_is_first_adider,
            total_has_fire_marshal,
            total_has_pts,
            total_has_cscs,
            total_adjustment_minutes,
        }
    };
};

const processVisitorLogWithInfo = (project, logs, visitors, {
    total_break_time,
    total_time,
    total_travel_time,
    total_travel_distance,
    total_time_wo_break,
    total_time_included_duration,
    total_has_SMSTS_or_SSSTS,
    total_is_first_adider,
    total_has_fire_marshal,
    total_has_pts,
    total_has_cscs,
    total_adjustment_minutes,
}, timezone = 'UTC') => {
    let visitor_info = visitors.reduce((list, v) => {
        if (!list[v.id]) {
            v.company = v.employer_ref && v.employer_ref.name;
            v.job_role = v.job_role_ref && v.job_role_ref.name;
            list[v.id] = v;
        }

        return list;
    }, {});


    let records = logs.map(log => {
        let visitor = visitor_info[log.visitor_id] || {};
        let dayOfYr = moment(log.day_of_yr, dbDateFormat);
        let adjusted_sec = (+log.adjustment || 0) * 60;

        let total_duration_instance = getTotalTime((log.duration_in_sec ? (+log.duration_in_sec + adjusted_sec) : 0), visitor.travel_time, true);
        total_time = (total_time + (log.duration_in_sec ? (+log.duration_in_sec + adjusted_sec) : 0));
        total_time_included_duration = total_duration_instance ? total_time_included_duration.add(total_duration_instance).add(adjusted_sec, 's') : total_time_included_duration;

        let travel_time_minutes = getTotalTravelDuration(visitor.travel_time);
        total_travel_time = (total_travel_time + (travel_time_minutes ? (+travel_time_minutes) : 0));

        let distance_traveled = ((visitor.travel_time && +visitor.travel_time.distance_traveled) ? (+visitor.travel_time.distance_traveled / 1000).toFixed(2) : null);
        total_travel_distance = (total_travel_distance + (distance_traveled ? (+distance_traveled) : 0));
        return {
            date: dayOfYr.format(displayDateFormat_D_MMM_Y),
            project_number: (project && project.id) ? project.project_number : '',
            project_name: (project && project.name) ? project.name : '',
            first_name: visitor.first_name,
            last_name: visitor.last_name,
            name: visitor.name,
            company: visitor.company,
            employer: visitor.company,
            job_role: visitor.job_role,
            inndex_id: `visitor:${visitor.id}`,
            in: log.clock_in ? momentTz.unix(+log.clock_in).tz(timezone).format('HH:mm:ss') : '',
            out: log.clock_out ? momentTz.unix(+log.clock_out).tz(timezone).format('HH:mm:ss') : '',
            total: log.duration_in_sec ? +dayjs.duration(+log.duration_in_sec + adjusted_sec,'seconds').asHours().toFixed(2): '-',
            total_sec: log.duration_in_sec,
            travel_time: (travel_time_minutes ? +dayjs.duration(travel_time_minutes,'minutes').asHours().toFixed(2) : travel_time_minutes),
            distance_traveled: distance_traveled ? +(distance_traveled) :null,
            total_with_travel_time: total_duration_instance ? +dayjs.duration(total_duration_instance.asMilliseconds()).asHours().toFixed(2) : '',
            total_with_travel_time_sec: total_duration_instance ? total_duration_instance.asSeconds() : 0,
            // temperature: log.clock_in_temperature ? log.clock_in_temperature + ' °C' : null,
            day_start: dayOfYr.startOf('day').valueOf(),
            day_timestamp: +log.clock_in,
            is_user: false,
        };
    });

    return {
        records,
        total: {
            total_break_time,
            total_time,
            total_travel_time,
            total_travel_distance,
            total_time_wo_break,
            total_time_included_duration,
            total_has_SMSTS_or_SSSTS,
            total_is_first_adider,
            total_has_fire_marshal,
            total_has_pts,
            total_has_cscs,
            total_adjustment_minutes,
        }
    };
};

const totalTimeRecordsOfProjectForAllSources = async (project, payload, timezone, createTotalsRow, attach_timelog = false) => {
    let all_operatives = (payload.all_operatives || false);
    let include_breaks = (payload.include_breaks || false);
    sails.log.info(`export total time report of project: ${project.id}, users: ${payload.users}, visitors: ${payload.visitors}, all operatives?:`, all_operatives);

    let from_date = moment(payload.from_date || '--', dbDateFormat);
    let to_date = moment(payload.to_date || '--', dbDateFormat);
    if (
        !from_date.isValid() ||
        !to_date.isValid() ||
        (payload.source && !VALID_SOURCES[payload.source]) ||
        ((payload.visitors || []).findIndex(id => isNaN(+id)) > -1) ||
        ((payload.users || []).findIndex(id => isNaN(+id)) > -1)
    ) {
        sails.log.info('Invalid Request');
        return ResponseService.errorObject('Invalid filter provided', {from_date, to_date, payload});
    }
    from_date = from_date.format(dbDateFormat);
    to_date = to_date.add(1, 'days').format(dbDateFormat);

    let daily_logs = [];
    if(all_operatives || (payload.users && payload.users.length)){
        daily_logs = await getDailyTimeEventV2(project.id, from_date, to_date, (payload.users || []), false, null, true, true, false, include_breaks);

        /*

        // Disabling attaching these keys for better performance:
        // `clock_in_declarations`  => `raw_clock_in_declarations`   => "Declarations"
        // `clock_out_declarations` => `raw_clock_out_declarations`  =>   not being used anywhere

        // Enabling this key for the IDX-1630 requirement
        // `manual_time_comments`   => `manual_time_comments`        => "Notes"
        */

        if (attach_timelog && daily_logs.length) {
            let location_based_events = (daily_logs || []).reduce((geo_fence_events, row) => {
                for (let i = 0; i < (row.events || []).length; i++) {
                    let event = (row.events || [])[i];
                    if (event.event_date_time && event.source === VALID_SOURCES.GEO_FENCE) {
                        let e = {
                            event_date_time: +event.event_date_time,
                            user_ref: row.user_id,
                        };
                        if(event.id){
                            e = {
                                id: event.id,
                            };
                        }
                        geo_fence_events.push(e);
                    }
                }
                return geo_fence_events;
            }, []);

            let geo_fence_events = await sails.models.usertimelog_reader.find({
                where: {
                    event_type: [EVENT_TYPE.IN, EVENT_TYPE.OUT],
                    project_ref: +project.id,
                    extras: {'!=': {}},
                    ...(location_based_events.length > 1 ? {or: location_based_events} : location_based_events.pop())
                },
                select: ['extras', 'event_date_time', 'user_ref']
            });

            sails.log.info(`Total user badge event as per daily_log: ${geo_fence_events.length}`);

            if(geo_fence_events.length) {
                daily_logs = daily_logs.map(log => {
                    // let clock_in_declarations = [];
                    // let clock_out_declarations = [];
                    let manual_time_comments = [];
                    for (let i = 0; i < (log.events || []).length; i++) {
                        let log_event = (log.events || [])[i];
                        if (log_event.event_date_time && log_event.source === VALID_SOURCES.GEO_FENCE) {
                            let badge_event_row = (geo_fence_events.find(event => (
                                event.event_date_time === log_event.event_date_time &&
                                event.user_ref === log.user_id
                            ))) || {};
                            if (badge_event_row.extras) {
                                // if (log_event.event_type == EVENT_TYPE.IN) {
                                //     if (badge_event_row.extras.declarations) {
                                //         clock_in_declarations.push(...badge_event_row.extras.declarations);
                                //     }
                                // }

                                // if (log_event.event_type == EVENT_TYPE.OUT) {
                                //     if (badge_event_row.extras.declarations) {
                                //         clock_out_declarations.push(...badge_event_row.extras.declarations);
                                //     }
                                // }

                                if (badge_event_row.extras.comment) {
                                    badge_event_row.extras.comment.event_type = (log_event.event_type == EVENT_TYPE.IN) ? EVENT_TYPE.IN : EVENT_TYPE.OUT;
                                    manual_time_comments.push(badge_event_row.extras.comment);
                                }
                            }
                        }
                    }

                    // log.clock_in_declarations = (clock_in_declarations.length) ? clock_in_declarations : null;
                    // log.clock_out_declarations = (clock_out_declarations.length) ? clock_in_declarations : null;
                    log.manual_time_comments = (manual_time_comments.length) ? manual_time_comments : null;

                    return log;
                });
            }
        }
        sails.log.info(`Total user logs for xls: ${daily_logs.length}`);
    }

    let visitor_logs = [];
    if(all_operatives || (payload.visitors && payload.visitors.length)){
        visitor_logs = await getVisitorsTimeLogForDates(project.id, from_date, to_date, (payload.visitors || []), false, null, true);
        sails.log.info(`Total visitor logs for xls: ${visitor_logs.length}`);
    }
    let processed_logs = [];
    let final_total = {
        total_break_time: 0,
        total_time: 0,
        total_travel_time: 0,
        total_travel_distance: 0,
        total_time_wo_break: 0,
        total_time_included_duration: moment.duration(),
        total_has_SMSTS_or_SSSTS: {},
        total_is_first_adider: {},
        total_has_fire_marshal: {},
        total_has_pts: {},
        total_has_cscs: {},
        total_adjustment_minutes: 0,
    };

    if (daily_logs.length) {
        let userIdVisited = _uniq(daily_logs.map(l => l.user_id));
        sails.log.info(`Got time logs of users: ${userIdVisited.join(',')}`);

        let induction_requests = await sails.models.inductionrequest_reader.find({
            select: ['id', 'record_id', 'user_ref', 'additional_data', 'travel_time', 'travel_method', 'user_doc_ids', 'district'],
            where: {
                project_ref: project.id,
                user_ref: userIdVisited,
            },
        });
        induction_requests = await expandUserDocs(induction_requests);
        /*
        daily_logs = await expandDailyLogInfo(project.id, daily_logs, true);

        if(payload.source){
            sails.log.info('filtering user records for source:', payload.source);
            daily_logs = daily_logs.filter(l => l.source === VALID_SOURCES[payload.source]);
        }
        */
        let {records, total} = await processUserLogsWithInductions(project, daily_logs, induction_requests, final_total, timezone, include_breaks);
        processed_logs = records;
        final_total = total;
    }

    if(visitor_logs.length){
        let uniqueVisitors = _uniq(visitor_logs.map(l => l.visitor_id));
        sails.log.info(`Got time logs of visitors: ${uniqueVisitors.join(',')}`);
        let visitors = await sails.models.visitor_reader.find({
            where: {id: uniqueVisitors},
        });

        visitors = await populateEmployerRefs(visitors, 'employer_ref', ['id', 'name', 'company_initial', 'country_code']);
        visitors = await populateJobRoles(visitors, 'job_role_ref');

        /*
        visitor_logs = await expandDailyLogInfo(project.id, visitor_logs, true, false, true);
        if(payload.source){
            sails.log.info('filtering visitor records for source:', payload.source);
            visitor_logs = visitor_logs.filter(l => l.source === VALID_SOURCES[payload.source]);
        }
        */
        let {records, total} = processVisitorLogWithInfo(project, visitor_logs, visitors, final_total, timezone);
        processed_logs = processed_logs.concat(records);
        final_total = total;
    }

    if(processed_logs.length){
        let sortByOrder = createTotalsRow ? ['desc', 'desc'] : ['asc', 'asc'];
        processed_logs = _orderBy(processed_logs, ['day_start', 'day_timestamp'], sortByOrder);

        if(createTotalsRow){
            processed_logs.push({
                'out': 'Total:',
                'total_break': showDurationAsHours(final_total.total_break_time),
                'total': showDurationAsHours(final_total.total_time),
                'total_wo_break': showDurationAsHours(final_total.total_time_wo_break),
                'travel_time': `${final_total.total_travel_time} minutes`,
                'distance_traveled': final_total.total_travel_distance ? `${(+final_total.total_travel_distance).toFixed(2)} Km` : null,
                'total_with_travel_time': showDurationAsHours(final_total.total_time_included_duration.asSeconds()),
                'has_SMSTS_or_SSSTS': Object.keys(final_total.total_has_SMSTS_or_SSSTS).length.toString(),
                'is_first_adider': Object.keys(final_total.total_is_first_adider).length.toString(),
                'has_fire_marshal': Object.keys(final_total.total_has_fire_marshal).length.toString(),
                // 'pts_number': Object.keys(final_total.total_has_pts).length.toString(),
                // 'cscs_number': Object.keys(final_total.total_has_cscs).length.toString(),
                // 'adjusted_min': (final_total.total_adjustment_minutes > 0 ? '+' : '') + (final_total.total_adjustment_minutes || ''),
                is_user: true, // By passing cell merge,
            });
        }
    }

    sails.log.info(`Total logs for export: ${processed_logs.length}`);
    return processed_logs;
};

module.exports = {
    // processLogBatch,
    // storeProcessedLogs,
    EVENT_TYPE,
    determineEventType: (readerName) => {
        let event_type = readerName; //EVENT_TYPE.UNKNOWN;
        let reader = String(readerName).toLowerCase().trim();
        if (reader.endsWith(READER_SUFFIX.ENTRANCE)) {
            event_type = EVENT_TYPE.IN;
        } else if (reader.endsWith(READER_SUFFIX.EXIT)) {
            event_type = EVENT_TYPE.OUT;
        } else if (reader.endsWith(READER_SUFFIX.INTERACTION)) {
            event_type = EVENT_TYPE.INTERACTION;
        }
        return event_type;
    },

    ingestTimeLogs,
    getShiftConfigOfUser,
    // groupRequestDataSet,
    VALID_SOURCES,
    IN_TIME_TYPE,
    buildDurations,
    generate_total_sec,
    // addProjectAndUser, // @deprecated
    reIndexTimeLogs,
    reActOnShiftConfigChange,
    expandBadgeEventWithUserInfo,
    expandDailyLogInfo,
    onlyValidOptimaLogs,
    generateFakeTimeLog,
    //user_time_logs_set
    //badge_event,

    notifyOnHighTemperatureEntry,
    totalTimeRecordsOfProjectForAllSources,
};

