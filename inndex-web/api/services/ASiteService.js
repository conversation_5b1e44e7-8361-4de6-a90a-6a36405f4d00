const { XMLParser } = require("fast-xml-parser");
const FormData = require('form-data');

const {
    COMPANY_SETTING_KEY: {A_SITE_INTEGRATION_CONFIG},
    PROJECT_SETTING_KEY: {A_SITE_PROJECT_CONFIG},
} = sails.config.constants;

const {
    errorObject,
} = require('./ResponseService');

const {
    getCacheOrReDerive
} = require('./CacheService');

const {
    makeGET,
    makePOST,
    fetchUrlAs,
    typeOf,
} = require('./HttpService');

const ASITE_API_CACHE =  'db:asite-api-cache';

const checkIfAsiteEnabled = async (project_id) => {
    let project_settings = await sails.models.projectsetting_reader.findOne({
        where:  {
            name: A_SITE_PROJECT_CONFIG,
            project_ref: project_id
        }
    });

    if(!project_settings || !project_settings.value) {
        sails.log.info(`[getAsiteProjectToolMapping] asite folder mapping unavailable.`);
        return false;
    }

    let {enabled} = project_settings.value;
    return enabled;
}

const getAsiteProjectToolMapping = async (project_id, tool_key, user_employer_id = 0) => {
    let project_settings = await sails.models.projectsetting_reader.findOne({
        where:  {
            name: A_SITE_PROJECT_CONFIG,
            project_ref: project_id
        }
    });

    if(!project_settings || !project_settings.value) {
        sails.log.info(`[getAsiteProjectToolMapping] asite folder mapping unavailable.`);
        return {};
    }

    let {enabled, workspace_id, tools_mapping} = project_settings.value;

    if(!enabled || !workspace_id || !tools_mapping.length) {
        sails.log.info(`[getAsiteProjectToolMapping] Invalid asite folder mapping found or config is disabled.`);
        return {};
    }

    let matched_tools = (tools_mapping || []).filter(tools => tools.tool_key == tool_key);

    let general_tool = (matched_tools || []).find(tools => tools.company_id == 0);
    if(!general_tool) {
        sails.log.info(`[getAsiteProjectToolMapping] Tool '${tool_key}' not mapped to any asite folder.`);
    }

    let company_tagged_tool = (matched_tools || []).find(tools => tools.company_id == user_employer_id);
    if(!company_tagged_tool) {
        sails.log.info(`[getAsiteProjectToolMapping] Tool '${tool_key}' with Company '${user_employer_id}' not mapped. Skipping company check.`);
    }

    if(!company_tagged_tool && !general_tool) {
        sails.log.info(`[getAsiteProjectToolMapping] Error - Tool '${tool_key}' with Company '${user_employer_id}' not mapped to any asite folder. Aborting.`);
        return {};
    }

    return {workspace_id, matched_tool: (company_tagged_tool || general_tool)};
}

const getCompanyASiteConfig = async (company_id) => {
    let company_settings = await sails.models.companysetting_reader.findOne({
        where: {
            company_ref: company_id,
            name: [A_SITE_INTEGRATION_CONFIG],
        }
    });

    if(!company_settings || !company_settings.value) {
        sails.log.info(`[getCompanyASiteConfig] No site config found.`);
        return {};
    }
    return company_settings.value;
}

const loginToASite = async (emailId, password) => {
    let loginFormData = new URLSearchParams({emailId, password});
    let loginResponse = await makePOST(`https://dms.asite.com/apilogin/`, loginFormData, {
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
    });
    sails.log.info('[loginToASite] asite response from api login call', loginResponse.status);

    if(!loginResponse.data || !loginResponse.data.UserProfile) {
        sails.log.info('[loginToASite] asite login failed with response', loginResponse);
    }

    return loginResponse.data;
};

const listWorkspaces = async (company_id) => {

    if(!company_id) {
        sails.log.info(`[listWorkspaces] Param company_id is required to list asite workspaces.`);
        return errorObject('Param company_id is required to list asite workspaces.');
    }
    sails.log.info(`[listWorkspaces] Looking for company asite settings of company: ${company_id}`);

    let asite_config = await getCompanyASiteConfig(company_id);

    if(!asite_config || !asite_config.enabled) {
        sails.log.info(`[listWorkspaces] asite integration is disabled.`);
        return errorObject('No asite config found OR integration might be disabled.');
    }

    sails.log.info(`[listWorkspaces] Retriving or Refreshing workspace list cache.`);
    let { workspaceList } = await retrieveAsiteUserAndWorkspaces(company_id, asite_config);

    if(!workspaceList) {
        sails.log.info(`[listWorkspaces] WorkspaceList not found`);
        return [];
    }

    let workspaceOptions = [];

    for (let count = 0; count < workspaceList.length; count++) {
        let workspace = workspaceList[count];
        const foldersUri = workspace.URI.find(uri => uri.includes('/folders'));

        workspaceOptions.push({
            Workspace_Name: workspace.Workspace_Name,
            Workspace_Id_Str: workspace.Workspace_Id,
            Workspace_Id: (workspace.Workspace_Id.match(/^[^$]+/) || []).shift(),
            Folders_Uri: foldersUri
        });
    }

    return workspaceOptions;
};

const listWorkspaceFolders = async (company_id, workspace_id) => {

    if(!company_id || !workspace_id) {
        sails.log.info(`[listWorkspaceFolders] Params company_id and workspace_id are required to list asite workspace folders.`);
        return errorObject('Params company_id and workspace_id are required to list asite workspaces.');
    }
    sails.log.info(`[listWorkspaceFolders] Looking for company asite settings of company: ${company_id}`);

    let asite_config = await getCompanyASiteConfig(company_id);

    if(!asite_config || !asite_config.enabled) {
        sails.log.info(`[listWorkspaceFolders] No site config found OR integration might be disabled.`);
        return errorObject('No site config found OR integration might be disabled.');
    }

    sails.log.info(`[listWorkspaceFolders] Retriving or Refreshing workspacelist and loggedin asite user cache.`);
    let { asite_user, workspaceList } = await retrieveAsiteUserAndWorkspaces(company_id, asite_config);

    if(!workspaceList && !asite_user) {
        sails.log.info(`[listWorkspaceFolders] WorkspaceList not found`);
        return {};
    }

    /**
     * Workspace Folder List API returns a list of folders within a Workspace.
     * Extract the URI tag for Workspace Folders from Workspace List API to execute Workspace Folder List API Service.
     */

    let workspace = (workspaceList || []).find(ws => ws.Workspace_Id.includes(workspace_id));
    const foldersUri = workspace.URI.find(uri => uri.includes('/folders'));

    let folders = await makeGET(foldersUri, '', {
        'Cookie': `ASessionID=${asite_user.Sessionid }`
    });
    sails.log.info('[listWorkspaceFolders] asite response from folders api call', folders.status);

    if(folders.error) {
        sails.log.error(folders.message);
        return { folders: [] };
    }

    if(folders.data != '' && folders.data.includes('Unauthorized Access')) {
        sails.log.info('[listWorkspaceFolders] asite session invalidated.');
        sails.log.error('[listWorkspaceFolders] Unauthorized Access - asite session invalidated.');
        return false;
    }

    const parser = new XMLParser();
    let jObj = parser.parse(folders.data);

    return { folders: jObj.asiteDataList.folderVO };
};

const uploadDocOnAsite = async (company_id, workspace_id, folder_id, pathData) => {

    if(!company_id || !workspace_id) {
        sails.log.info(`[uploadDocOnAsite] Params company_id and workspace_id are required to list asite workspace folders.`);
        return errorObject('Params company_id and workspace_id are required to list asite workspaces.');
    }
    sails.log.info(`[uploadDocOnAsite] Looking for company asite settings of company: ${company_id}`);

    let asite_config = await getCompanyASiteConfig(company_id);

    if(!asite_config || !asite_config.enabled) {
        sails.log.info(`[uploadDocOnAsite] No site config found OR integration might be disabled.`);
        return errorObject('No site config found OR integration might be disabled.');
    }

    sails.log.info(`[uploadDocOnAsite] Retriving or Refreshing workspacelist and loggedin asite user cache.`);
    let { asite_user, workspaceList } = await retrieveAsiteUserAndWorkspaces(company_id, asite_config);

    if(!workspaceList && !asite_user) {
        sails.log.info(`[uploadDocOnAsite] WorkspaceList not found`);
        return false;
    }

    let workspace = (workspaceList || []).find(ws => ws.Workspace_Id.includes(workspace_id));
    const foldersUri = workspace.URI.find(uri => uri.includes('/folders'));

    let folders = await makeGET(foldersUri, '', {
        'Cookie': `ASessionID=${asite_user.Sessionid}`
    });
    sails.log.info('[uploadDocOnAsite] asite response from api call', folders.status);

    if(folders.error) {
        sails.log.error(folders.message);
        return false;
    }

    if(folders.data != '' && folders.data.includes('Unauthorized Access')) {
        sails.log.info('[uploadDocOnAsite] asite session invalidated.');
        sails.log.error('[uploadDocOnAsite] Unauthorized Access - asite session invalidated.');
        return false;
    }

    const parser = new XMLParser();
    let jObj = parser.parse(folders.data);
    let foldersList = flattenFolders(jObj.asiteDataList.folderVO);
    let folderObj = foldersList.find(f => f.FolderID.includes(folder_id));

    const uploadUri = folderObj.URI.find(uri => uri.includes('/upload'));
    // const uploadUri = folderObj.URI.find(uri => uri.includes('/uploadFileWithCustomAttributes'));

    let streamResponse = await fetchUrlAs(pathData.location, 'stream');

    // Upload code to upload doc with /uploadFileWithCustomAttributes API.

    // form.append('primaryFile', streamResponse.data, {
    //     filename: pathData.name,//'DailyActivity-Test-2024-06-07.pdf',
    //     contentType: 'application/octet-stream'
    // });
    // form.append('uploadFileDetails', JSON.stringify({
    //     "documentRef": "wq",
    //     "revision": "0",
    //     "documentTitle": "manual upload",
    //     "purposeOfIssueName": "S0 - Initial Status",
    //     "statusName": "---",
    //     "revisionNotes": "revision note of file",
    //     "customAttributes": [
    //         { "attributeName": "Originator", "attributeValues": ["AMK"] },
    //         { "attributeName": "Project", "attributeValues": ["0473"] },
    //         { "attributeName": "Location", "attributeValues": ["SW"] },
    //         { "attributeName": "Level", "attributeValues": ["02"] },
    //         { "attributeName": "File Type", "attributeValues": ["CER"] },
    //         { "attributeName": "Discipline", "attributeValues": ["A"] },
    //         { "attributeName": "Number", "attributeValues": [`${timestamp}`] },
    //         { "attributeName": "Deliverable Reference", "attributeValues": ["A0100"] },
    //         { "attributeName": "Project Stage", "attributeValues": ["2"] },
    //         { "attributeName": "LOD", "attributeValues": ["LOD200"] },
    //         { "attributeName": "IDP Mask Key", "attributeValues": [`AMK-0473-SW-02-CER-A-${timestamp}-A0100-2-LOD200`]}
    //     ]
    // }), {
    //     contentType: 'application/json'
    // });

    let timestamp = Date.now();
    let allowRevision = false;

    const form = new FormData();
    form.append('upload', streamResponse.data, {
        filename: `${(allowRevision) ? timestamp : ''}${pathData.name}`,
        contentType: 'application/octet-stream'
    });

    form.append('txtDocRef', pathData.name);
    form.append('revision', '0');
    form.append('documentTitle', `${pathData.name}`);
    // form.append('purposeOfIssue', 'S0 - Initial Status (WIP)'); //Need to confim if this needs to be configurable from project setup.

    // As discussed with Suraj Balra from Sweet via email, This is the requested default status for all uploaded documents. Agreed to keep these values enabled for all asite workspaces for Sweet.
    form.append('purposeOfIssue', 'S2 - Suitable for Information');
    form.append('statusName', 'Published');
    form.append('publish_as_private', 'false');
    // form.append('revisionNotes', 'First floor drawing');

    let uploadRes = await makePOST(uploadUri, form, {
        'Content-Type': 'multipart/form-data; charset=UTF-8',
        'Cookie': `ASessionID=${asite_user.Sessionid}`,
        ...form.getHeaders()
    });

    if(uploadRes.status === 200) {
        sails.log.info('[uploadDocOnAsite] Uploaded doc to asite successfuly.', uploadRes.data);
        return true;
    } else {
        sails.log.info('[uploadDocOnAsite] Error occured while uploading doc to asite.', uploadRes.data);
        return false;
    }

};

const flattenFolders = (folder) => {
    const flatArray = [];

    const traverseFolders = (currentFolder) => {
        const { folderVO, ...rest } = currentFolder;
        flatArray.push({ ...rest, folderVO: [] });

        if (folderVO && typeOf(folderVO, 'array') && folderVO.length > 0) {
            folderVO.forEach(subFolder => traverseFolders(subFolder));
        } else if (folderVO && typeOf(folderVO, 'object') && folderVO.FolderID != '') {
            traverseFolders(folderVO)
        }
    };

    traverseFolders(folder);

    return flatArray;
};

const getWorkspaceList = async (asite_user) => {
    // Extract the session id from login API response and URI tag for Workspace Folders from Workspace List API.
    const workspaceListUri = asite_user.URI.find(uri => uri.includes('workspace/workspacelist'));

    if(workspaceListUri) {
        let wk = await makeGET(workspaceListUri, '', {
            'Accept': 'application/json',
            'Cookie': `ASessionID=${asite_user.Sessionid}`
        });
        sails.log.info('[getWorkspaceList] asite response from api call', wk.status);

        if(wk.error) {
            sails.log.error(wk.message);
            return [];
        }
        return wk.data.asiteDataList.workspaceVO;
    }
    return [];
};

const retrieveAsiteUserAndWorkspaces = async (company_id, asite_config) => {
    const whereClause = { company_ref: company_id };

    sails.log.info('[retrieveAsiteUserAndWorkspaces] Trying to fetch asite data cache for, ', whereClause);
    return await getCacheOrReDerive(ASITE_API_CACHE, whereClause, async () => {
        sails.log.info('[retrieveAsiteUserAndWorkspaces] Regenerating asite data cache for, ', whereClause);

        const { email, password } = asite_config;
        const user_profile = await loginToASite(email, password);
        const workspaceList = await getWorkspaceList(user_profile.UserProfile);

        return { asite_user: user_profile.UserProfile,  workspaceList }
    }, 0.1); // Setting timeToLiveHour -> 0.1 hrs = 6 mins.
};

module.exports = {
    loginToASite,
    listWorkspaces,
    listWorkspaceFolders,
    getCompanyASiteConfig,
    getAsiteProjectToolMapping,
    uploadDocOnAsite,
    retrieveAsiteUserAndWorkspaces,
    checkIfAsiteEnabled,
};
