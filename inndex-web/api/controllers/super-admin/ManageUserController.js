const {
    UserRevisionService: {createUserRevision},
    EmailService: {getSuppressionStatus, removeFromSuppressionList},
    ResponseService: {successResponse, errorResponse, sendResponse},
    DataProcessingService: {expandUserDocFiles, populateDocumentChildren, attachUserDocument, populateProjectRefs},
    OptimaSyncService: {deleteBadge, getOptimaAccessGroup, updateBadge,},
    RekognitoService: {indexInductionUserFaceOrError,triggerDeleteFaceFromCollection,},
} = require('./../../services');
const dayjs = require("dayjs");
const _pick = require('lodash/pick');
const moment = require("moment");

const updateUserProfile = async (user_ref, payload) => {
    sails.log.info(`[ADMIN] update user info ${user_ref}, payload:`, payload);
    let updated = await sails.models.user.updateOne({id: user_ref}).set(payload);

    createUserRevision(user_ref, {personal: updated}).catch(sails.log.error);
    return updated;
};

module.exports = {
    getUserById: async (req, res) => {
        let user_ref = +req.param('userId', 0);
        let extraColumns = req.param('extra', '').split(',');

        sails.log.info(`[ADMIN] ID: ${req.user.id}, get user ${user_ref}, extra: ${extraColumns}`);
        let select = [
            'id', 'email', 'first_name', 'last_name',
            'profile_pic_ref', 'country_code', 'is_active', 'last_active_on',
            'user_onboard_status', 'timezone', 'parent_company'
        ];
        if(extraColumns.includes('mail')){
            select.push(...['email_verified_on']);
        }
        if(extraColumns.includes('reset')){
            select.push(...['reset_count', 'reset_password_expires', 'reset_password_token']);
        }
        let user = await sails.models.user_reader.findOne({
            where: {id: user_ref},
            select: select
        });

        if (extraColumns.includes('pic') && user.profile_pic_ref) {
            let pic = await sails.models.userfile_reader.findOne({
                where: {id: +user.profile_pic_ref},
                select: ['id', 'file_url', 'sm_url']
            });
            user.profile_pic_ref = pic || user.profile_pic_ref;
        }

        return successResponse(res, {user});
    },

    updateUserInfo: async (req, res) => {
        const user_ref = +req.param('userId', 0);
        const action = req.param('action', '-');
        const requestBodyUser = ((req.body && req.body.user) || {});
        sails.log.info(`[ADMIN] ID: ${req.user.id}, update user ${user_ref}, action: ${action}`);

        if (action === 'updateProfile') {
            let payload = _pick(requestBodyUser, [
                'first_name',
                'middle_name',
                'last_name',
                'country_code',
                'nin',
                'user_onboard_status',

                'profile_pic_ref',
                'profile_pic_expiry',
                'timezone',
                'parent_company',
            ]);

            let updated = await updateUserProfile(user_ref, payload);
            return successResponse(res, {user: updated, message: 'User profile info updated successfully'});
        } else if (action === 'updateStatus') {

            let payload = _pick(requestBodyUser, [
                'is_active',
            ]);
            let updated = await updateUserProfile(user_ref, payload);
            return successResponse(res, {user: updated, message: 'User profile status updated successfully'});
        } else if (action === 'resetEmailStatus') {

            let updated = await updateUserProfile(user_ref, {
                reset_count: 1,
                reset_password_expires: null,
                reset_password_token: null
            });
            return successResponse(res, {user: updated, message: 'Cleared previous reset password attempts successfully.'});
        }

        return errorResponse(res, 'Invalid request');
    },

    getUserInductionsAndDocuments: async (req, res) => {
        let user_ref = +req.param('userId', 0);
        let extraColumns = req.param('extra', '').split(',');

        sails.log.info(`[ADMIN] get user inductions, user: ${user_ref}`);

        let inductions = await sails.models.inductionrequest_reader.find({
            where: {user_ref},
            select: ['id', 'project_ref', 'record_id', 'creator_name', 'status_code', 'fr_face_id', 'user_doc_ids'],
            sort: ['id']
        });
        inductions = await populateProjectRefs(inductions, 'project_ref', ['id', 'name', 'project_initial', 'use_prefix']);

        sails.log.info(`[ADMIN] get user documents, user: ${user_ref}`);

        let document_filter = {
            doc_owner_id: user_ref,
            parent_doc_ref: null, // get only parent docs
            is_deleted_manually : { '!=': 1 },
            expiry_date: { '>': moment().valueOf() },
        };

        let user_docs = await sails.models.userdoc_reader.find(document_filter).sort('id');
        user_docs = await populateDocumentChildren(user_docs, document_filter);

        if(extraColumns.includes('doc.files')){
            user_docs = await (expandUserDocFiles(user_docs, (expandErr, expandedList) =>  expandedList || user_docs));
        }

        sails.log.info(`[ADMIN] got user inductions and documents, user: ${user_ref}, inductions: ${inductions.length}, docs: ${user_docs.length}`);
        return successResponse(res, {inductions, user_docs});
    },

    associateDocWithInductions: async (req, res) => {
        let user_ref = +req.param('userId', 0);
        let {induction_ids, user_doc_ids, notify, comment} = _pick((req.body || {}), [
            'induction_ids',
            'user_doc_ids',
            'notify',
            'comment'
        ]);
        induction_ids = induction_ids.filter(s => s !== '' && !isNaN(+s)).map(s => +s);
        user_doc_ids = user_doc_ids.filter(s => s !== '' && !isNaN(+s)).map(s => +s);
        sails.log.info(`[ADMIN] for user: ${user_ref}, associate docs: (${user_doc_ids}) with inductions: (${induction_ids}) notify: ${notify}`);

        let induction_requests = await sails.models.inductionrequest_reader.find({
            select: ['project_ref', 'user_ref', 'status_code', 'additional_data', 'record_id', 'comments', 'user_doc_ids'],
            where: {
                id: induction_ids,
                user_ref,
            }
        });

        for (let i = 0; i < induction_requests.length; i++) {
            let record = induction_requests[i];
            let comments = (record.comments || []);
            if (comment) {
                comment.extra = {
                    ...(comment.extra || {}),
                    from: record.user_doc_ids,
                    to: user_doc_ids,
                };
                comments.push(comment);
            }
            sails.log.info(`[ADMIN] Update induction-request: ${record.id}, project_ref: ${record.project_ref} old_doc_ids: ${record.user_doc_ids}`, comment);
            let updated_induction = await sails.models.inductionrequest.updateOne({id: record.id})
                .set({
                    comments: comments,
                    user_doc_ids: user_doc_ids
                });
            let project = await sails.models.project_reader.findOne({
                where: {
                    id: record.project_ref
                },
                select: ['id', 'parent_company', 'custom_field']
            });
            let country_code = (project.custom_field && project.custom_field.country_code);
            let user_info = (record.additional_data || {}).user_info || {};
            await attachUserDocument(updated_induction, {
                user_ref: user_ref,
                last_name: user_info.last_name,
                country_code: country_code,
                parent_company: project.parent_company
            }, true, false, true, false);

        }
        return successResponse(res, {done: true});
    },

    associateFileAsProfilePic: async (req, res) => {
        let file_ref = +req.param('file_ref', 0);
        let user_ref = +req.param('userId', 0);
        let change_file_ownership = req.param('change_ownership', true).toString() === 'true';

        sails.log.info(`[ADMIN] Update profile of user ${user_ref} with ${file_ref}, change_file_ownership: ${change_file_ownership}`);
        if(change_file_ownership){
            let file_exists = await sails.models.userfile.updateOne({id: file_ref}).set({
                user_id: user_ref,
            });
            if(!file_exists || !file_exists.id){
                sails.log.info(`No file record found, id: ${file_ref}`);
                return errorResponse(res, `File record not found, Skipping updating profile.`);
            }
            sails.log.info(`[ADMIN] Updated ownership of file:${file_ref} with user_id: ${user_ref}`);

        }

        let user = await sails.models.user.updateOne({ id: user_ref }).set({
            profile_pic_ref: file_ref,
            profile_pic_expiry: dayjs().add(2, 'year').valueOf()
        });
        sails.log.info(`[ADMIN] User profile pic has been updated`);

        return successResponse(res, {message:`User profile pic has been updated`, user});
    },

    updateInductionUserProfilePic: async (req, res) => {
        let induction_ids = req.param('induction_ids');
        let userId = +req.param('userId');

        sails.log.info(`[ADMIN] Update user profile of user ${userId} for inductions ${induction_ids}`);

        let induction_requests = await sails.models.inductionrequest_reader.find({
            select: ['id', 'fr_face_id', 'project_ref', 'user_ref'],
            where: {
                id: { in: induction_ids },
                user_ref: userId
            }
        });

        if(!induction_requests || !induction_requests.length) {
            sails.log.info(`No inductions found to update user ${userId} profile pic.`)
            return errorResponse(res, `No inductions found to update user ${userId} profile pic.`);
        }

        let user = await sails.models.user_reader.findOne({
            select: ['profile_pic_ref'],
            where: {
                id: userId,
                profile_pic_ref: {'!=': null},
            }
        }).populate('profile_pic_ref');

        if (!user || !user.profile_pic_ref || !user.profile_pic_ref.id) {
            sails.log.info(`Profile pic is not set for the user ${userId}.`)
            return errorResponse(res, `Profile pic is not set for the user ${userId}.`);
        }

        for (let i = 0; i < induction_requests.length; i++) {
            let ir = induction_requests[i];
            let fr_face_id = ir.fr_face_id;
            if(ir.fr_face_id){
                sails.log.info(`[FR]: induction record ${ir.id} already having faceId: ${ir.fr_face_id}`);
                let {
                    success: deleteSuccess,
                    data: deleteData
                } = await triggerDeleteFaceFromCollection(ir.project_ref, [ir.fr_face_id]);
                if (!deleteSuccess) {
                    sails.log.warn(`[FR]: Failed while deleting induction: ${ir.id} faceId: ${ir.fr_face_id}`);
                    return errorResponse(res, `Failed while removing existing enrolment from induction: ${ir.id}`, deleteData);
                }

                // re-enrolling
                ir.additional_data = {user_info: user}; // passing new pic for enrolment call.
                let indexingResult = await indexInductionUserFaceOrError(ir.project_ref, ir);
                if (indexingResult.error) {
                    sails.log.info(`[ADMIN] Error from enrolment call of induction: ${ir.id}, error`, indexingResult);
                    return errorResponse(res, `FR enrolment call for induction:${ir.id} failed`, indexingResult);
                }
                fr_face_id = indexingResult.fr_face_id;
            }

            sails.log.info(`[ADMIN] Updating user profile pic in induction ${ir.id}, updated fr_face_id: ${fr_face_id}`);
            let result = await sails.sendNativeQuery(`UPDATE
                 induction_request
                 SET
                 additional_data =
                     jsonb_set(
                         jsonb (additional_data),
                         '{user_info, profile_pic_ref}',
                         to_jsonb($1::json),
                         true
                 ),
                 fr_face_id = $3
                 WHERE id = $2`,
                [user.profile_pic_ref, ir.id, fr_face_id]
            );

        }

        sails.log.info(`[ADMIN] User profile pic has been updated in inductions ${induction_ids} for user ${userId}.`)
        return successResponse(res, {message:`User profile pic has been updated into induction (ID: ${induction_ids})`});
    },

    deleteInductionById: async (req, res) => {
        let inductionId = +req.param('inductionId', 0);
        let userId = +req.param('userId', 0);

        sails.log.info(`[ADMIN] Delete induction request for induction ${inductionId}, of user ${userId}`);
        if (!inductionId || !userId) {
            sails.log.info(`invalid request for delete induction, user ${userId} id: ${inductionId}`)
            return errorResponse(res, `Invalid request for delete induction`);
        }
        let induction_request = await sails.models.inductionrequest_reader.findOne({
            select: ['id', 'project_ref', 'fr_face_id', 'optima_badge_number'],
            where: {
                id: inductionId,
                user_ref: userId
            }
        });

        if (!induction_request || !induction_request.id) {
            sails.log.info(`[ADMIN] induction NOT found to delete, user ${userId} id: ${inductionId}`)
            return errorResponse(res, `No induction record found for provided details`);
        }
        if (induction_request.fr_face_id) {
            // delete face entry from collection
            let {success, data} = await triggerDeleteFaceFromCollection(induction_request.project_ref, [induction_request.fr_face_id]);
            if (!success) {
                return errorResponse(res, `Failed while deleting enrolled faces from project`, data);
            }
        }
        if (induction_request.optima_badge_number) {
            sails.log.info(`[ADMIN] induction ${inductionId} have optima badge number: ${induction_request.optima_badge_number}, trying to delete it as well.`)
            const optima_setting = await sails.models.optimasetting_reader.findOne({
                where: {project_ref: induction_request.project_ref, key: {'!=': null}},
            });
            if (optima_setting && optima_setting.site_id) {
                let {success: badgeDeleted} = await deleteBadge(optima_setting, induction_request.optima_badge_number);
            }
        }
        sails.log.info('[ADMIN] deleting existing medication detail records');
        let medications = await sails.models.longmedicationdetail.destroy({induction_ref: induction_request.id});
        sails.log.info('[ADMIN] Deleted medications count', medications ? medications.length : null);

        let [deleted] = await sails.models.inductionrequest.destroy({
            id: inductionId,
            user_ref: userId,
        });
        // @todo: Add ALS entry.
        sails.log.info(`[ADMIN] Deleted induction`, JSON.stringify({medications, deleted}));
        sails.log.info(`[ADMIN] Deleted induction id ${inductionId} for user ${userId} from project ${deleted.project_ref}`);
        return successResponse(res, {message: `Deleted induction record # ${deleted.record_id} from project ${deleted.project_ref}`});
    },

    wipeAllBadgesFromInductions: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        sails.log.info(`[ADMIN] Wipe all existing badge # from inductions of project ${projectId} requested by ${req.user.id}`);

        let induction_requests = await sails.models.inductionrequest_reader.find({
            select: ['id', 'optima_badge_number'],
            where: {project_ref: projectId, optima_badge_number: {'!=': null}}
        });

        sails.log.info(`[ADMIN] Existing induction vs badge info`, JSON.stringify(induction_requests));

        let updated_inductions = await sails.models.inductionrequest.update({project_ref: projectId}).set({optima_badge_number: null});

        sails.log.info(`[ADMIN] Updated inductions count`, updated_inductions.length);
        return successResponse(res, {message:`Records updated successfully`, count: updated_inductions.length});
    },

    syncBlackListedBadgesToOptima: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        sails.log.info(`[ADMIN] Sync Project block / Company blacklisted inductions of project ${projectId} requested by ${req.user.id}`);

        let deny_group = await getOptimaAccessGroup(req.project_optima_setting);
        if (deny_group.error || !deny_group.id) {
            sails.log.info(`[ADMIN] Deny access groups of ${projectId} NOT FOUND.`);
            return sendResponse(res, deny_group);
        }
        let induction_requests = await sails.models.inductionrequest_reader.find({
            select: ['id', 'record_id', 'optima_badge_number'],
            where: {
                project_ref: projectId,
                optima_badge_number: {'!=': null},
                status_code: [4, 5],    // Company Block or Project Block
            }
        });
        sails.log.info(`[ADMIN] site_id: ${req.project_optima_setting.site_id} Total induction to sync: ${induction_requests.length}, deny group`);
        if(induction_requests.length > 100){
            sails.log.info(`[ADMIN] site_id: ${req.project_optima_setting.site_id} Too many induction requests to sync, skipping further processing.`);
            return errorResponse(res, `Too many induction requests to sync, these needs to synced manually via #data-change-request.`);
        }
        const extra_mgs = (induction_requests.length > 50) ? ' Please avoid re-syncing again until next 15 minutes' : '';
        successResponse(res, {
            message: `Total ${induction_requests.length} will be synced, depending on number of badges it may take some time.${extra_mgs}`,
        });
        for (let i = 0; i < induction_requests.length; i++) {
            const ir = induction_requests[i];
            sails.log.info(`[ADMIN] site_id: ${req.project_optima_setting.site_id} Updating induction: ${ir.id}, badge: ${ir.optima_badge_number} to deny group`);
            let updated = await updateBadge(req.project_optima_setting, ir.optima_badge_number, {
                "groupId": deny_group.id,
            });
            if (!updated.success) {
                sails.log.info(`[ADMIN] Failed while updating badge "${ir.optima_badge_number}" to deny group for record_id: ${ir.record_id}. error`, updated);
                break;
            }
        }
    },

    wipeAllFacesFromInductions: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        sails.log.info(`[ADMIN] Wipe all existing FR faces # from inductions of project ${projectId} requested by ${req.user.id}`);
        let induction_requests = await sails.models.inductionrequest_reader.find({
            select: ['id', 'fr_face_id'],
            where: {project_ref: projectId, fr_face_id: {'!=': null}}
        });
        if (!induction_requests.length) {
            return successResponse(res, {message:`Records updated successfully`, count: 0});
        }
        sails.log.info(`[ADMIN] Existing induction vs faceId info`, JSON.stringify(induction_requests));

        let {success, data} = await triggerDeleteFaceFromCollection(projectId, induction_requests.map(ir => ir.fr_face_id));
        if (!success) {
            return errorResponse(res, `Failed while deleting enrolled faces from project`, data);
        }
        let updated_inductions = await sails.models.inductionrequest.update({project_ref: projectId, id: (induction_requests.map(ir => ir.id))}).set({fr_face_id: null});

        sails.log.info(`[ADMIN] Updated inductions count`, updated_inductions.length);
        return successResponse(res, {message:`Records updated successfully`, count: updated_inductions.length});
    },

    autoEnrolInductionsIntoFR: async (req, res) => {
        let projectId = req.param('projectId');
        let project_has_fr = !!(req.project_optima_setting && req.project_optima_setting.id && req.project_optima_setting.has_fr);
        sails.log.info('[ADMIN] Enrol approved inductions for FR, project:', projectId, `has_fr: ${project_has_fr}`);
        if(!project_has_fr){
            return errorResponse(res, sails.__('facial_recognition_not_enabled'));
        }

        let inductions = await sails.models.inductionrequest_reader.find({
            select: ['id', 'record_id', 'status_code', 'user_ref', 'additional_data'],
            where: {
                fr_face_id: null,
                status_code: [2, 6], // only `accepted` Or `In review`
                project_ref: projectId
            },
            sort: ['id asc'],
        });

        sails.log.info('[ADMIN] total inductions to enrol for FR are:', inductions.length);
        successResponse(res, {count: inductions.length});

        let processed = [];
        for (let i = 0, len = inductions.length; i < len; i++) {
            const ir = inductions[i];
            sails.log.info('[ADMIN] processing induction id:', ir.id);
            sails.log.info(`[ADMIN] [FR]: Project : ${projectId} enrolling induction: ${ir.id}`);
            let indexingResult = await indexInductionUserFaceOrError(projectId, ir);
            if (indexingResult.error) {
                sails.log.info('[ADMIN] Error from indexing call', indexingResult);
                sails.log.error(`[ADMIN] FR enrolment call at ID:${ir.id} failed, Skipping...`);
                continue;
            }
            sails.log.info(`[ADMIN] [FR]: induction: ${ir.id} enrolled with faceId: ${indexingResult.fr_face_id}`);
            let updated = await sails.models.inductionrequest.update({id: ir.id}).set({fr_face_id: indexingResult.fr_face_id});

            processed.push({
                id: ir.id,
                fr_face_id: indexingResult.fr_face_id,
            });
        }
        sails.log.info('[ADMIN] Total face enrolled', inductions.length, 'processed', JSON.stringify(processed));
    },

    removeMailSuppressionStatus: async (req, res) => {
        let address = (req.body.address || '');
        if (!address || !address.length) {
            return errorResponse(res, 'Invalid request');
        }
        sails.log.info('checking email suppression status');
        let {not_found, message, SuppressedDestination} = await getSuppressionStatus(address);
        if (not_found) {
            sails.log.info('email suppression entry NOT FOUND for:', address);
            return successResponse(res, {not_found, message});
        }
        sails.log.info('Removing email suppression status for:', address);
        let result = await removeFromSuppressionList(address);
        sails.log.info('email suppression outcome', result);
        return successResponse(res, {...result, info: SuppressedDestination});
    },
};
