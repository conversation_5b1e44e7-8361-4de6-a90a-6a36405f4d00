const moment = require('moment');
const {DEFAULT_PAGE_SIZE} = sails.config.constants;
const INTERNAL_SERVER_ERROR = sails.__('internal server error');
const {getLatestUserRevision} = require('./../services/UserRevisionService');
const {PdfUtil, TokenUtil: { replaceBrToNl }} = require('./../services');
const momentTz = require('moment-timezone');
const { successResponse, errorResponse } = require('./../services/ResponseService');
const _uniq = require('lodash/uniq');

const {
    DataProcessingService: {
        sendMailToNominatedManagerCPA,
        getUserFullName,
        getProjectTimezone,
        populateUserRefs,
        populateProjectRefs,
        shareReportViaEmail,
        getPdfHeaderTemplate,
        getHourlyWeatherLogs,
    },
    TokenUtil: {
        allProjectAdminsByOneOfDesignations,
        filterProjectUsersEmailEligibility,
        getCompanyInfo,
        resourceIdentifier,
        ROLES
    },
    ExcelService: {
        streamExcelDownload,
        getDownloadDailyReportWorkbook,
        getWorkforceHoursReportWorkbook,
        getWorkforceHoursComparisonReportWorkbook,
        getActivityHoursBreakdownWorkbook,
    },
    TranslationService: {
        celsiusToFahrenheit,
    },
    SharedService: {
        tempDirPath,
        downloadPdfViaGenerator,
        s3UploaderWithExpiry,
    },
    ASiteService : { checkIfAsiteEnabled, getAsiteProjectToolMapping, uploadDocOnAsite }
} = require('./../services');
const {
    inductionFn: {
        getUserInductionEmployer,
        getInductionUserEmployers
    },
} = require('../sql.fn');
const _difference = _.difference;
const getValues = (dataArr = [], source) =>  {
    let filteredArr = [];
    if (dataArr.length) {
        dataArr.filter(function(obj) {
            if (obj.source == source) {
                filteredArr.push(obj.comments);
            }
        });
    }
    return filteredArr;
};
const WeatherIcon = {
    'arrow': 'arrow-up.svg', // Used for wind arrow
    'raindrops': 'wi-raindrops.svg', // Used for wind arrow
    '1': 'wi-day-sunny.svg', // Sunny
    '2': 'wi-day-sunny.svg', // Mostly Sunny
    '3': 'wi-day-sunny-overcast.svg', // Partly Sunny
    '4': 'wi-day-cloudy.svg', // Intermittent Clouds
    '5': 'wi-day-haze.svg', // Hazy Sunshine
    '6': 'wi-day-cloudy-high.svg', // Mostly Cloudy
    '7': 'wi-cloudy.svg', // Cloudy
    '8': 'wi-cloudy.svg', // Dreary (Overcast)
    '11': 'wi-fog.svg', // Fog
    '12': 'wi-showers.svg', // Showers
    '13': 'wi-day-showers.svg', // Mostly Cloudy w/ Showers
    '14': 'wi-day-showers.svg', // Partly Sunny w/ Showers
    '15': 'wi-thunderstorm.svg', // T-Storms
    '16': 'wi-day-storm-showers.svg', // Mostly Cloudy w/ T-Storms
    '17': 'wi-day-storm-showers.svg', // Partly Sunny w/ T-Storms
    '18': 'wi-rain-mix.svg', // Rain
    '19': 'wi-cloudy-gusts.svg', // Flurries
    '20': 'wi-day-cloudy-gusts.svg', // Mostly Cloudy w/ Flurries
    '21': 'wi-day-cloudy-gusts.svg', // Partly Sunny w/ Flurries
    '22': 'wi-snow.svg', // Snow
    '23': 'wi-day-snow.svg', // Mostly Cloudy w/ Snow
    '24': 'wi-day-snow-thunderstorm.svg', // Ice
    '25': 'wi-sleet.svg', // Sleet
    '26': 'wi-rain-mix-f.svg', // Freezing Rain
    '29': 'wi-snow-wind.svg', // Rain and Snow
    '30': 'wi-hot.svg', // Hot
    '31': 'wi-snowflake-cold.svg', // Cold
    '32': 'wi-windy.svg', // Windy
    '33': 'wi-night-clear.svg', // Clear
    '34': 'wi-night-cloudy.svg', // Mostly Clear
    '35': 'wi-night-alt-partly-cloudy.svg', // Partly Cloudy
    '36': 'wi-night-alt-partly-cloudy.svg', // Intermittent Clouds
    '37': 'wi-night-clear.svg', // Hazy Moonlight
    '38': 'wi-night-alt-cloudy.svg', // Mostly Cloudy
    '39': 'wi-night-showers.svg', // Partly Cloudy w/ Showers
    '40': 'wi-night-alt-showers.svg', // Mostly Cloudy w/ Showers
    '41': 'wi-night-thunderstorm.svg', // Partly Cloudy w/ T-Storms
    '42': 'wi-night-alt-thunderstorm.svg', // Mostly Cloudy w/ T-Storms
    '43': 'wi-night-snow.svg', // Mostly Cloudy w/ Flurries
    '44': 'wi-night-alt-snow.svg', // Mostly Cloudy w/ Snow
};

const expandDailyActivitiesPhotos = async (projectdailyactivities) => {
    let allPhotoIds = [];
    projectdailyactivities.map(pda => {
        if (pda.photos && Object.keys(pda.photos).length) {
            let idsArr = Object.values(pda.photos);
            //sails.log.info('Photos for id, ', pda.id);

            //flatten an array of arrays and unique
            allPhotoIds.push(...new Set([].concat.apply([], idsArr)));
        }
    });
    //sails.log.info('Fetching images of ids', allPhotoIds);

    let allPhotos = await sails.models.userfile.find({
        where: {id: allPhotoIds},
        select: ['id', 'file_url', 'sm_url', 'file_mime']
    });
    sails.log.info('Fetched photos, ', allPhotos.length);

    projectdailyactivities.map(pda => {
        if (pda.photos && Object.keys(pda.photos).length) {
            for (let key in pda.photos) {
                pda.photos[key] = pda.photos[key].map(id => {
                    return allPhotos.find(photo => photo.id === id);
                });
            }
        }
        return pda;
    });

    return projectdailyactivities;
};

const cleanDailyActivity = (projectDailyActivity) => {
    if (projectDailyActivity.photos && Object.keys(projectDailyActivity.photos).length) {
        for (let key in projectDailyActivity.photos) {
            projectDailyActivity.photos[key] = projectDailyActivity.photos[key].map(photo => {
                return (photo && photo.id) ? photo.id : photo;
            });
        }
    }
    return projectDailyActivity;
};

const cleanPhotos = (pp_images) => {
    return (pp_images || []).map(photo => {
        return (photo && photo.id) ? photo.id : photo;
    });
};

const getObjectId = (object) => {
    return (object && object.id) ? object.id : object;
};

const createProgressPhotosByDailyActivity = async (dailyActivity, oldData = {}) => {
    let progressPhotos = [];
    for (let i = 0, len = dailyActivity.activities.length; i < len; i++) {
        let activity = dailyActivity.activities[i], key = activity.key;
        sails.log.info('Processing activity: ', activity);
        //Filter newly added activities in daily activity record.
        let oldActivity = (oldData.activities || []).find(a => a.pp_id == activity.pp_id);
        if (dailyActivity.photos[key] && dailyActivity.photos[key].length && !activity.pp_id){
            sails.log.info('Preparing progress photo for current activity.');
            // Populating progress photos data as per activity data.
            let photoData = {
                pp_title: activity.title,
                pp_location: dailyActivity.activities_location[key],
                pp_description: dailyActivity.activities_description[key],
                user_ref: (dailyActivity.user_ref ? dailyActivity.user_ref : oldData.user_ref),
                user_revision_ref: (dailyActivity.user_revision_ref ? dailyActivity.user_revision_ref : oldData.user_revision_ref),
                project_ref: (dailyActivity.project_ref ? getObjectId(dailyActivity.project_ref) : getObjectId(oldData.project_ref)),
                pp_images: cleanPhotos(dailyActivity.photos[key]),
                has_daily_activity: true
            };

            let progressPhoto = await sails.models.progressphotos.create(photoData);
            dailyActivity.activities[i].pp_id = progressPhoto.id;

        } else if (activity.pp_id && oldData && ((oldActivity && activity.title != oldActivity.title) ||
            (oldData.photos && _difference(dailyActivity.photos[key], oldData.photos[key]).length) ||
            (oldData.activities_location && dailyActivity.activities_location[key] != oldData.activities_location[key]) ||
            (oldData.activities_description && dailyActivity.activities_description[key] != oldData.activities_description[key]))
        ) {
            let progressPhoto = await sails.models.progressphotos.updateOne({
                id: dailyActivity.activities[i].pp_id
            }).set({
                pp_title: activity.title,
                pp_location: dailyActivity.activities_location[key],
                location: (dailyActivity.locations_tag[key] && dailyActivity.locations_tag[key].lat && dailyActivity.locations_tag[key].long) ? dailyActivity.locations_tag[key] : {},
                pp_description: dailyActivity.activities_description[key],
                pp_images: cleanPhotos(dailyActivity.photos[key])
            });
            sails.log.info('Updated progressphoto with daily activity data', dailyActivity);
        }
    }
    return dailyActivity;
};

const getAvailableUserRef = async (dailyactivity) => {
    if (dailyactivity.user_revision_ref) {
        dailyactivity.user_ref = dailyactivity.user_revision_ref.personal;
    } else {
        dailyactivity.user_ref = await sails.models.user_reader.findOne({
            id: dailyactivity.user_ref
        });
    }

    return { dailyactivity_updated: dailyactivity };
}

const updateComments = (items) => {
    return items.map(item => ({
      ...item,
      comments: item.comments ? replaceBrToNl(item.comments) : item.comments
    }));
  };

const replaceBrWithNlInComments = (dailyActivity) =>{
    if(dailyActivity.comment){
        dailyActivity.comment = replaceBrToNl(dailyActivity.comment);
    }

    if(dailyActivity.activities_description) {
        dailyActivity.activities_description = Object.fromEntries(
            Object.entries(dailyActivity.activities_description).map(([key, value]) => [
                key,
                replaceBrToNl(value)
            ])
        );
    }

    if(dailyActivity.delay_or_additional) {
        dailyActivity.delay_or_additional = updateComments(dailyActivity.delay_or_additional);
    }
    if(dailyActivity.instructions) {
        dailyActivity.instructions = updateComments(dailyActivity.instructions);
    }

    return dailyActivity;
}

const getEmployerFromProfileIfNull = async (userEmployer, user_ref) => {
    if(userEmployer && (userEmployer.name || userEmployer.id)) {
        return userEmployer;
    }

    let userProfileEmployer = await sails.models.userempdetail_reader.findOne({
        where: {user_ref: user_ref},
        select: ['employer']
    });

    return {...userProfileEmployer, name: userProfileEmployer && userProfileEmployer.employer};
};

const processViewDownloadActivity =  async (req, res, dailyactivity, type, imageQuality='original', responseType='', localsObj = null) => {
        res.locals = localsObj || res.locals;
        let { dailyactivity_updated } = await getAvailableUserRef(dailyactivity);
        dailyactivity = dailyactivity_updated;

        sails.log.info('got record, id', dailyactivity ? dailyactivity.id : undefined);
        let totalPages = 1;
        let editedByEmployerInfo = null, userEmployerInfo = null;
        let showEditedBy = false;

        let operativesRef = (dailyactivity.operatives || []).reduce((arr, operative) => {
            if (operative && operative.user_ref) {
                arr.push(operative.user_ref);
            }
            return arr;
        }, []);

        let usersToFetchEmpFor = _uniq([
            dailyactivity.user_ref.id,
            dailyactivity.edited_by && dailyactivity.edited_by.id,
            ...operativesRef,
        ]);

        // Fetch user's employer from induction data for user_rer, edited_by and operatives.
        let userEmpFromInduction = await getInductionUserEmployers(dailyactivity.project_ref.id, {statusCodes: [6, 2, 4, 5], userIds: usersToFetchEmpFor}, false);

        if (operativesRef.length) {
            dailyactivity.operatives = (dailyactivity.operatives || []).map(operative => {
                if (operative && operative.user_ref) {
                    let empInfo = (userEmpFromInduction || []).find(emp => emp.user_ref == operative.user_ref);
                    console.log(empInfo);
                    if (empInfo && empInfo.name) {
                        operative.employer = empInfo.name;
                    }
                }
                return operative;
            });
        }

        userEmployerInfo = (userEmpFromInduction || []).find(emp => emp.user_ref == dailyactivity.user_ref.id) || {};
        userEmployerInfo = await getEmployerFromProfileIfNull(userEmployerInfo, dailyactivity.user_ref.id);

        if(dailyactivity.edited_by && dailyactivity.edited_by.id) {
            if(dailyactivity.edited_by.id !== dailyactivity.user_ref.id) {
                showEditedBy = true;
            }
            editedByEmployerInfo = (userEmpFromInduction || []).find(emp => emp.user_ref == dailyactivity.edited_by.id) || {};
            editedByEmployerInfo = await getEmployerFromProfileIfNull(editedByEmployerInfo, dailyactivity.edited_by.id);
        }

        let bbClass = '';
        if (!dailyactivity.operatives.length && !dailyactivity.plants.length && !dailyactivity.materials.length) {
            bbClass = 'bb-required';
        }
        let photosLength = Object.keys(dailyactivity.photos).length;
        if (dailyactivity.photos && photosLength) {
            sails.log.info('Expanding daily activity photos.');
            let photoPages = 0;
            for (key in dailyactivity.photos) {
                let photo = (dailyactivity.photos[key] || []);
                //3 photos per row
                photoPages += Math.ceil(photo.length/3);
                let imgUrlArr = [];
                for (let i = 0, len = photo.length; i < len; i++) {
                    try {
                        let fileInfo = await sails.models.userfile.findOne({
                            where: {id: photo[i]},
                            select: ['sm_url', 'md_url', 'file_url']
                        });
                        if (fileInfo && fileInfo.id) {
                            if (type === 'pdf' || ['small','medium'].includes(imageQuality)) {
                                imgUrlArr.push(fileInfo.sm_url || fileInfo.md_url || fileInfo.file_url);
                            } else {
                                imgUrlArr.push(fileInfo.file_url);
                            }
                        } else {
                            sails.log.info('Image not found for ID.', photo[i]);
                        }
                    } catch (e) {
                        dailyactivity.photos = [];
                        sails.log.info('Failed to expand daily activity photos.');
                    }
                }
                if (imgUrlArr.length) {
                    dailyactivity.photos[key] = imgUrlArr;
                }
            };
            //4 photo rows per page
            totalPages += Math.ceil(photoPages / 3);
        }
        //sails.log.info('Expanded photos ', dailyactivity.photos);

        // let activitiesKey = (dailyactivity.activities || []).reduce((arr,activity) => {
        //     arr.push(activity.key);
        //     return arr;
        // }, []);

        let weatherLog = [];
        if(dailyactivity.weather_logs.length) {
            weatherLog = await sails.models.weatherlog_reader.find({id: dailyactivity.weather_logs});
        }

        //check plants field to overcome from page overlapping
        if (dailyactivity.plants.length && (dailyactivity.operatives.length + dailyactivity.plants.length + dailyactivity.materials.length) > 18) {
            totalPages += Math.ceil(dailyactivity.activities.length/5);
        }

        if (dailyactivity.operatives.length > 33) {
            totalPages += Math.ceil(dailyactivity.activities.length/5);
        }

        let { project_logo_file, companyName } = await getCompanyInfo(dailyactivity.project_ref);
        /*If there will be 5 activities than can fit in one table and another table will be added below with activities 6-10 and so on*/
        let activitiesCount = dailyactivity.activities.length;
        let activitiesPage = 1;
        if (activitiesCount > 5) {
            activitiesPage = Math.ceil(activitiesCount / 5);
        }

        dailyactivity.clientComments = await getValues(dailyactivity.instructions, 'client');
        dailyactivity.subcontractorComments = await getValues(dailyactivity.instructions, 'subcontractor');
        dailyactivity.delayComments = await getValues(dailyactivity.delay_or_additional, 'delay');
        dailyactivity.additionalWorksComments = await getValues(dailyactivity.delay_or_additional, 'additional');
        dailyactivity.additionalComments = await getValues(dailyactivity.delay_or_additional, 'additional_comment');
        let showAdditionalFields = false;
        if (Object.keys(dailyactivity.activities_description).length || dailyactivity.clientComments.length || dailyactivity.subcontractorComments.length || dailyactivity.delayComments.length || dailyactivity.additionalWorksComments.length) {
            showAdditionalFields = true;
        }
        if (weatherLog.length && activitiesCount > 3) {
            totalPages += 1;
        }
        let tz = getProjectTimezone(dailyactivity.project_ref);
        if (dailyactivity.shift_date) {
            dailyactivity.shift_date = momentTz(dailyactivity.shift_date).format(res.locals.__('displayDateFormat_dash_DD_MM_YYYY'));
        }

        let classIndex = 0;
        (dailyactivity.activities || []).map((activity, index) => {
            activity.color_class = `activeColor${classIndex}`;
            classIndex = (classIndex === 4) ? 0 : classIndex + 1;
            return activity;
        });

        let project = dailyactivity.project_ref;
        let daPhrase = project.custom_field ? project.custom_field.da_phrase_singlr : 'Daily Activity';
        // intentionally made them constant - https://inndex.atlassian.net/browse/IDX-1468?focusedCommentId=15804
        let wppPhrase = 'WPP/Method';
        let tbPhrase = 'Task Briefing';
        let permitPhrase = 'Permit';

        totalPages += activitiesPage;
        sails.log.info('Total Pages', totalPages);
        let form_template = `pages/daily-activities-form-page`;
        let html = await sails.renderView(form_template, {
            title: daPhrase,
            project,
            type,
            companyName,
            daPhrase,
            dailyactivity,
            weatherLog,
            totalPages,
            dateFormat: res.locals.__('displayDateFormat_slash_DD_MM_YYYY'),
            displayDateFormat_dash_DD_MM_YYYY: res.locals.__('displayDateFormat_dash_DD_MM_YYYY'),
            displayFullDateFormat_slash_DD_MM_YYYY_HH_mm_ss: res.locals.__('displayFullDateFormat_slash_DD_MM_YYYY_HH_mm_ss'),
            moment,
            project_logo_file,
            layout: false,
            activitiesPage,
            activitiesCount,
            showAdditionalFields,
            userEmployerInfo,
            bbClass,
            editedByEmployerInfo,
            showEditedBy,
            wppPhrase,
            tbPhrase,
            permitPhrase,
            _getLocaleOf(key){
                return res.locals.__(key);
            },
            showTemperature(val) {
                let temperature_unit = res.locals.__('temperature_unit');
                if(temperature_unit === 'C'){
                    return val;
                }
                return celsiusToFahrenheit(val);
            },
            hasLocationTag(locationTag) {
                return Object.keys(locationTag).length;
            },
            getUserFullName(userInfo) {
                return getUserFullName(userInfo);
            },
            showDurationAsHours: (duration_in_sec) => {
                if(duration_in_sec && !isNaN(+duration_in_sec)){
                    return PdfUtil.showDurationAsHours(duration_in_sec);
                }
                return duration_in_sec;
            },
            unix(n, format) {
                return moment.unix(n).format(format);
            },
            momentT(n, format) {
                return moment(n).format(format);
            },
            getActivityTitle(key, activitiesObj) {
                let o = activitiesObj.find(activity => activity.key == key);
                return o.title;
            },
            momentTz(n, format) {
                return momentTz(+n).tz(tz).format(format);
            },
            weatherIconPath(code = '') {
                return sails.config.custom.PUBLIC_URL + 'images/weatherIcons/' + WeatherIcon[code] || '';
            },
            updateTotal(object, total) {
                if (!object.total) {
                    object.total = 0;
                }
                if (total) {
                    object.total += +total;
                }
                return object;
            },
            toFixed(number) {
                return (+number) ? (+number).toFixed(4) : 0;
            }
        });

        if(type === 'html') {
            return html;
        }

        let username = getUserFullName(dailyactivity.user_ref);
        let file_name = `${daPhrase}-Report-${dailyactivity.shift_date}-${username.replace(/[^a-zA-Z ]/g, "")}-${dailyactivity.createdAt}`;
        let heading_line = `${daPhrase} (#${dailyactivity.record_id})`;
        let project_line = `${(project.project_number != null) ? project.project_number + ' - ' + project.name : project.name} (#${project.id}): ${project.contractor}`;
        let date_line = `Prepared: ${momentTz(+dailyactivity.createdAt).tz(tz).format(res.locals.__('displayFullDateFormat_dash_DD_MM_YYYY_HH_mm_ss'))}`;
        return await downloadPdfViaGenerator({
            req,
            res,
            html,
            tool: 'daily-activity',
            file_name,
            heading_line,
            project_line,
            date_line,
            logo_file: project_logo_file,
            responseType
        });
};

const syncDailyActivityDocToAsite = async (req, projectInfo, daId, localsObj, userEmp = 0) => {
    sails.log.info(`[syncDailyActivityDocToAsite] Starting execution for daily activity ${daId} employer ID ${userEmp}`);

    let {workspace_id, matched_tool} = await getAsiteProjectToolMapping(projectInfo.id, 'daily_activities', userEmp);
    if(!workspace_id || !matched_tool) {
        sails.log.info(`[syncDailyActivityDocToAsite] Aborting execution for daily activity ${daId} workspace_id or matched_tool not found.`);
        return;
    }

    let { employer } = await getCompanyInfo(projectInfo, null, ['id', 'company_initial']);

    let dailyactivity =  await sails.models.projectdailyactivities_reader.findOne({
        id: daId
    }).populate('user_revision_ref');

    dailyactivity = await populateProjectRefs([dailyactivity], 'project_ref', []);
    [dailyactivity] = await populateUserRefs(dailyactivity, 'user_ref', []);

    //PDF generation and uploading to Asite, Can be handled using lambda/eventbridge for faster API response. Need to rethink a better approach.
    sails.log.info(`[syncDailyActivityDocToAsite] preparing PDF for daily activity ${daId}`);
    let response = await processViewDownloadActivity({headers: {'user-agent' : req.headers['user-agent']}}, {}, dailyactivity, 'pdf', 'original', 'path', localsObj);

    sails.log.info(`[syncDailyActivityDocToAsite] PDF prepared, starting asite upload for daily activity ${daId}`);

    //File name format -> {PROJECT/CONTRACT NUMBER} ({INNDEX PROJECT ID NUMBER})-{COMPANY INITIALS}-{TOOL NAME}-{REPORT TYPE}-{REPORT #}
    let toolPhraseForAsite = (projectInfo.custom_field.da_phrase || '').replace(/\s/g, '_');
    response.name = `${projectInfo.project_number} (${projectInfo.id})-${employer.company_initial}-${toolPhraseForAsite}-Shift_Report-${dailyactivity.record_id}.pdf`;

    sails.log.info(`[syncDailyActivityDocToAsite] Filename to be used on asite ${response.name}`);

    await uploadDocOnAsite(employer.id, workspace_id, matched_tool.folder_id, response);
};

const createDailyActivityFn = async (req, res) => {
    sails.log.info('Create daily activity for project, by', req.user.id);

    let createRequest = _.pick((req.body || {}), [
        'project_ref',
        'submitted_by',
        'submitted_datetime',
        'shift_date',
        'shift_time',
        'activities',
        'operatives',
        'plants',
        'materials',
        'photos',
        'work_carried_out',
        'work_on_schedule',
        'comment',
        'progress',
        'wpp',
        'task_briefing_sheets',
        'instructions',
        'delay_or_additional',
        'activities_description',
        'activities_location',
        'locations_tag',
        'version',
        'permit_number'
    ]);

    createRequest.user_ref = req.user.id;

    let revision = await getLatestUserRevision(req.user.id);
    createRequest.user_revision_ref = revision.id;
    createRequest = replaceBrWithNlInComments(createRequest);
    sails.log.info('Staring progress photo creation by daily activity data.');
    //Populating createRequest with progress photo ids.
    createRequest = await createProgressPhotosByDailyActivity(createRequest);
    sails.log.info('Successfully created progress photos.');
    let projectInfo = await sails.models.project.findOne({where: {id: createRequest.project_ref}, select: ['name','project_category','parent_company', 'custom_field', 'weather_location_key', 'postcode']});
    let weatherLogs = await getHourlyWeatherLogs(
        projectInfo.weather_location_key,
        projectInfo.postcode,
        createRequest.shift_date,
        createRequest.shift_time
    );
    createRequest.weather_logs = weatherLogs.map(w=> w.id);
    let projectdailyactivity = await sails.models.projectdailyactivities.create(createRequest);

    if (projectdailyactivity) {

        let project_name = projectInfo.name;
        let daPhrase = projectInfo.custom_field.da_phrase;
        let subject = `${projectInfo.custom_field.da_phrase_singlr} Report - Project: ${project_name}`;
        let added_by = getUserFullName(req.user);
        let projUsrResult = await allProjectAdminsByOneOfDesignations(projectdailyactivity.project_ref, [ 'nominated', 'custom']);
        projUsrResult = filterProjectUsersEmailEligibility(projUsrResult, 'daily_activities');
        sails.log.info('Send email to nom managers, count:', projUsrResult.length);
        for (let j = 0, len = projUsrResult.length; j < len; j++) {
            let nomManager = projUsrResult[j];
            let user_name = getUserFullName(nomManager.user_ref);
            let emailHtml = await sails.renderView('pages/mail/new-daily-activity', {
                title: subject,
                user_name,
                added_by,
                alt_phrase: projectInfo.custom_field.da_phrase_singlr,
                project_name,
                layout: false
            });
            let emailAdd = nomManager.user_ref && nomManager.user_ref.email;
            sails.log.info('Sending mail to', emailAdd);
            await EmailService.sendMail(subject, [emailAdd], emailHtml);
            sails.log.info('Daily Activity notification has been sent');
        }

        //send mail to company project admins(Nominated Managers)
        if (projectInfo.project_category === 'company-project' && projectInfo.parent_company) {
            await sendMailToNominatedManagerCPA(projectInfo.parent_company, projectInfo, req.user, daPhrase, 'submitted '+daPhrase+' Report');
        }

        sails.log.info('Created daily activity successfully.');
        return successResponse(res, projectdailyactivity);
    }

    sails.log.info('Failed to add daily activity');
    return errorResponse(res, sails.__('Failed to add daily activity'));
};

const updateDailyActivityFn = async (req, res) => {
    let id = +req.param('id');
    sails.log.info('update daily activity ', id);

    let updateRequest = _.pick((req.body || {}), [
        'submitted_by',
        'submitted_datetime',
        'shift_date',
        'shift_time',
        'activities',
        'operatives',
        'plants',
        'materials',
        'photos',
        'work_carried_out',
        'work_on_schedule',
        'comment',
        'progress',
        'wpp',
        'task_briefing_sheets',
        'instructions',
        'permit_number',
        'delay_or_additional',
        'activities_description',
        'activities_location',
        'locations_tag',
        'reviewer',
        'project_ref'
    ]);
    sails.log.info('Fetching curent data for daily activity :', id);
    //Used to identify old and newly added activities and get project data.
    let projDailyActivityOld =  await sails.models.projectdailyactivities.findOne({
        where: {id: id},
        select: ['activities','photos','activities_location','locations_tag','activities_description',
            'user_ref','user_revision_ref','project_ref'
        ]
    });

    updateRequest = replaceBrWithNlInComments(updateRequest);
    if(updateRequest.photos) {
        sails.log.info('Staring progress photo creation, Old daily activity records :', projDailyActivityOld);
        updateRequest = await createProgressPhotosByDailyActivity(updateRequest, projDailyActivityOld);
        sails.log.info('Successfully created progress photos.');
    }

    updateRequest.edited_by = req.user.id;
    let projectInfo = await sails.models.project.findOne({where: {id: projDailyActivityOld.project_ref}, select: ['name','weather_location_key', 'postcode', 'contractor', 'project_number', 'custom_field']});
    if(updateRequest.shift_date && updateRequest.shift_time) {
        updateRequest.project_ref = (updateRequest.project_ref && updateRequest.project_ref.id) ? updateRequest.project_ref.id : updateRequest.project_ref;
        let weatherLogs = await getHourlyWeatherLogs(
            projectInfo.weather_location_key,
            projectInfo.postcode,
            updateRequest.shift_date,
            updateRequest.shift_time
        );
        updateRequest.weather_logs = weatherLogs.map(w=> w.id);
        sails.log.info('Updating daily activity for project, by', req.user.id);
    }

    if(updateRequest.reviewer && updateRequest.reviewer.timestamp) {
        updateRequest.signed_off = true;
        sails.log.info('Updating daily activity signed_off, by', req.user.id);
    }
    let projectdailyactivity = await sails.models.projectdailyactivities.updateOne({id: id}).set(cleanDailyActivity(updateRequest));

    let hasAsite = await checkIfAsiteEnabled(projectInfo.id);
    if(updateRequest.reviewer && updateRequest.reviewer.timestamp && hasAsite) {
        sails.log.info(`Daily activity ID ${id} signed off by user ${req.user.id}`);
        let userEmployer = await getUserInductionEmployer({id: projectdailyactivity.user_ref}, projectdailyactivity.project_ref);
        // Check if user's induction employer not there, Load employer from profile data.
        if(!userEmployer) {
            [da] = await populateUserRefs([projDailyActivityOld], 'user_ref', ['parent_company']);
            userEmployer = da.user_ref.parent_company;
        }
        // Not awaiting syncDailyActivityDocToAsite fn, to run the PDF generation and asite upload in backend.
        let localsObj = Object.assign({}, res.locals); // Cloning the res.locals, as the object gets cleared after the API response sent.
        syncDailyActivityDocToAsite(req, projectInfo, projectdailyactivity.id, localsObj, userEmployer).catch(sails.log.error);
    }
    if (projectdailyactivity && projectdailyactivity.id) {
        sails.log.info('Updated daily activity successfully.');
        return successResponse(res, projectdailyactivity);
    }

    sails.log.info('Failed to update daily activity');
    return errorResponse(res, sails.__('Failed to update daily activity'));
}

const getDailyActivityFn = async (req, res) => {
    let id = +req.param('id');
    sails.log.info('Fetch Daily Activity, id:', id);

    if (!id) {
        return errorResponse(res, 'Daily Activity Id is required.');
    }

    let pageNumber = +(req.query.pageNumber || 1);
    let limit = +(req.query.limit || 30);

    if (isNaN(pageNumber) || isNaN(limit)) {
        sails.log.info("Invalid Request for getProjectInspectionTours");
        return errorResponse(
            res,
            "Invalid request required."
        );
    }

    let skip = (pageNumber - 1) * limit;
    try {
        let projectdailyactivity =  await sails.models.projectdailyactivities.findOne({id: id}, skip, limit)
            .populate('project_ref');
        let total =  await sails.models.projectdailyactivities.findOne({id: id})
        console.log({total})
        sails.log.info('got daily activity record, id', projectdailyactivity ? projectdailyactivity.id : undefined);
        return successResponse(res, {projectdailyactivity, skip, limit, total});
    } catch(failure){
        sails.log.info('Failed to find Daily Activity.', failure);
        return errorResponse(res, INTERNAL_SERVER_ERROR, failure);
    }
};

const deleteDailyActivityFn = async (req, res) => {
    let id = +req.param('id');
    sails.log.info('Deleting Daily Activity, id:', id);

    if (!id) {
        return errorResponse(res, 'Daily Activity Id is required.');
    }

    try {
        let deleted =  await sails.models.projectdailyactivities.destroy({id: id});
        sails.log.info('Deleted Daily Activity', deleted ? deleted.length : null);
        return successResponse(res, {message: "Deleted Daily Activity Successfully."});
    } catch(failure){
        sails.log.info('Failed to delete Daily Activity.', failure);
        return errorResponse(res, INTERNAL_SERVER_ERROR, failure);
    }
};

const downloadDailyActivityFn = async (req, res) => {
    let id = +req.param('id');
    let createdAt = +req.body.createdAt;
    let type = req.body.type;
    sails.log.info('Fetch daily activity :', id);

    let whereClause = {id: id, createdAt: createdAt};
    let dailyactivity =  await sails.models.projectdailyactivities.findOne(whereClause)
        .populate('project_ref')
        .populate('user_revision_ref')
        .populate('edited_by');

    if (dailyactivity && dailyactivity.id) {
        let response = await processViewDownloadActivity(req, res, dailyactivity, type);
        if(type === 'html') {
            sails.log.info('Rendering html view');
            return res.send(response);
        }
        //pdf
        return response;
    }
    sails.log.info('Failed to find daily activity');
    return errorResponse(res, sails.__('internal server error'));

};

const downloadDailyActivitiesReportXLSXFn = async (req, res) => {
    let projectId = (req.body.projectId || '');
    let fromDate = moment(req.body.fromDate).startOf('day').valueOf();
    let toDate = moment(req.body.toDate).endOf('day').valueOf();
    let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
    let filter = { project_ref: projectId };

    if (!fromDate || !toDate) {
        return errorResponse(res, 'from date and to date are required.');
    }

    sails.log.info('fetch all daily-activities with filter:', filter, fromDate, toDate, `is_inherited_project: ${is_inherited_project}`);
    let allActivities =  await sails.models.projectdailyactivities.find({
        where: {
            createdAt: {'>=': fromDate, '<=': toDate},
            ...filter
        },
        sort: ['shift_date DESC']
    });
    let workbook = await getDownloadDailyReportWorkbook(allActivities);
    let fileName = `Activity-Report-${projectId}-[${moment(fromDate).format('DD-MM-YYYY')}-${moment(toDate).format('DD-MM-YYYY')}].xlsx`;
    return streamExcelDownload(res, workbook, fileName);
};

module.exports = {
    createDailyActivity: createDailyActivityFn,

    createDailyActivityV2: createDailyActivityFn,

    updateDailyActivity: updateDailyActivityFn,

    updateDailyActivityV2: updateDailyActivityFn,

    getDailyActivity: getDailyActivityFn,

    getDailyActivityV2: getDailyActivityFn,

    getDailyActivities: async (req, res) => {
        let projectId = +req.param('projectId');
        let userId = +req.param('userId');
        let search = req.param('search');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let sortKey = req.param('sortKey' ,'id');
        let sortDir = req.param('sortDir' ,'desc');
        let expandPhotos = req.query.expand_photos;
        sails.log.info('Fetch daily activities by project, projectId:', projectId);

        if (!projectId) {
            return errorResponse(res, 'project Id is required');
        }

        let where = {
            project_ref: projectId
        };

        if (userId) {
            where.user_ref = userId;
        }
        if(search && search !== 'null'){
            search = search.toLowerCase();
            let searchResult =  await sails.models.projectdailyactivities.find({
                where,
                select: ['id', 'activities', 'activities_location','locations_tag', 'user_ref','record_id']
            });
            searchResult = await populateUserRefs(searchResult, 'user_ref', []);

            const temp = searchResult.filter(function (d) {
                let userFullName = `${d.user_ref.first_name ? d.user_ref.first_name : ''}${d.user_ref.last_name ? ' ' + d.user_ref.last_name : ''}`.trim().toLowerCase();
                return (
                    (Object.values(d.activities_location || {}).join(', ').toLowerCase().indexOf(search) !== -1) ||
                    (d.user_ref && d.user_ref.first_name && d.user_ref.first_name.toLowerCase().indexOf(search) !== -1) ||
                    (d.user_ref && d.user_ref.middle_name && d.user_ref.middle_name.toLowerCase().indexOf(search) !== -1) ||
                    (d.user_ref && d.user_ref.last_name && d.user_ref.last_name.toLowerCase().indexOf(search) !== -1) ||
                    (userFullName && userFullName.indexOf(search) !== -1) ||
                    (d.record_id && d.record_id.includes(search)) ||
                    ((d.activities || []).map(e => e.title).join(', ').toLowerCase().indexOf(search) !== -1) || !search
                );
            });
           let ids = [];
           temp.map(search => {
               ids.push(search.id)
           })

           where = {id: ids};
        }

        if (isNaN(pageNumber) || isNaN(pageSize)) {
            sails.log.info('Invalid Request for getProjectInspectionTours');
            return errorResponse(res,'Invalid request required.');
        }

        try {
            let projectdailyactivities =  await sails.models.projectdailyactivities.find(where)
                .limit(pageSize)
                .skip(pageNumber * pageSize)
                .sort([
                    {[sortKey]: sortDir}
                ]);
            projectdailyactivities = await populateProjectRefs(projectdailyactivities, 'project_ref', []);
            projectdailyactivities = await populateUserRefs(projectdailyactivities, 'user_ref', []);

            if (expandPhotos === 'yes') {
                projectdailyactivities =  await expandDailyActivitiesPhotos(projectdailyactivities);
            }
            let total =  await sails.models.projectdailyactivities.count({where})
            sails.log.info('Fetch daily activities.', projectdailyactivities.length);
            return successResponse(res, {projectdailyactivities, total});
        } catch(failure){
            sails.log.info('Failed to fetch daily activities for project.', failure);
            return errorResponse(res, INTERNAL_SERVER_ERROR, failure);
        }
    },

    deleteDailyActivity: deleteDailyActivityFn,

    deleteDailyActivityV2: deleteDailyActivityFn,

    // @todo: vshal: deprecated api, need to remove on 1st Dec
    downloadDailyActivitiesReport: async (req, res) => {
        let id = +req.param('id');
        let updatedAt = +req.param('updatedAt');
        let type = req.param('type') ? req.param('type'): 'pdf';
        sails.log.info('Fetch daily activity :', id);

        let whereClause = {id: id,updatedAt:updatedAt};

        let dailyactivity =  await sails.models.projectdailyactivities.findOne(whereClause)
            .populate('project_ref')
            .populate('user_revision_ref')
            .populate('edited_by');

        if (dailyactivity && dailyactivity.id) {
            let response = await processViewDownloadActivity(req, res, dailyactivity, type);
            if(type === 'html') {
                sails.log.info('Rendering html view');
                return res.send(response);
            }
            //pdf
            return response;
        }
        sails.log.info('Failed to find daily activity');
        return errorResponse(res, sails.__('internal server error'));
    },

    //Single PDF Download
    downloadDailyActivity: downloadDailyActivityFn,

    downloadDailyActivityV2: downloadDailyActivityFn,

    downloadDailyActivitiesReportXLSX: downloadDailyActivitiesReportXLSXFn,

    downloadDailyActivitiesReportXLSXV2: downloadDailyActivitiesReportXLSXFn,

    downloadWorkforceHoursReport: async (req, res) => {
        let projectId = +req.param('projectId');
        let fromDate = moment(req.body.fromDate).startOf('day').valueOf();
        let toDate = moment(req.body.toDate).endOf('day').valueOf();
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        let filter = { project_ref: projectId };

        if (!fromDate || !toDate) {
            return errorResponse(res, 'from date and to date are required.');
        }

        let fromShiftDate = moment(fromDate).format('YYYY-MM-DD');
        let toShiftDate = moment(toDate).format('YYYY-MM-DD');
        let shiftDateFilter = fromDate;
        if (toShiftDate > fromShiftDate) {
            shiftDateFilter = {'>=': fromShiftDate, '<=': toShiftDate};
        };

        sails.log.info('shiftDateFilter: ', shiftDateFilter);

        sails.log.info('fetch all daily-activities with filter:', filter, fromDate, toDate, `is_inherited_project: ${is_inherited_project}`);
        let allActivities =  await sails.models.projectdailyactivities.find({
            select: ['shift_date', 'operatives','createdAt'],
            where: {
                shift_date: shiftDateFilter,
                ...filter
            },
            sort: ['shift_date ASC']
        });
        let workbook = await getWorkforceHoursReportWorkbook(allActivities);
        let fileName = `Workforce-Hours-Report-${projectId}-[${moment(fromDate).format('DD-MM-YYYY')}-${moment(toDate).format('DD-MM-YYYY')}].xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    },

    downloadHoursComparisonReport: async (req, res) => {
        let projectId = +req.param('projectId');
        let fromDate = moment(req.body.fromDate).startOf('day').valueOf();
        let toDate = moment(req.body.toDate).endOf('day').valueOf();
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        let filter = { project_ref: projectId };

        if (!fromDate || !toDate) {
            return errorResponse(res, 'from date and to date are required.');
        }

        let fromShiftDate = moment(fromDate).format('YYYY-MM-DD');
        let toShiftDate = moment(toDate).format('YYYY-MM-DD');
        let shiftDateFilter = fromDate;
        if (toShiftDate > fromShiftDate) {
            shiftDateFilter = {'>=': fromShiftDate, '<=': toShiftDate};
        };

        sails.log.info('shiftDateFilter: ', shiftDateFilter);

        sails.log.info('fetch all daily-activities with filter:', filter, fromDate, toDate, `is_inherited_project: ${is_inherited_project}`);
        let allActivities =  await sails.models.projectdailyactivities.find({
            select: ['shift_date', 'operatives','createdAt'],
            where: {
                shift_date: shiftDateFilter,
                ...filter
            },
            sort: ['shift_date ASC']
        });
        let allTimeLogs = await sails.models.userdailylog.find({
            select: ['day_of_yr', 'total_in_sec', 'user_ref', 'adjustment_minutes'],
            where: {
                day_of_yr: shiftDateFilter,
                project_ref: projectId
            },
            sort: ['day_of_yr ASC']
        });
        let workbook = await getWorkforceHoursComparisonReportWorkbook(allActivities, allTimeLogs);
        let fileName = `Workforce-Actual-vs-Reported-Hours-Report-${projectId}-[${moment(fromDate).format('DD-MM-YYYY')}-${moment(toDate).format('DD-MM-YYYY')}].xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    },

    //Multiple PDFs Download b/w date range
    downloadDailyActivities: async (req, res) => {
        let projectId = (req.body.projectId || '');
        let fromDate = moment(req.body.fromDate).startOf('day').valueOf();
        let toDate = moment(req.body.toDate).endOf('day').valueOf();

        if (!fromDate || !toDate) {
            return errorResponse(res, 'from date and to date are required.');
        }

        sails.log.info('fetch all daily-activities with filter:', fromDate, toDate);
        let allActivities =  await sails.models.projectdailyactivities.find({
            where: {
                createdAt: {'>=': fromDate, '<=': toDate},
                project_ref: projectId
            },
            sort: ['createdAt DESC']
        })
            .populate('user_revision_ref');
        allActivities = await populateProjectRefs(allActivities, 'project_ref', []);
        allActivities = await populateUserRefs(allActivities, 'edited_by', []);
        if (allActivities.length) {
            fromDate = moment(req.body.fromDate).format('DD-MM-YYYY');
            toDate = moment(req.body.toDate).format('DD-MM-YYYY');
            let featureName = 'daily-activity';
            let childFilePath = `${featureName}/json/${moment().valueOf()}`;
            let pdfFilesPath = [];
            let daPhrase = 'Daily Activity';
            for (let index in allActivities) {
                let dailyActivity = allActivities[index];
                daPhrase = dailyActivity.project_ref.custom_field ? dailyActivity.project_ref.custom_field.da_phrase_singlr : 'Daily Activity';
                let filename = `${daPhrase}-Report-${dailyActivity.shift_date}-${getUserFullName(dailyActivity.user_ref)}-${dailyActivity.createdAt}`;
                pdfFilesPath.push(`${filename}.pdf`);
            }

            let outputFileName = `${daPhrase}-Report-[${fromDate}-${toDate}]`;
            let masterFileDir = `${featureName}/pdf/${moment().add(10, 'seconds').valueOf()}`;
            let masterFilePath = `${masterFileDir}/${outputFileName}.json`;
            let mail_style = await sails.renderView('pages/mail-style', {layout: false});
            let mail_footer = await sails.renderView('pages/mail/mail-footer', {layout: false});
            // json data
            let jsonObj = {
                masterFileDir,
                "date_range": `${fromDate} - ${toDate}`,
                "feature_name": featureName,
                "email_subject": `innDex-${outputFileName}`,
                "email_body": `Please click on link to get ${daPhrase} reports from ${fromDate} to ${toDate}: `,
                mail_style,
                mail_footer,
                "sender_email": `innDex${(process.env.APP_ENV !== 'production') ? ` ${process.env.APP_ENV}` : ''} <${sails.config.custom.SOURCE_MAIL_ADDRESS}>`,
                "receiver_email": req.user.email,
                "receiver_id": req.user.id,
                "receiver_name": getUserFullName(req.user),
                "output_file_name": outputFileName,
                pdfFilesPath
            };
            // stringify JSON Object
            let s3UploadResponse = await s3UploaderWithExpiry(masterFilePath, JSON.stringify(jsonObj), 'application/json', 'public-read', 1);
            sails.log.info(`Uploaded file ${s3UploadResponse.key || s3UploadResponse.Key} on s3 in bucket ${s3UploadResponse.Bucket}.`);

            for (let index in allActivities) {
                let dailyActivity = allActivities[index];
                let daPhrase = dailyActivity.project_ref.custom_field ? dailyActivity.project_ref.custom_field.da_phrase_singlr : 'Daily Activity';
                let html = await processViewDownloadActivity(req, res, dailyActivity, 'html', 'small');
                let filename = `${daPhrase}-Report-${dailyActivity.shift_date}-${getUserFullName(dailyActivity.user_ref)}-${dailyActivity.createdAt}`;
                let jsonContent = JSON.stringify({html, featureName, pdfFileName: `${filename}.pdf`, masterFileDir, masterFilePath});
                await s3UploaderWithExpiry(`${childFilePath}/${filename}.json`, jsonContent, 'application/json', 'public-read', 1);
            }

            sails.log.info(`Processed ${allActivities.length} daily activity records.`);
            return successResponse(res, {message: `${allActivities.length} reports will be sent to your email address ${req.user.email}`});
        }

        sails.log.info('No record found between selected duration.');
        return successResponse(res, {message: 'No record found between selected duration.'});
    },

    shareDailyActivityReport: async(req, res) => {
        let id = +req.param('id');
        let email = req.body.email;
        let projectId = req.body.projectId;
        let byUser = req.user;
        sails.log.info('Share Daily Activity Report request by user', byUser.id, ' for project ', projectId);
        let dailyactivity =  await sails.models.projectdailyactivities_reader.findOne({id: id}).populate('user_revision_ref');
        dailyactivity = await populateProjectRefs([dailyactivity], 'project_ref', []);
        [dailyactivity] = await populateUserRefs(dailyactivity, 'user_ref', []);
        if (dailyactivity && dailyactivity.id) {
            let html = await processViewDownloadActivity(req, res, dailyactivity, 'html', 'small');
            let projectInfo = dailyactivity.project_ref;
            let daPhrase = projectInfo.custom_field ? projectInfo.custom_field.da_phrase_singlr : 'Daily Activity';
            let username = getUserFullName(dailyactivity.user_ref);
            let attachmentName = `${daPhrase}-Report-${dailyactivity.shift_date}-${username.replace(/[^a-zA-Z ]/g, "")}-${dailyactivity.createdAt}`;
            ResponseService.successResponse(res, {message: `The ${daPhrase} report is being prepared and will be shared shortly.`});
            await shareReportViaEmail(req, res, html, 'daily-activity', attachmentName, daPhrase, byUser, email, projectInfo.name);
            return;
        }
    },

    downloadActivityHoursBreakdownReport: async(req, res) => {
        let projectId = +req.param('projectId');
        let fromDate = moment(req.body.fromDate).startOf('day').valueOf();
        let toDate = moment(req.body.toDate).endOf('day').valueOf();
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        let filter = { project_ref: projectId };

        if (!fromDate || !toDate) {
            return errorResponse(res, 'from date and to date are required.');
        }

        let fromShiftDate = moment(fromDate).format('YYYY-MM-DD');
        let toShiftDate = moment(toDate).format('YYYY-MM-DD');
        let shiftDateFilter = fromDate;
        if (toShiftDate > fromShiftDate) {
            shiftDateFilter = {'>=': fromShiftDate, '<=': toShiftDate};
        };
        sails.log.info('shiftDateFilter: ', shiftDateFilter);
        sails.log.info('fetch all daily-activities with filter:', filter, fromDate, toDate, `is_inherited_project: ${is_inherited_project}`);
        let allActivities =  await sails.models.projectdailyactivities_reader.find({
            select: ['shift_date', 'shift_time', 'submitted_by', 'activities', 'activities_location', 'progress', 'operatives', 'plants', 'createdAt'],
            where: {
                shift_date: shiftDateFilter,
                ...filter
            },
            sort: ['shift_date ASC']
        });
        let projectInfo = await sails.models.project_reader.findOne({where: {id: projectId}, select: ['name', 'custom_field']});
        let workbook = await getActivityHoursBreakdownWorkbook(allActivities, projectInfo);
        let fileName = `Activity-Hours-Breakdown-Report-${projectId}-[${moment(fromDate).format('DD-MM-YYYY')}-${moment(toDate).format('DD-MM-YYYY')}].xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    },
}

