const {
    DEFAULT_PAGE_SIZE,
    EMAIL_NOTIFICATION_FEATURE_CODES,
    displayDateFormat_DD_MM_YYYY,
    displayTimeFormat_HH_mm_ss,
    displayDateFormat_DD_MM_YYYY_HH_mm_ss,
    FEATURES: { PERMIT_TOOL, PERMIT_REGISTER},
    INNDEX_SLS_ARN: { CONSTANT_ACTIVE_PERMIT_FN_ARN, CONSTANT_MARK_PERMIT_EXPIRE_FN_ARN, CONSTANT_NOTIFY_BEFORE_PERMIT_EXPIRE_FN_ARN }
} = sails.config.constants;
const _uniq = require('lodash/uniq');
const dayjs = require('dayjs');
const PERMIT_STATUS = Object.freeze({
    PENDING: 1,
    REGISTER_PENDING: 8,
    APPROVED: 2,
    ACTIVE: 3,
    CHANGE_REQUESTED: 4,
    REJECTED: 5,
    EXPIRED: 6,
    CLOSED_OUT: 7,
    CLOSEOUT_REQUESTED: 9,
    CANCELLED: 10
});
const {
    ResponseService: {
        errorObject,
        sendResponse,
        errorResponse,
        successResponse
    },
    HttpService: {
        fetchUrlAs
    },
    SharedService: {
        extractDayAndHour,
        s3UploaderWithExpiry,
        mergePdfsViaUrls,
        downloadPdfViaGenerator,
        scheduleEvent,
    },
    DataProcessingService: {
        populateUserRefs,
        populateUserFileRefs,
        populateProjectRefs,
        populateAttachmentFileRefs,
        getUserFullName,
        getUserFirstName,
        saveToolBriefing,
        getProjectTimezone,
        containsNonLatinCodepoints,
    },
    EmailService: {
        queueEmailNotifications,
    },
    NotificationService: {
        NOTIFICATION_CATEGORY,
        sendPushNotification
    },
    TokenUtil: {
        getPermitRequestStatusLabel,
        getCompanyInfo,
        hasOnlySiteManagementAccess,
    },
    ExcelService: {
        streamExcelDownload,
        exportPermitRequestsReport,
    },
    ASiteService : { checkIfAsiteEnabled, getAsiteProjectToolMapping, uploadDocOnAsite }
} = require('./../services');

const {
    coreFn: {
        fetchFileDataByIds,
    },
    inductionFn: {
        getInductionEmployerByUserIds,
        getProjectInductions,
        getUserInductionEmployer,
    },
    permitFn: {
        getPaginatePermitTemplates,
        getPaginatePermitRequests,
        expandFileRefs,
        getPermitConfigBySignOffUser,
        getBriefedByUserPermits,
    }
} = require('../sql.fn');

const REQUESTER_DATA_FIELDS = {
    JOB_ROLE: "job_role_of_requestor",
    COMPANY: "company_of_requestor",
    NAME: "name_of_requestor",
    REQUEST_DATETIME: "datetime_of_request",
    REQUEST_DATE: "date_of_request",
    REQUEST_TIME: "time_of_request"
}

const SIGNATORY_DATA_FIELDS = {
    JOB_ROLE: "job_role_of_signatory",
    COMPANY: "company_of_signatory",
    NAME: "name_of_signatory",
    SIGN_OFF_DATETIME: "sign_off_datetime",
    SIGN_OFF_DATE: "sign_off_date",
    SIGN_OFF_TIME: "sign_off_time"
}

const PROJECT_DATA_FIELDS = {
    PROJECT_NAME: "project_name",
    PROJECT_CONTRACT: "project_contractor",
    PROJECT_POSTCODE: "project_postcode"
}

const PERMIT_TEMPLATE_FIELDS = {
    PERMIT_REF: "permit_ref",
    PERMIT_TYPE: "permit_type",
}

const PERMIT_DATA_FIELDS = {
    RECORD_ID: "permit_record_id",
    STATUS: "permit_status"
}

const START_DATE_DATA_FIELDS = {
    PERMIT_START_DATETIME: "permit_start_datetime",
    PERMIT_START_DATE: "permit_start_date",
    PERMIT_START_TIME: "permit_start_time"
}

const EXPIRY_DATE_DATA_FIELDS = {
    PERMIT_EXPIRY_DATETIME: "permit_expiry_datetime",
    PERMIT_EXPIRY_DATE: "permit_expiry_date",
    PERMIT_EXPIRY_TIME: "permit_expiry_time"
}

const action_status = {
    "change-request": 4,
    "reject": 5,
    "closeout-requested": 9,
    "closeout": 7,
    "cancelled": 10,
}

const status_vs_label = {
    "approved": 2,
    "active": 3,
    ...action_status
}
const SIGNOFF_INITIAL_STATE = 10;
const SIGNOFF_FINAL_STATE = 190;
const CLOSEOUT_REQUESTOR_STATE = 200; //it was 91 earlier
const CLOSEOUT_INITIAL_STATE = 210;
const CLOSEOUT_FINAL_STATE = 400; //it was 95 earlier
const FINAL_STATE = 500;

const {
    PermitValidator: {
        addPermitTemplate,
        updatePermitTemplate,
        validateProjectPermitConfig,
        createPermitRequestValidator,
        resubmitPermitRequestValidator,
        signOffRequestValidator,
        requestCloseoutNotifyValidator,
    }
} = require('./../validators');

const {
    PDFDocument,
    drawRectangle,
    rgb,
    degrees,
    drawImage,
} = require("pdf-lib");
const fontkit = require('@pdf-lib/fontkit');

const permit_status_meta = {
    "1":  {"label": "Pending", "color": "#FFAF14"},
    "8":  {"label": "Register Pending", "color": "#FFAF14"},
    "2":  {"label": "Approved", "color": "#1EC1DC"},
    "3":  {"label": "Active", "color": "#5BCF67"},
    "4":  {"label": "Change Requested", "color": "#F8D51D"},
    "5":  {"label": "Rejected", "color": "#FF6442"},
    "6":  {"label": "Expired", "color": "#8C8D84"},
    "7":  {"label": "Closed Out", "color": "#4E3515"},
    "9":  {"label": "Closeout Requested", "color": "#F8D51D"},
    "10":  {"label": "Cancelled", "color": "#FF6442"}
}

const  associateStateWithSignOff = async (sign_off, templateSignatures) => {
    let state = SIGNOFF_INITIAL_STATE;
    let closeout_state = CLOSEOUT_INITIAL_STATE;
    let lastSignOffIndex = undefined;
    let lastCloseoutIndex = undefined;
    //finding index to set final state
    (templateSignatures || []).map((sign, index) => {
       if (sign.is_closeout) {
           lastCloseoutIndex = index;
       }  else if (!sign.is_closeout_requestor && !sign.is_closeout) {
           lastSignOffIndex = index;
       }
    });
    sails.log.info("lastSignOffIndex: ", lastSignOffIndex, "lastCloseoutIndex: ", lastCloseoutIndex);
    return (templateSignatures || []).map((sign, index) => {
        sign = _.pick((sign || {}), [
            'sign_number',
            'is_requestor',
            'is_closeout_requestor',
            'is_closeout',
            'field_name',
            'field_label',
            'link_fields',
            'link_sections',
            'signatories'
        ]);
        sign.signatories = (sign_off[index] && sign_off[index].signatories) ? sign_off[index].signatories : [];
        //assuming that closeout will last sign off so its state should be 99
        if (templateSignatures[index].is_closeout_requestor) {
            sign.state = CLOSEOUT_REQUESTOR_STATE;
        } else if (templateSignatures[index].is_closeout) {
            sign.state = (lastCloseoutIndex === index) ? CLOSEOUT_FINAL_STATE : closeout_state;
            closeout_state += 10;
        } else {
            sign.state = (lastSignOffIndex === index) ? SIGNOFF_FINAL_STATE : state;
            state += 10;
        }

        return sign;
    });
}

const getDataFieldValue = async (permitRequest, userId, dataFields, formField) => {
    let fieldValue = null;
    let [userInfo] = await getInductionEmployerByUserIds([userId], permitRequest.project_ref.id, [6, 2, 4, 5], true);
    switch (formField.data_field) {
        case dataFields.JOB_ROLE:
            fieldValue = (userInfo && userInfo.job_role) || null;
            break;
        case dataFields.COMPANY:
            fieldValue = (userInfo && userInfo.user_employer) || null;
            break;
        case dataFields.NAME:
            fieldValue = (userInfo && userInfo.user_name) || null;
            break;
    }

    return fieldValue;
};

const fillPermitPdf = async (req, res, permitRequest, permitTemplate, includeOperativeRegister) => {
    let tz = getProjectTimezone(permitRequest.project_ref);

    //Filling PDF START
    const formUrl = permitTemplate.fillable_pdf_ref.file_url; //'https://pdf-lib.js.org/assets/dod_character.pdf';
    sails.log.info('permit request: filling pdf start.', formUrl);

    let response = await fetchUrlAs(formUrl);
    let formBytes = '';
    if (!response.success) {
        sails.log.info('Failed to load pdf, fillable pdf not found.');
        return errorObject('Failed to load pdf, fillable pdf not found.');
    }
    formBytes = Buffer.from(response.data, 'binary').toString('base64');

    // Load the PDF with form fields
    const pdfDoc = await PDFDocument.load(formBytes);

    // get two text fields from the form
    const form = pdfDoc.getForm();

    let hasNonLatinChar = false;
    for(let formField of permitTemplate.fillable_pdf_fields) {
        let fieldValue = (permitRequest.pdf_fields_value || []).find(field => (field.field_name === formField.field_name && field.field_id === formField.field_id));
        let signInfo = (permitRequest.signatures || []).find(sign => sign.field_name === formField.field_name);

        if ((!fieldValue || !fieldValue.value) && (!signInfo || !signInfo.sign_ref) && formField.field_type != 'data_field') {
            continue;
        }

        if(formField.field_type === 'signature') {
            let signInfo = (permitRequest.signatures || []).find(sign => sign.field_name === formField.field_name);
            if (signInfo && signInfo.sign_ref) {
                let sign = await getUserSign(signInfo.sign_ref);
                sails.log.info("Adding Signature.");
                const emblemImage = await pdfDoc.embedPng(sign);
                const imageField = form.getButton(formField.field_name)
                // Fill the faction image field with our emblem image
                imageField.setImage(emblemImage)
            }
        } else if (formField.field_type === 'esignature') {
            let signInfo = (permitRequest.signatures || []).find(sign => sign.field_name === formField.field_name);
            if (signInfo && signInfo.sign_ref) {
                let sign = await getUserSign(signInfo.sign_ref);
                sails.log.info("Adding E-Signature.");
                const emblemImage = await pdfDoc.embedPng(sign);
                const imageField = form.getSignature(formField.field_name);
                const pdfLibSigImgName = signInfo.field_name;

                imageField.acroField.getWidgets().forEach((widget) => {
                    const { context } = widget.dict;
                    const { width, height } = widget.getRectangle();
                    const appearance = [
                        /*...drawRectangle({
                            x: 0,
                            y: 0,
                            width,
                            height,
                            borderWidth: 0.5,
                            color: rgb(0, 0, 0),
                            borderColor: rgb(0, 0, 0),
                            rotate: degrees(0),
                            xSkew: degrees(0),
                            ySkew: degrees(0),
                        }),*/

                        ...drawImage(pdfLibSigImgName, {
                            x: 5,
                            y: 5,
                            width: width - 10,
                            height: height - 10,
                            rotate: degrees(0),
                            xSkew: degrees(0),
                            ySkew: degrees(0),
                        }),
                    ];

                    const stream = context.formXObject(appearance, {
                        Resources: { XObject: { [pdfLibSigImgName]: emblemImage.ref } },
                        BBox: context.obj([0, 0, width, height]),
                        Matrix: context.obj([1, 0, 0, 1, 0, 0]),
                    });
                    const streamRef = context.register(stream);

                    widget.setNormalAppearance(streamRef);
                });
            }
        } else if(formField.field_type === 'data_field') {
            let field = form.getTextField(formField.field_name);
            sails.log.info(`Filling data field through: ${formField.data_field}`);
            let fieldValue = null

            // Process form field data
            if ([REQUESTER_DATA_FIELDS.JOB_ROLE, REQUESTER_DATA_FIELDS.COMPANY, REQUESTER_DATA_FIELDS.NAME].includes(formField.data_field)) {
                fieldValue = await getDataFieldValue(permitRequest, permitRequest.requestor_ref, REQUESTER_DATA_FIELDS, formField);
            } else if ([SIGNATORY_DATA_FIELDS.JOB_ROLE, SIGNATORY_DATA_FIELDS.COMPANY, SIGNATORY_DATA_FIELDS.NAME].includes(formField.data_field) && formField.linked_with) {
                let signature = (permitRequest.signatures || []).find(sign => sign.field_name === formField.linked_with);
                if (signature && +signature.signatory_user_ref) {
                    fieldValue = await getDataFieldValue(permitRequest, +signature.signatory_user_ref, SIGNATORY_DATA_FIELDS, formField);
                }
            } else if ([SIGNATORY_DATA_FIELDS.SIGN_OFF_DATETIME, SIGNATORY_DATA_FIELDS.SIGN_OFF_DATE, SIGNATORY_DATA_FIELDS.SIGN_OFF_TIME].includes(formField.data_field) && formField.linked_with) {
                let signature = (permitRequest.signatures || []).find(sign => sign.field_name === formField.linked_with);
                if (signature && +signature.signatory_user_ref) {
                    fieldValue = (+signature.date_signed) ? dayjs(+signature.date_signed).tz(tz).format((formField.data_field === SIGNATORY_DATA_FIELDS.SIGN_OFF_DATETIME) ? displayDateFormat_DD_MM_YYYY_HH_mm_ss : (formField.data_field === SIGNATORY_DATA_FIELDS.SIGN_OFF_DATE) ? displayDateFormat_DD_MM_YYYY : displayTimeFormat_HH_mm_ss) : null;
                }
            } else if ([REQUESTER_DATA_FIELDS.REQUEST_DATETIME, REQUESTER_DATA_FIELDS.REQUEST_DATE, REQUESTER_DATA_FIELDS.REQUEST_TIME].includes(formField.data_field) && permitRequest.createdAt) {
                fieldValue = dayjs(permitRequest.createdAt).tz(tz).format((formField.data_field === REQUESTER_DATA_FIELDS.REQUEST_DATETIME) ? displayDateFormat_DD_MM_YYYY_HH_mm_ss : (formField.data_field === REQUESTER_DATA_FIELDS.REQUEST_DATE) ? displayDateFormat_DD_MM_YYYY : displayTimeFormat_HH_mm_ss);
            } else if ([PROJECT_DATA_FIELDS.PROJECT_NAME, PROJECT_DATA_FIELDS.PROJECT_CONTRACT, PROJECT_DATA_FIELDS.PROJECT_POSTCODE].includes(formField.data_field)) {
                let project = await sails.models.project_reader.findOne({
                    where: { id: +permitRequest.project_ref.id },
                    select: ['name', 'project_number','postcode']
                });
                fieldValue = (PROJECT_DATA_FIELDS.PROJECT_NAME === formField.data_field) ? project.name : ((PROJECT_DATA_FIELDS.PROJECT_CONTRACT === formField.data_field) ? project.project_number : project.postcode);
            } else if ([PERMIT_TEMPLATE_FIELDS.PERMIT_REF, PERMIT_TEMPLATE_FIELDS.PERMIT_TYPE].includes(formField.data_field)) {
                fieldValue = (PERMIT_TEMPLATE_FIELDS.PERMIT_REF === formField.data_field) ? permitTemplate.ref_number : permitTemplate.permit_type;
            } else if ([START_DATE_DATA_FIELDS.PERMIT_START_DATETIME, START_DATE_DATA_FIELDS.PERMIT_START_DATE, START_DATE_DATA_FIELDS.PERMIT_START_TIME].includes(formField.data_field)) {
                fieldValue = dayjs(+permitRequest.start_on).tz(tz).format((formField.data_field === START_DATE_DATA_FIELDS.PERMIT_START_DATETIME) ? displayDateFormat_DD_MM_YYYY_HH_mm_ss : (formField.data_field === START_DATE_DATA_FIELDS.PERMIT_START_DATE) ? displayDateFormat_DD_MM_YYYY : displayTimeFormat_HH_mm_ss);
            } else if ([EXPIRY_DATE_DATA_FIELDS.PERMIT_EXPIRY_DATETIME, EXPIRY_DATE_DATA_FIELDS.PERMIT_EXPIRY_DATE, EXPIRY_DATE_DATA_FIELDS.PERMIT_EXPIRY_TIME].includes(formField.data_field) && +permitRequest.expire_on) {
                fieldValue = dayjs(+permitRequest.expire_on).tz(tz).format((formField.data_field === EXPIRY_DATE_DATA_FIELDS.PERMIT_EXPIRY_DATETIME) ? displayDateFormat_DD_MM_YYYY_HH_mm_ss : (formField.data_field === EXPIRY_DATE_DATA_FIELDS.PERMIT_EXPIRY_DATE) ? displayDateFormat_DD_MM_YYYY : displayTimeFormat_HH_mm_ss);
            } else if (PERMIT_DATA_FIELDS.STATUS === formField.data_field) {
                let status_meta = getPermitRequestStatusLabel(permitRequest.status);
                fieldValue = (status_meta && status_meta.label) ? status_meta.label : null;
            } else if (PERMIT_DATA_FIELDS.RECORD_ID === formField.data_field) {
                fieldValue = permitRequest.record_id;
            }

            let da = field.acroField.getDefaultAppearance() || '';
            if (field && fieldValue) {
                if (+permitTemplate.font_size && da) {
                    field.setFontSize(+permitTemplate.font_size)
                }
                field.setText(`${fieldValue}`);
            }
        } else {
            if (!hasNonLatinChar) {
                hasNonLatinChar = containsNonLatinCodepoints(fieldValue.value);
            }
            let field = form.getTextField(formField.field_name);
            let da = field.acroField.getDefaultAppearance() || '';
            if (field && fieldValue.value) {
                if (+permitTemplate.font_size && da) {
                    field.setFontSize(+permitTemplate.font_size)
                }
                field.setText(`${fieldValue.value}`);
            }
        }
    }

    if (hasNonLatinChar) {
        sails.log.info('Some values contains non latin characters.');
        // Fetch the Ubuntu font
        const fontUrl = 'https://pdf-lib.js.org/assets/ubuntu/Ubuntu-R.ttf';
        const fontBytes = await fetch(fontUrl).then((res) => res.arrayBuffer());

        // Embed the Ubuntu font
        pdfDoc.registerFontkit(fontkit);
        const ubuntuFont = await pdfDoc.embedFont(fontBytes);

        // **Key Step:** Update the field appearances with the Ubuntu font
        form.updateFieldAppearances(ubuntuFont);
    }

    // Flatten the form's fields
    try {
        form.flatten();
    } catch (e) {
        sails.log.info("Unable to flatten, ", e);
    }

    let fileName = permitTemplate.fillable_pdf_ref.name;
    const pdfBytes = await pdfDoc.save();
    let fileAsBuffer = new Buffer.from(pdfBytes);
    if (includeOperativeRegister && permitRequest.has_register && permitTemplate.take_register_when) {
        const mergedPdf = await PDFDocument.create();
        const pdfA = await PDFDocument.load(fileAsBuffer);
        const copiedPagesA = await mergedPdf.copyPages(pdfA, pdfA.getPageIndices());
        copiedPagesA.forEach((page) => mergedPdf.addPage(page));

        let registerPageBuffer = await generateRegisterPage(req, res, permitRequest, permitTemplate);
        const pdfB = await PDFDocument.load(registerPageBuffer);
        const copiedPagesB = await mergedPdf.copyPages(pdfB, pdfB.getPageIndices());
        copiedPagesB.forEach((page) => mergedPdf.addPage(page));

        const mergedPdfFile = await mergedPdf.save();
        fileAsBuffer = new Buffer.from(mergedPdfFile);
    }

    let s3UploadResponse = await s3UploaderWithExpiry(`${PERMIT_TOOL}/pdf/${dayjs().add(10, 'seconds').valueOf()}/${fileName}`, fileAsBuffer, 'application/pdf', 'public-read', 1);
    if (s3UploadResponse.Location || s3UploadResponse.location) {
        sails.log.info("got filled permit pdf.")
        return {
            file_url: s3UploadResponse.public_url || s3UploadResponse.Location,
            file_name: fileName
        };
    }
    sails.log.info('Something went wrong while filling pdf.');
    return false;
}

const generateRegisterPage = async (req, res, permitRequest, permitTemplate) => {
    let briefings = await sails.models.toolbriefings_reader.find({
        where: { tool_record_ref: permitRequest.id, tool_key: PERMIT_REGISTER, project_ref: permitRequest.project_ref.id },
        select: ['id', 'briefed_at', 'briefed_by', 'briefed_by_name', 'project_ref', 'tool_record_ref', 'register', 'guest_register']
    });
    sails.log.info(`Found ${briefings.length} for permit request ${permitRequest.id}.`);

    briefings = await populateUserRefs(briefings, 'briefed_by', ['id', 'first_name', 'last_name']);
    for (let i=0; i < briefings.length; i++) {
        let briefing = briefings[i];
        briefing.allattendees = [];
        let signatures = await sails.models.usersignature_reader.find({
            where: {briefing_ref: briefing.id}
        });
        let userIds = (briefing.register || []).map(r => r.user_ref).filter(Number);
        if (userIds.length) {
            briefing.register = await getInductionEmployerByUserIds(userIds, permitRequest.project_ref.id, [6, 2, 4, 5], true);
            briefing.allattendees = briefing.register.reduce(function (prev, item, i) {
                let signature = (signatures || []).find(object => object.user_ref === item.user_ref);
                prev.push({id: item.user_ref, name: item.user_name, employer: item.user_employer, job_role: item.job_role, sign: signature ? signature.sign : null});
                return prev;
            }, []);
        }

        //push guest attendees as well
        briefing.guest_register.map(gr => {
            let signature = (signatures || []).find(object => object.guest_reg_id == gr.id);
            briefing.allattendees.push({...gr, sign: signature ? signature.sign : null});
        });

        briefings[i] = briefing;
    }

    let { project_logo_file } = await getCompanyInfo(permitRequest.project_ref);

    let html = await sails.renderView('pages/permit-register', {
        title: `Permit Register`,
        registers: briefings,
        dayjs(n, format) {
            return dayjs(+n).format(format);
        },
        layout: false
    });

    const tz = getProjectTimezone(permitRequest.project_ref)
    return await downloadPdfViaGenerator({
        req,
        res,
        html,
        tool:'permit-register',
        file_name: `permit-register-${dayjs().valueOf()}`,
        logo_file: project_logo_file,
        heading_line: `${permitTemplate.ref_number} - ${permitTemplate.permit_type}`,
        project_line: `${permitRequest.project_ref.project_number} - ${permitRequest.project_ref.name}`,
        date_line: `Requested: ${dayjs().tz(tz).format('DD/MM/YYYY HH:mm:ss')}`,
        additional_line: `Permit Number: ${permitRequest.record_id}`,
        responseType: 'pdfBuffer',
        ellipses_heading: true,
    });
   }

//Approved if start date is not yet reached else Active
const isActiveOrApproved = (startOn, permitTemplate, permitRequest) => {
    if (permitTemplate.take_register_when && !permitRequest.has_register) {
        return PERMIT_STATUS.REGISTER_PENDING;
    }
    return (+startOn > dayjs().valueOf()) ? PERMIT_STATUS.APPROVED : PERMIT_STATUS.ACTIVE;
}

const getExpireOnEpoch = (expireInISO, startOnEpoch) => {
    let { day, hour } = extractDayAndHour(expireInISO);
    return (+day || +hour) ? dayjs(+startOnEpoch).add(+day, 'days').add(+hour, 'hours').valueOf() : null;
}

const prepareSignOffUsers = async (projectId, userIds) => {
    let additionalCols = [
        `additional_data -> 'user_info' ->> 'first_name'       as first_name`,
        `additional_data -> 'user_info' ->> 'middle_name'      as middle_name`,
        `additional_data -> 'user_info' ->> 'last_name'        as last_name`,
        `additional_data -> 'user_info' -> 'profile_pic_ref' ->> 'id'  as profile_pic_id`,
        `additional_data -> 'user_info' -> 'profile_pic_ref' ->> 'file_url'  as profile_pic_url`,
        `additional_data -> 'employment_detail' ->> 'employer' as employer`,
        `additional_data -> 'employment_detail' ->> 'id' as employer_id`,
        `additional_data -> 'employment_detail' ->> 'job_role' as job_role`
    ];

    let {
        records
    } = await getProjectInductions(projectId, {
        statusCodes: [PERMIT_STATUS.APPROVED, PERMIT_STATUS.EXPIRED],
        limit: -1,
        searchUserIds: userIds
    }, additionalCols);

    sails.log.info(`Got ${records.length} users from sign-off users list.`);
    let distinctUserIds = []
    return (records || []).reduce((arr, item) => {
        if (distinctUserIds.includes(item.user_ref)) {
            return arr;
        }
        distinctUserIds.push(item.user_ref);
        arr.push({
            "induction_id": item.id,
            "id": item.user_ref,
            "first_name": item.first_name,
            "last_name": item.last_name,
            "profile_pic_ref": {
                "id": item.profile_pic_id,
                "file_url": item.profile_pic_url
            },
            "name": item.name,
            "user_employer": {
                "id": item.employer_id,
                "employer": item.employer,
            }
        })
        return arr;
    }, []);
}

const notifyAppUsers = async (req, message, messageTitle, project, permitRequest, sendToUsers, category, additionalInfo={}) => {
    const notificationData = {
        category,
        document_id: permitRequest.id,
        project_id: project.id,
        ...additionalInfo
    }
    let firebaseMsgData = {
        category,
        document_id: permitRequest.id.toString(),
        project_id: project.id.toString(),
    };

    for (let i=0; i < sendToUsers.length; i++) {
        await sendPushNotification({
            message,
            messageTitle,
            recipientUserInfo: sendToUsers[i],
            submittedByUserInfo: req.user,
            category,
            notificationData,
            firebaseMsgData
        });
    }
}

const savePermitRequest = async (req, res, requestBody, sendToUsers, prId= 0) => {
    let permitTemplate = await sails.models.permittemplate_reader.findOne({
        where: {id: +requestBody.permit_ref},
        select: ['signatures', 'expire_in', 'take_register_when']
    });
    requestBody.expire_on = (permitTemplate.expire_in) ? getExpireOnEpoch(permitTemplate.expire_in, requestBody.start_on) : null;

    //process to set state of permit request
    if (requestBody.signature) {
        let signWithMetaInfo = await attachMetaInfoWithSignature(req, permitTemplate.signatures, requestBody.signature, []);
        if (!signWithMetaInfo || !signWithMetaInfo.sign_ref) {
            sails.log.error(signWithMetaInfo);
            return sendResponse(res, signWithMetaInfo);
        }
        sails.log.info(`got signature in the permit request.`);
        let permitConfig = await sails.models.projectpermitconfig_reader.findOne({
            where: { id: +requestBody.config_ref },
            select: ['sign_off']
        });

        if (!permitConfig) {
            sails.log.error(`Invalid request, permit config not found for given id: ${+requestBody.config_ref}.`);
            return errorObject(`Invalid request, permit config not found for given id: ${+requestBody.config_ref}.`);
        }

        requestBody.state = (permitConfig.sign_off.length > 1) ? 20 : FINAL_STATE;

        if (requestBody.state === 20 && (permitTemplate.signatures.length > 1)) {
            let nextSignOff = permitConfig.sign_off[1];
            requestBody.state = nextSignOff.state;
            requestBody.status = (requestBody.state >= CLOSEOUT_REQUESTOR_STATE) ? isActiveOrApproved(requestBody.start_on, permitTemplate, requestBody) : requestBody.status;
        } else if (requestBody.state === FINAL_STATE) {
            requestBody.status = isActiveOrApproved(requestBody.start_on, permitTemplate, requestBody);
        }

        requestBody.signatures = [signWithMetaInfo];
    }
    sails.log.info(`Permit request with state: ${requestBody.state} and status: ${requestBody.status} is being saved.`);
    delete requestBody.signature;

    let permitRequest = {};
    if (prId) {
        let payload = _.pick((requestBody || {}), [
            'mandatory_attachments',
            'pdf_fields_value',
            'signatures',
            'start_on',
            'expire_on',
            'state',
            'status',
            'status_logs',
        ]);
        let {validationError} = resubmitPermitRequestValidator(payload);
        if(validationError) {
            return errorObject('Invalid Request.', {validationError});
        }
        permitRequest = await sails.models.permitrequest.updateOne({
            id: prId
        }).set(payload);
    } else {
        let {validationError} = createPermitRequestValidator(requestBody);
        if(validationError) {
            return errorObject('Invalid Request.', {validationError});
        }
        permitRequest = await sails.models.permitrequest.create(requestBody);
    }

    //activate the permit through scheduler
    if ([PERMIT_STATUS.APPROVED, PERMIT_STATUS.REGISTER_PENDING].includes(permitRequest.status)) {
        await scheduleEvent(permitRequest.id, 'permit', CONSTANT_ACTIVE_PERMIT_FN_ARN, permitRequest.start_on, 'active');
    }

    //schedule to expire permit
    if ([PERMIT_STATUS.APPROVED, PERMIT_STATUS.REGISTER_PENDING, PERMIT_STATUS.ACTIVE].includes(permitRequest.status) && permitRequest.expire_on) {
        let beforePermitExpire = dayjs(+permitRequest.expire_on).subtract(30, 'minutes').valueOf();
        let updateExisting = !!(prId);
        await scheduleEvent(permitRequest.id, 'permit', CONSTANT_NOTIFY_BEFORE_PERMIT_EXPIRE_FN_ARN, beforePermitExpire, 'before_expire', updateExisting);

        await scheduleEvent(permitRequest.id, 'permit', CONSTANT_MARK_PERMIT_EXPIRE_FN_ARN, permitRequest.expire_on, 'expire', updateExisting);
    }

    //notification: To selected signatories
    if (sendToUsers && sendToUsers.length) {
        sails.log.info(`Notify users about permit request sign-off on project ${permitRequest.project_ref}.`);
        let project = await sails.models.project_reader.findOne({
            where: {id: permitRequest.project_ref},
            select: ['name', 'custom_field']
        });
        let tz = await getProjectTimezone(project);
        let permitTemplate = await sails.models.permittemplate_reader.findOne({
            where: {id: +permitRequest.permit_ref},
            select: ['permit_type']
        })


        let permit_title = permitTemplate.permit_type;
        let mail_title = `Permit Sign-off Request (${permit_title}): ${project.name}`;
        let requester_name = getUserFullName(req.user);
        let message = `You have been assigned a permit sign-off request @ ${project.name}`;
        await sendMailToUsers(permitTemplate, permitRequest, sendToUsers, project, {
            mail_title,
            body_text: message,
            permit_requestor: `${requester_name} (${permitRequest.requestor_company})`
        });
        await notifyAppUsers (req, message, mail_title, project, permitRequest, sendToUsers, NOTIFICATION_CATEGORY.NEW_PERMIT_REQUEST);
    }

    return permitRequest;
};

const attachMetaInfoWithSignature = async (req, meta_signatures, signature, permitRequestSignatures) => {
    let existingSignOff = permitRequestSignatures.find(sign => sign.field_name === signature.field_name);
    if (existingSignOff) {
        sails.log.info(`Invalid Request, this sign-off stage ${signature.field_name} has already been approved on the permit.`)
        return errorObject(`Invalid Request, this sign-off stage has already been approved on the permit.`);
    }

    let metaData = meta_signatures.find(sign => sign.field_name === signature.field_name);
    if (!metaData) {
        return errorObject(`Invalid Request, the signatory is not authorise to sign-off the permit.`);
    }

    let sign_ref= await saveUserSign(req, signature.sign);
    delete signature.sign;

    let signWithMeta = {
        sign_ref,
        ...signature,
        "sign_number": metaData.sign_number,
        "field_label": metaData.field_label,
        "date_signed": dayjs().valueOf(),
        "signatory_user_ref": req.user.id,
        "is_requestor": metaData.is_requestor,
        "is_closeout_requestor": metaData.is_closeout_requestor,
        "is_closeout": metaData.is_closeout,
        "link_fields": metaData.link_fields,
    }

    sails.log.info("sign is attached with meta data: ", signWithMeta);

    return signWithMeta;
};

const saveUserSign = async (req, sign) => {
    let savedSign = await sails.models.usersignature.create({"sign": sign, "user_ref": req.user.id, "feature_name": PERMIT_TOOL});
    return savedSign.id;
};

const getUserSign = async (sign_ref) => {
    let userSign = await sails.models.usersignature_reader.findOne({id: +sign_ref});
    return userSign.sign;
};

const getUsersInfo = async (userIds=[]) => {
    if (userIds && userIds.length) {
        let sendToUsers = (await sails.models.user_reader.find({
            where: {id: userIds},
            select: ['first_name', 'last_name', 'email']
        }) || []).map(u => ({id: u.id, name: getUserFirstName(u), email: u.email}));
        let usersString = sendToUsers.map(user => `${user.name}`).join(', ');
        return {sendToUsers, usersString};
    }
    return {sendToUsers:[], usersString:''};
};

const prepareStatusLog = (req, action, note, origin, status_meta, comment, status) => {
    return {
        action,
        "timestamp": dayjs().valueOf(),
        "user_id": req.user.id,
        "name": getUserFullName(req.user),
        note,
        origin,
        status_meta,
        comment,
        status_code: status
    }
}

const getSignOffCount = (permitTemplate, permitRequest) => {
    let total_expected_signoff = permitTemplate.signatures.filter(sign => !sign.is_requestor && !sign.is_closeout_requestor && !sign.is_closeout).length;
    let signoff_count = permitRequest.signatures.filter(sign => !sign.is_requestor && !sign.is_closeout_requestor && !sign.is_closeout).length;
    return {total_expected_signoff, signoff_count};
}

const getCloseoutSignOffCount = (permitTemplate, permitRequest) => {
    let total_expected_signoff = permitTemplate.signatures.filter(sign => sign.is_closeout).length;
    let signoff_count = permitRequest.signatures.filter(sign => sign.is_closeout).length;
    return {total_expected_signoff, signoff_count};
}

const sendMailToUsers = async (permitTemplate, permitRequest, recipients, project, additionals={}) => {
    sails.log.info(`Notifying: ${additionals.body_text}, recipients count: ${recipients.length}`);

    let tz = await getProjectTimezone(project);
    await queueEmailNotifications(EMAIL_NOTIFICATION_FEATURE_CODES.PERMIT_TOOL, recipients, {
        messageInfo: {
            ...additionals,
            date_permit_request: dayjs(permitRequest.createdAt).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss),
            permit_type: permitTemplate.permit_type,
            permit_record_id: permitRequest.record_id,
            permit_start_datetime: dayjs(+permitRequest.start_on).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss)
        },
        project: {
            id: project.id,
            name: project.name,
        },
    });
};

const getPermitAttachments = async (req, res, permitRequest, permitTemplate, attachmentType) => {
    let action = req.param('action', 'download');
    let permitAttachments = [];
    // populate files ref
    const fileIds = permitRequest.mandatory_attachments.flatMap(attachment => attachment.attachment_file_refs || []);

    let files = await sails.models.userfile_reader.find({
        where: {id: fileIds},
        select: ['name', 'file_mime', 'file_url', 'img_translation', 'user_id', 'createdAt']
    });

    files = await populateUserRefs(files, 'user_id', ['first_name', 'last_name']);

    // Preparing attachments data
    let pdfAttachments = [];
    for (const attachment of permitRequest.mandatory_attachments) {
        attachment.attachment_file_refs && attachment.attachment_file_refs.length &&
        (attachment.attachment_file_refs = files.filter(file => attachment.attachment_file_refs.includes(file.id)))

        for (const attachmentFile of attachment.attachment_file_refs) {
            let attachmentObj = {
                title: attachment.title,
                attachment_type: attachment.attachment_type || 'mandatory',
            }
            if ((action === 'view' || attachmentType === 'grid') && attachmentFile.img_translation && attachmentFile.img_translation.length) {
                for (const img of attachmentFile.img_translation) {
                    attachmentObj.image = {
                        createdAt: attachmentFile.createdAt,
                        name: attachmentFile.name,
                        file_url: img,
                        user: attachmentFile.user_id
                        // file_mime: attachmentFile.file_mime
                    }
                    permitAttachments.push(attachmentObj);
                }
                // if (attachment.file_mime === 'application/pdf')
            } else if (attachmentFile.file_mime !== 'application/pdf') {
                attachmentObj.image = {
                    createdAt: attachmentFile.createdAt,
                    name: attachmentFile.name,
                    file_url: attachmentFile.file_url,
                    user: attachmentFile.user_id
                    // file_mime: attachmentFile.file_mime
                }
                permitAttachments.push(attachmentObj);
            } else {
                pdfAttachments.push(attachmentFile.file_url);
                // skip attachment something wrong
                console.log("skip attachment something wrong");
            }
        }
    }

    if (permitAttachments && !permitAttachments.length) {
        sails.log.info("getPermitAttachments: No mandatory attachment found");
        return { pdfAttachments };

    }
    let projectInfo = await sails.models.project_reader.findOne({
        where: {id: permitRequest.project_ref.id},
        select: ['name', 'contractor', 'parent_company', 'custom_field']
    });

    const tz = getProjectTimezone(projectInfo)
    let companyInfo = await getCompanyInfo(projectInfo);

    let html = await sails.renderView('pages/permit-form-pdf/permit-form-pdf', {
        permitName: `${permitTemplate.ref_number} - ${permitTemplate.permit_type}`,
        logo: companyInfo.project_logo_file.file_url,
        projectName: projectInfo.name,
        downloadedUser: req.user.first_name,
        attachments: permitAttachments,
        isFullSizeAttachments: attachmentType === 'full',
        getDate: (date) => {
            return dayjs(date).tz(tz).format('DD/MM/YYYY HH:mm');
        },
        todayDate: dayjs().tz(tz).format('DD/MM/YYYY HH:mm:ss'),
        layout: false
    });

    let imageAttachmentPdf = await downloadPdfViaGenerator({
        req,
        res,
        html,
        tool: 'permit-tools',
        file_name: projectInfo.name,
        responseType: 'path',
        heading_line: `${permitTemplate.ref_number} - ${permitTemplate.permit_type}`,
        project_line: `${permitRequest.project_ref.project_number} - ${projectInfo.name}`,
        date_line: `Requested: ${dayjs().tz(tz).format('DD/MM/YYYY HH:mm:ss')}`,
        additional_line: `Permit Number: ${permitRequest.record_id}`,
        logo_file: companyInfo.project_logo_file,
        ellipses_heading: true,
    });

    return { imageAttachmentPdf: imageAttachmentPdf.location, pdfAttachments };
}


const syncClosedPermitReqToAsite = async (req, res, projectInfo, permitReqId, localsObj, userEmp = 0) => {
    sails.log.info(`[syncClosedPermitReqToAsite] Starting execution for permit request ${permitReqId} employer ID ${userEmp}`);

    let {workspace_id, matched_tool} = await getAsiteProjectToolMapping(projectInfo.id, 'permit_tool', userEmp);
    if(!workspace_id || !matched_tool) {
        sails.log.info(`[syncClosedPermitReqToAsite] Aborting execution for permit request ${permitReqId} workspace_id or matched_tool not found.`);
        return;
    }

    let { employer } = await getCompanyInfo(projectInfo, null, ['id', 'company_initial']);

    let permitRequest = await sails.models.permitrequest_reader.findOne({
        where: {id: permitReqId},
        select: ['status_logs', 'signatures',  'status', 'state', 'start_on', 'expire_on', 'permit_ref', 'config_ref', 'project_ref', 'requestor_ref', 'pdf_fields_value', 'createdAt', 'has_register', 'mandatory_attachments', 'record_id']
    });

    let permitTemplate = await sails.models.permittemplate_reader.findOne({
        where: permitRequest.permit_ref,
        select: ['fillable_pdf_fields', 'field_sections', 'fillable_pdf_ref', 'permit_type', 'ref_number', 'take_register_when', 'font_size']
    }).populate('fillable_pdf_ref');

    [permitRequest] = await populateProjectRefs([permitRequest], 'project_ref', ['name', 'custom_field', 'parent_company', 'contractor', 'project_number']);

    sails.log.info(`[syncClosedPermitReqToAsite] preparing PDF for permit request ${permitReqId}`);
    res.locals = localsObj || res.locals;
    let filled_pdf = await fillPermitPdf(req, res, permitRequest, permitTemplate, true);
    if (!filled_pdf || !filled_pdf.file_url) {
        return sails.log.info('[syncClosedPermitReqToAsite] Something went wrong fetching permit document PDF.');
    }
    sails.log.info(`[syncClosedPermitReqToAsite] PDF prepared, starting asite upload for permit request ${permitReqId}`);

    //File name format -> {PROJECT/CONTRACT NUMBER} ({INNDEX PROJECT ID NUMBER})-{COMPANY INITIALS}-{TOOL NAME}-{REPORT TYPE}-{REPORT #}
    let toolPhraseForAsite = (projectInfo.custom_field.permit_phrase || '').replace(/\s/g, '_');
    let fileData = {
        location: filled_pdf.file_url,
        name: `${projectInfo.project_number} (${projectInfo.id})-${employer.company_initial}-${toolPhraseForAsite}-Permit_Report-${permitRequest.record_id}.pdf`
    };

    sails.log.info(`[syncClosedPermitReqToAsite] Filename to be used on asite ${fileData.name}`);

    await uploadDocOnAsite(employer.id, workspace_id, matched_tool.folder_id, fileData);
};

module.exports = {
    createPermitTemplate: async (req, res) => {
        let companyId = +req.param('companyId');
        sails.log.info(`creating permit template on company portal, by ${req.user.id}`);
        let requestBody = _.pick((req.body || {}), [
            'ref_number',
            'permit_type',
            'is_active',
            'ref_docs',
            'expire_in',
            'include_mandatory_attachments',
            'mandatory_attachments_title',
            'require_closeout',
            'fillable_pdf_ref',
            'fillable_pdf_fields',
            'field_sections',
            'signatures',
            'take_register_when',
            'register_config',
            'font_size',
        ]);

        requestBody.company_ref = companyId;
        requestBody.created_by = req.user.id;

        let {validationError} = addPermitTemplate(requestBody);
        if(validationError){
            return errorResponse(res, 'Invalid Request.', {validationError});
        }

        let permitTemplate = await sails.models.permittemplate.create(requestBody);

        sails.log.info('Permit template has been saved successfully.');
        return successResponse(res, {permitTemplate});
    },

    updatePermitTemplate: async (req, res) => {
        let id = +req.param('id');
        sails.log.info(`update permit template on company portal, by', ${req.user.id}`);
        let requestBody = _.pick((req.body || {}), [
            'ref_number',
            'permit_type',
            'is_active',
            'ref_docs',
            'expire_in',
            'include_mandatory_attachments',
            'mandatory_attachments_title',
            'require_closeout',
            'fillable_pdf_ref',
            'fillable_pdf_fields',
            'field_sections',
            'signatures',
            'take_register_when',
            'register_config',
            'font_size',
        ]);

        let {validationError} = updatePermitTemplate(requestBody);
        if(validationError){
            return errorResponse(res, 'Invalid Request.', {validationError});
        }

        let permitTemplate = await sails.models.permittemplate.updateOne({id}).set(requestBody);

        sails.log.info('Permit template has been updated successfully.');
        return successResponse(res, {permitTemplate});
    },

    updatePermitTemplateStatus: async (req, res) => {
        let id = +req.param('id');
        sails.log.info(`update permit template on company portal, by', ${req.user.id}`);
        let requestBody = _.pick((req.body || {}), [
            'is_active'
        ]);

        let permitTemplate = await sails.models.permittemplate.updateOne({id}).set(requestBody);

        sails.log.info('Permit template status has been updated successfully.');
        return successResponse(res, {permitTemplate});
    },

    fetchPermitTemplates: async (req, res) => {
        let companyId = +req.param('companyId');
        let searchTerm = (req.param('q', '')).toString().trim();
        let pageNumber = +req.param('pageNumber', 0);
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        sails.log.info(`Fetching permit templates by company: ${companyId}.`);

        //get main (parent_ref: null) assigned card only
        let defaultResponse = {
            records: [],
            q: searchTerm,
            pageSize,
            pageNumber,
            totalCount: 0,
        };
        let {
            records,
            total: totalCount
        } = await getPaginatePermitTemplates(companyId, pageSize, (pageSize * pageNumber), searchTerm);

        let fileIds = records.reduce((arr, item) => {
            arr.push(item.fillable_pdf_ref, ...item.ref_docs);
            return arr;
        }, []);
        if (fileIds.length) {
            let files = await expandFileRefs(fileIds);
            records = records.map(record => {
                record.fillable_pdf_ref = files.find(file => file.id === record.fillable_pdf_ref);
                record.ref_docs = files.filter(file => record.ref_docs.includes(file.id));
                return record;
            })
        }

        sails.log.info(`found ${records.length} permit templates.`);
        return successResponse(res, {...defaultResponse, records, totalCount});
    },

    fetchProjectPermitTemplates: async (req, res) => {
        let projectId = +req.param('projectId');
        sails.log.info(`Fetching active permit templates from company of project ${projectId}`);
        let projectInfo = await sails.models.project_reader.findOne({where: {id: projectId}, select: ['contractor', 'custom_field']});
        let country_code = (projectInfo.custom_field && projectInfo.custom_field.country_code) || 'GB';
        let parentCompany = await sails.models.createemployer_reader.findOne({where: {name: projectInfo.contractor, country_code}, select: 'name'});
        if (!projectInfo.contractor || !parentCompany.id) {
            sails.log.info('Invalid request, project company not found.');
            return errorResponse(res, `Invalid request, project company not found for project, ${projectId}`);
        }

        let companyId = parentCompany.id;
        sails.log.info(`Got company id ${companyId} of project ${projectId}`);

        let permitTemplates = await sails.models.permittemplate_reader.find({
            where: {company_ref: companyId, is_active: true},
            select: ['id', 'permit_type', 'signatures', 'ref_number']
        });

        let permitTemplatesConfig = await sails.models.projectpermitconfig_reader.find({
            where: {permit_ref: (permitTemplates || []).map(template => template.id), project_ref: projectId},
        });

        let master_permit_managers = [];
        (permitTemplates || []).forEach(template => {
            const permitConfig = (permitTemplatesConfig || []).find(config => config.permit_ref === template.id);
            template.is_active = false;
            if (permitConfig) {
                template.config_id = permitConfig.id;
                delete permitConfig.id;
                Object.assign(template, permitConfig);
                master_permit_managers = permitConfig.master_permit_managers
            }
        });

        sails.log.info(`Got ${permitTemplates.length} permit templates from company ${companyId} of project ${projectId}.`);
        return successResponse(res, {permit_templates: permitTemplates, master_permit_managers});
    },

    saveProjectPermitConfig: async (req, res) => {
        let projectId = +req.param('projectId');
        sails.log.info(`save project permit config for project ${projectId}, by ${req.user.id}`);

        let requestBody = _.pick((req.body || {}), [
            'master_permit_managers',
            'project_permits',
        ]);

        let permitTemplateIds = (requestBody.project_permits || []).map(item => item.permit_ref);

        let permitTemplates = await sails.models.permittemplate_reader.find({
            where: {id: permitTemplateIds},
            select: ['signatures']
        });

        for (let i = 0; i < requestBody.project_permits.length; i++) {
            let projectPermit = requestBody.project_permits[i];
            let permitTemplate = permitTemplates.find(pt => pt.id === projectPermit.permit_ref);
            projectPermit.project_ref = (projectPermit.project_ref || projectId);
            projectPermit.user_ref = (projectPermit.user_ref || req.user.id);
            projectPermit.sign_off = await associateStateWithSignOff(projectPermit.sign_off, permitTemplate.signatures);
            projectPermit.master_permit_managers = requestBody.master_permit_managers;
            requestBody.project_permits[i] = projectPermit;
        }

        let {validationError, payload} = validateProjectPermitConfig(requestBody.project_permits);
        if(validationError){
            return errorResponse(res, 'Invalid Request.', {validationError});
        }

        for (let i = 0; i < payload.length; i++) {
            let projectPermit = payload[i];
            if (projectPermit.id) {
                await sails.models.projectpermitconfig.updateOne({id: projectPermit.id}).set(projectPermit);
            } else {
                await sails.models.projectpermitconfig.create(projectPermit);
            }
        }

        sails.log.info('Project permits configuration have been saved successfully.');
        return successResponse(res, {});
    },

    getProjectPermitConfig: async (req, res) => {
        let id = +req.param('id');
        let projectId = +req.param('projectId');
        sails.log.info(`Fetching project permit config ${id} on project ${projectId}`);

        let permitConfig = await sails.models.projectpermitconfig_reader.findOne({id});

        sails.log.info('Fetched project permit config.');
        return successResponse(res, {permit_config: permitConfig});
    },

    getProjectActivePermits: async (req, res) => {
        const projectId = +req.param('projectId');
        sails.log.info(`Fetching project permits on project ${projectId}`);

        let permits = await sails.models.projectpermitconfig_reader.find({
            where: {project_ref: projectId, is_active: true},
            select: ['sign_off']
        }).populate('permit_ref');

        permits = permits.filter(permit => permit.permit_ref.is_active);

        let fileIds = permits.reduce((arr, permit) => {
            arr.push(...(permit.permit_ref.ref_docs || []));
            return arr;
        }, []);

        let files = [];
        if (fileIds.length) {
            files = await fetchFileDataByIds(_uniq(fileIds));
        }

        permits = permits.map(permit => {
            if (permit.permit_ref.ref_docs && permit.permit_ref.ref_docs.length && files.length) {
                permit.permit_ref.ref_docs = files.filter(file => (permit.permit_ref.ref_docs).includes(file.id));
            }

            //@todo Vshal: Remove this temporary code block in dec 2025, handle by app
            if (permit.permit_ref.include_mandatory_attachments === false) {
                permit.permit_ref.mandatory_attachments_title = [];
            }

            let otherSignOff = (permit.permit_ref.signatures || []).filter(sign => !sign.is_requestor && !sign.is_closeout)
            permit.has_next_signoff = !!(otherSignOff.length);
            return permit;
        })

        sails.log.info(`Found ${permits.length} permits on project ${projectId}.`);
        return successResponse(res, {permits});
    },

    createPermitRequest: async (req, res) => {
        let projectId = +req.param('projectId');
        sails.log.info(`create permit request on project ${projectId}, by ${req.user.id}`);

        let requestBody = _.pick((req.body || {}), [
            'permit_ref',
            'mandatory_attachments',
            'pdf_fields_value',
            'start_on',
            'status',
            'signature',
            'config_ref',
            'users_to_notify',
        ]);

        let tool_briefing = req.body.tool_briefing || undefined;
        let permitTemplate = await sails.models.permittemplate.findOne({
            where: {id: +requestBody.permit_ref},
            select: ['take_register_when']
        });

        if (!tool_briefing && permitTemplate.take_register_when === 'creating_permit') {
            sails.log.error(`Please take register before raising the permit.`);
            return errorResponse(res, `Please take register before raising the permit.`);
        }

        requestBody.status = 1;
        requestBody.project_ref = projectId;
        requestBody.requestor_ref = req.user.id;
        let requestInductionInfo = await getInductionEmployerByUserIds([req.user.id], projectId, [2, 6]);
        requestBody.requestor_company = (requestInductionInfo.length && requestInductionInfo[0].user_employer) ? requestInductionInfo[0].user_employer : null;
        requestBody.state = SIGNOFF_INITIAL_STATE;
        let {sendToUsers, usersString} = await getUsersInfo(requestBody.users_to_notify);
        let comment = (usersString.length) ? `${usersString} was selected to sign-off` : '';
        let status_log = prepareStatusLog(req, 'requested', 'Requested', 'system', getPermitRequestStatusLabel(requestBody.status), comment, requestBody.status);
        requestBody.status_logs = [status_log];
        let permitRequest = await savePermitRequest(req, res, requestBody, sendToUsers);
        if (!permitRequest.id) {
            return sendResponse(res, permitRequest);
        }

        if (tool_briefing && permitTemplate.take_register_when === 'creating_permit') {
            let toolBriefingReq = _.pick((tool_briefing || {}), [
                'tool_key',
                'briefed_at',
                'briefed_by',
                'project_ref',
                'tool_record_ref',
                'register',
                'guest_register',
            ]);

            toolBriefingReq.tool_record_ref = permitRequest.id;
            let briefing = await saveToolBriefing(projectId, null, toolBriefingReq);
            if (!briefing.id) {
                return sendResponse(res, briefing);
            }
        }
        sails.log.info('permit request has been saved successfully.');
        return successResponse(res, {permitRequest});
    },

    resubmitPermitRequest: async (req, res) => {
        let prId = +req.param('id');
        let projectId = +req.param('projectId');
        sails.log.info(`resubmit permit request ${prId} on project ${projectId} by ${req.user.id}`);

        let requestBody = _.pick((req.body || {}), [
            'mandatory_attachments',
            'pdf_fields_value',
            'start_on',
            'status',
            'signature',
            'users_to_notify',
        ]);
        requestBody.status = 1;
        requestBody.state = SIGNOFF_INITIAL_STATE;
        let permitRequest = await sails.models.permitrequest_reader.findOne({
            where: {id: prId, project_ref: projectId},
            select: ['status_logs', 'signatures',  'status', 'state', 'start_on', 'permit_ref', 'config_ref', 'project_ref', 'requestor_ref', 'pdf_fields_value', 'createdAt', 'has_register']
        });

        if (!permitRequest || !permitRequest.id) {
            sails.log.error(`Invalid id supplied to resubmit permit request, Id: ${prId}.`);
            return errorResponse(res, `Invalid id supplied to resubmit permit request, Id: ${prId}.`);
        }

        requestBody.permit_ref = permitRequest.permit_ref;
        requestBody.config_ref = permitRequest.config_ref;
        let {sendToUsers, usersString} = await getUsersInfo(requestBody.users_to_notify);
        let comment = (usersString.length) ? `${usersString} was selected to sign-off` : '';
        let status_log = prepareStatusLog(req, 'resubmitted', 'Resubmitted', 'system', getPermitRequestStatusLabel(requestBody.status), comment, requestBody.status);
        requestBody.status_logs = [
            status_log,
            ...permitRequest.status_logs
        ];
        permitRequest = await savePermitRequest(req, res, {...permitRequest, ...requestBody}, sendToUsers, prId);
        if (!permitRequest.id) {
            return sendResponse(res, permitRequest);
        }

        sails.log.info('permit request has been resubmitted successfully.');
        return successResponse(res, {permitRequest});
    },

    signOffPermitRequest: async (req, res) => {
        let prId = +req.param('id');

        let requestBody = _.pick((req.body || {}), [
            'signature',
            'action_type',
            'users_to_notify',
            'pdf_fields_value',
            'mandatory_attachments',
        ]);

        let permit_request = await sails.models.permitrequest_reader.findOne({
            where: {id: prId},
            select: ['signatures', 'status_logs', 'status', 'state', 'config_ref', 'permit_ref', 'start_on', 'project_ref', 'requestor_ref', 'pdf_fields_value', 'mandatory_attachments', 'createdAt', 'has_register']
        });

        if(!requestBody.signature || !requestBody.signature.sign || [PERMIT_STATUS.APPROVED, PERMIT_STATUS.ACTIVE, PERMIT_STATUS.CHANGE_REQUESTED].includes(permit_request.status) || permit_request.state === FINAL_STATE) {
            sails.log.error('Invalid sign off request.', `Permit status: ${permit_request.status}, Permit state: ${permit_request.state}`);
            return errorResponse(res, 'Invalid sign off request.');
        }

        let permitConfig = await sails.models.projectpermitconfig_reader.findOne({
            where: {id: +permit_request.config_ref},
            select: ['sign_off', 'master_permit_managers']
        });

        let permitTemplate = await sails.models.permittemplate_reader.findOne({
            where: {id: +permit_request.permit_ref},
            select: ['signatures', 'permit_type', 'expire_in', 'require_closeout', 'take_register_when']
        });

        if (!permit_request) {
            sails.log.error(`Invalid request, permit request not found for id: ${prId}.`);
            return errorResponse(res, 'Invalid request, permit request not found.');
        }

        let signWithMetaInfo = await attachMetaInfoWithSignature(req, permitTemplate.signatures, requestBody.signature, permit_request.signatures);
        if (!signWithMetaInfo || !signWithMetaInfo.sign_ref) {
            sails.log.error(signWithMetaInfo);
            return sendResponse(res, signWithMetaInfo);
        }
        let signOff = (permitConfig.sign_off || []).find(sign => sign.state === permit_request.state && sign.signatories.includes(signWithMetaInfo.signatory_user_ref));
        if (!signOff && !permitConfig.master_permit_managers.includes(signWithMetaInfo.signatory_user_ref)) {
            sails.log.info('Invalid Request, the signatory is not authorise to sign-off the permit.', prId);
            return errorResponse(res, 'Invalid Request, the signatory is not authorise to sign-off the permit.');
        }

        let nextSignOff = permitConfig.sign_off[permit_request.signatures.length + 1];
        requestBody.state = (permitTemplate.signatures.length === permit_request.signatures.length + 1) ? FINAL_STATE : nextSignOff.state; //set state for next sign-off
        requestBody.status = permit_request.status;
        if (requestBody.state === FINAL_STATE) {
            requestBody.status = (permitTemplate.require_closeout) ? PERMIT_STATUS.CLOSED_OUT : isActiveOrApproved(permit_request.start_on, permitTemplate, permit_request);
        } else if (requestBody.state < FINAL_STATE && (permitTemplate.signatures.length > (permit_request.signatures.length + 1))) {
            requestBody.state = nextSignOff.state;
            if (requestBody.state >= CLOSEOUT_REQUESTOR_STATE) {
                requestBody.status = isActiveOrApproved(permit_request.start_on, permitTemplate, permit_request);
                sails.log.info(`Permit request has been approved, id: ${permit_request.id}`);
            }
        }

        let usersToNotifyIds = requestBody.users_to_notify || [];
        let {sendToUsers, usersString} = await getUsersInfo([...usersToNotifyIds, permit_request.requestor_ref]);
        let comment = (usersString.length) ? `${usersString} was selected to sign-off` : '';
        let action = ([PERMIT_STATUS.APPROVED, PERMIT_STATUS.ACTIVE, PERMIT_STATUS.REGISTER_PENDING].includes(requestBody.status)) ? 'approved': 'signed-off';
        let note = (requestBody.action_type) ? requestBody.action_type : 'Signed-off';
        let status_log = prepareStatusLog(req, action, note, 'system', {label: 'Approved', color: '#54D3E8'}, comment, PERMIT_STATUS.APPROVED);
        requestBody.status_logs = [
            status_log,
            ...(permit_request.status_logs || [])
        ];

        requestBody.signatures = [
            ...(permit_request.signatures || []),
            signWithMetaInfo
        ];
        delete requestBody.signature;

        requestBody.pdf_fields_value = [...(permit_request.pdf_fields_value || []), ...(requestBody.pdf_fields_value || [])];
        requestBody.mandatory_attachments = [...(permit_request.mandatory_attachments || []), ...(requestBody.mandatory_attachments || [])];

        let {validationError} = signOffRequestValidator(requestBody);
        if(validationError) {
            return errorResponse(res, 'Invalid Request.', {validationError});
        }

        if([PERMIT_STATUS.APPROVED, PERMIT_STATUS.ACTIVE].includes(requestBody.status)) {
            requestBody.expire_on = (permitTemplate.expire_in) ? getExpireOnEpoch(permitTemplate.expire_in, permit_request.start_on) : null;
        }

        sails.log.info(`Permit Request ${prId} Current Status: ${permit_request.status} and State: ${permit_request.state}.`, permit_request.signatures);

        permit_request = await sails.models.permitrequest.updateOne({
            id: prId
        }).set(requestBody);

        sails.log.info(`Permit Request ${prId} Updated Status: ${permit_request.status} and State: ${permit_request.state}.`, permit_request.signatures);

        //activate the permit through scheduler
        if ([PERMIT_STATUS.APPROVED, PERMIT_STATUS.REGISTER_PENDING].includes(permit_request.status)) {
            await scheduleEvent(permit_request.id, 'permit', CONSTANT_ACTIVE_PERMIT_FN_ARN, permit_request.start_on, 'active');
        }

        //schedule to expire permit
        if ([PERMIT_STATUS.APPROVED, PERMIT_STATUS.REGISTER_PENDING, PERMIT_STATUS.ACTIVE].includes(permit_request.status) && permit_request.expire_on) {
            let beforePermitExpire = dayjs(+permit_request.expire_on).subtract(30, 'minutes').valueOf();
            await scheduleEvent(permit_request.id, 'permit', CONSTANT_NOTIFY_BEFORE_PERMIT_EXPIRE_FN_ARN, beforePermitExpire, 'before_expire', false);

            await scheduleEvent(permit_request.id, 'permit', CONSTANT_MARK_PERMIT_EXPIRE_FN_ARN, permit_request.expire_on, 'expire', false);
        }

        let project = await sails.models.project_reader.findOne({
            where: {id: permit_request.project_ref},
            select: ['name', 'custom_field']
        });
        let tz = await getProjectTimezone(project);

        let usersInductionInfo = await getInductionEmployerByUserIds(_uniq([permit_request.requestor_ref, req.user.id, ...(usersToNotifyIds || [])]), project.id, [2, 6]);
        let signedByInductionInfo = usersInductionInfo.find(ir => ir.user_ref === req.user.id);
        let requestorInductionInfo = usersInductionInfo.find(ir => ir.user_ref === permit_request.requestor_ref);

        let permit_title = permitTemplate.permit_type;
        //notification: To next signatories
        let nextSignatoriesInfo = sendToUsers.filter(u => u.id != permit_request.requestor_ref);
        if (nextSignatoriesInfo && nextSignatoriesInfo.length) {
            let mail_title = `Permit Sign-off Request (${permit_title}): ${project.name}`;
            let message = `You have been assigned a permit sign-off request @ ${project.name}`;
            await sendMailToUsers(permitTemplate, permit_request, nextSignatoriesInfo, project, {
                mail_title,
                body_text: message,
                assigned_by: `${signedByInductionInfo.user_name} (${signedByInductionInfo.user_employer})`,
                permit_requestor: `${requestorInductionInfo.user_name} (${requestorInductionInfo.user_employer})`,
            });

            await notifyAppUsers (req, message, mail_title, project, permit_request, nextSignatoriesInfo, NOTIFICATION_CATEGORY.PERMIT_TO_SIGNOFF);
        }

        //notification: To Requester
        let requestorInfo = sendToUsers.filter(u => u.id === permit_request.requestor_ref);
        if ([PERMIT_STATUS.ACTIVE, PERMIT_STATUS.APPROVED].includes(permit_request.status)) {
            let message = `Your permit request has been APPROVED @ ${project.name}`;
            let mail_title = `Permit Request Approved (${permitTemplate.permit_type}): ${project.name}`;
            await sendMailToUsers(permitTemplate, permit_request, requestorInfo, project, {
                mail_title,
                body_text: message
            });
            await notifyAppUsers (req, message, mail_title, project, permit_request, requestorInfo, NOTIFICATION_CATEGORY.PERMIT_SIGNED_OFF, {});

            if(PERMIT_STATUS.ACTIVE === permit_request.status) {
                let message =  `Your permit is now active @ ${project.name}`;
                let mail_title = `Permit Request Active (${permitTemplate.permit_type}): ${project.name}`;
                let additional = { permit_expiry_datetime: dayjs(+permit_request.expire_on).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) };
                await sendMailToUsers(permitTemplate, permit_request, requestorInfo, project, {
                    mail_title,
                    body_text: message,
                    ...additional
                });
                await notifyAppUsers (req, message, mail_title, project, permit_request, requestorInfo, NOTIFICATION_CATEGORY.PERMIT_SIGNED_OFF, {});
            }
        } else if (permit_request.status === PERMIT_STATUS.REGISTER_PENDING) {
            let message = `Your permit request has been APPROVED @ ${project.name}. You will still need to take a register before the permit becomes active. Before starting work, open up the permit in 'My Permits' to take the register.`;
            let mail_title = `Permit Request APPROVED - Register Pending (${permitTemplate.permit_type}): ${project.name}`;
            await sendMailToUsers(permitTemplate, permit_request, requestorInfo, project, {
                mail_title,
                body_text: message,
            });
            await notifyAppUsers (req, message, mail_title, project, permit_request, requestorInfo, NOTIFICATION_CATEGORY.PERMIT_SIGNED_OFF, {});
        } else {
            let {total_expected_signoff, signoff_count} = getSignOffCount(permitTemplate, permit_request);
            let message = `The permit that you requested has passed sign-off step ${signoff_count} of ${total_expected_signoff} @ ${project.name}`;
            let mail_title = `Permit Request Update (${permitTemplate.permit_type}): ${project.name}`;
            await sendMailToUsers(permitTemplate, permit_request, requestorInfo, project, {
                mail_title,
                body_text: message,
                sign_off_by: `${signedByInductionInfo.user_name} (${signedByInductionInfo.user_employer})`,
                sign_off_step_assign_to: `Step ${signoff_count + 1} of ${total_expected_signoff} Assigned to ${usersString}`
            });

            await notifyAppUsers (req, message, mail_title, project, permit_request, requestorInfo, NOTIFICATION_CATEGORY.PERMIT_SIGNED_OFF, {});
        }

        sails.log.info(`Permit request sign-off has been updated successfully.`)
        return successResponse(res, {permit_request});
    },

    closeoutSignOffPermitRequest:  async (req, res) => {
        let prId = +req.param('id');

        let requestBody = _.pick((req.body || {}), [
            'signature',
            'users_to_notify',
            'pdf_fields_value',
            'mandatory_attachments',
        ]);

        let permit_request = await sails.models.permitrequest_reader.findOne({
            where: {id: prId},
            select: ['signatures', 'status_logs', 'status', 'state', 'config_ref', 'permit_ref', 'start_on', 'project_ref', 'requestor_ref', 'pdf_fields_value', 'mandatory_attachments', 'createdAt', 'has_register']
        });

        if(!requestBody.signature || !requestBody.signature.sign || ![PERMIT_STATUS.CLOSEOUT_REQUESTED].includes(permit_request.status) || permit_request.state <= SIGNOFF_FINAL_STATE) {
            sails.log.error('Invalid sign off request.', `Permit ${prId} status: ${permit_request.status}, Permit state: ${permit_request.state}`);
            return errorResponse(res, 'Invalid sign off request.');
        }

        let permitConfig = await sails.models.projectpermitconfig_reader.findOne({
            where: {id: +permit_request.config_ref},
            select: ['sign_off', 'master_permit_managers']
        });

        let permitTemplate = await sails.models.permittemplate_reader.findOne({
            where: {id: +permit_request.permit_ref},
            select: ['signatures', 'permit_type', 'expire_in', 'require_closeout', 'take_register_when']
        });

        if (!permit_request) {
            sails.log.error(`Invalid request, permit request not found for id: ${prId}.`);
            return errorResponse(res, 'Invalid request, permit request not found.');
        }

        let signWithMetaInfo = await attachMetaInfoWithSignature(req, permitTemplate.signatures, requestBody.signature, permit_request.signatures);
        if (!signWithMetaInfo || !signWithMetaInfo.sign_ref) {
            sails.log.error(signWithMetaInfo);
            return sendResponse(res, signWithMetaInfo);
        }
        let signOff = (permitConfig.sign_off || []).find(sign => sign.state === permit_request.state && sign.signatories.includes(signWithMetaInfo.signatory_user_ref));
        if (!signOff && !permitConfig.master_permit_managers.includes(signWithMetaInfo.signatory_user_ref)) {
            sails.log.info('Invalid Request, the signatory is not authorise to sign-off the permit.', prId);
            return errorResponse(res, 'Invalid Request, the signatory is not authorise to sign-off the permit.');
        }

        let nextSignOff = permitConfig.sign_off[permit_request.signatures.length + 1];
        requestBody.state = (permitTemplate.signatures.length === permit_request.signatures.length + 1) ? FINAL_STATE : nextSignOff.state; //set state for next sign-off
        requestBody.status = permit_request.status;
        if (requestBody.state === FINAL_STATE) {
            requestBody.status = PERMIT_STATUS.CLOSED_OUT;
        } else if (requestBody.state < FINAL_STATE && (permitTemplate.signatures.length > (permit_request.signatures.length + 1))) {
            requestBody.state = nextSignOff.state;
        }

        let usersToNotifyIds = requestBody.users_to_notify || [];
        let {sendToUsers, usersString} = await getUsersInfo([...usersToNotifyIds, permit_request.requestor_ref]);
        let comment = (usersString.length) ? `${usersString} was selected to closeout sign-off` : '';
        let action = (PERMIT_STATUS.CLOSED_OUT === requestBody.status) ? 'closed-out': 'closeout-signed-off';
        let note = 'Closeout Signed-off';
        let status_log = prepareStatusLog(req, action, note, 'system', permit_status_meta[9], comment, PERMIT_STATUS.CLOSEOUT_REQUESTED);
        requestBody.status_logs = [
            status_log,
            ...(permit_request.status_logs || [])
        ];

        requestBody.signatures = [
            ...(permit_request.signatures || []),
            signWithMetaInfo
        ];
        delete requestBody.signature;

        requestBody.pdf_fields_value = [...(permit_request.pdf_fields_value || []), ...(requestBody.pdf_fields_value || [])];
        requestBody.mandatory_attachments = [...(permit_request.mandatory_attachments || []), ...(requestBody.mandatory_attachments || [])];

        let {validationError} = signOffRequestValidator(requestBody);
        if(validationError) {
            return errorResponse(res, 'Invalid Request.', {validationError});
        }

        permit_request = await sails.models.permitrequest.updateOne({
            id: prId
        }).set(requestBody);

        let project = await sails.models.project_reader.findOne({
            where: {id: permit_request.project_ref},
            select: ['name', 'custom_field', 'project_number', 'parent_company']
        });

        if(permit_request.state === FINAL_STATE || permit_request.status === PERMIT_STATUS.CLOSED_OUT) {
            let hasAsite = await checkIfAsiteEnabled(permit_request.project_ref);
            if (hasAsite) {
                sails.log.info(`Project has asite enabled trying to sync permit document for project ${permit_request.project_ref}`);

                let userEmployer = await getUserInductionEmployer({id: permit_request.requestor_ref}, permit_request.project_ref);
                // Check if user's induction employer not there, Load employer from profile data.
                if(!userEmployer) {
                    let [pReq] = await populateUserRefs([permit_request], 'requestor_ref', ['parent_company']);
                    userEmployer = pReq.requestor_ref.parent_company;
                }
                // Not awaiting syncClosedPermitReqToAsite fn, to run the PDF generation and asite upload in backend.
                let localsObj = Object.assign({}, res.locals); // Cloning the res.locals, as the object gets cleared after the API response sent.
                syncClosedPermitReqToAsite(req, res, project, permit_request.id, localsObj, userEmployer).catch(sails.log.error);
            }
        }

        let tz = await getProjectTimezone(project);

        let usersInductionInfo = await getInductionEmployerByUserIds(_uniq([permit_request.requestor_ref, req.user.id, ...(usersToNotifyIds || [])]), project.id, [2, 6]);
        let signedByInductionInfo = usersInductionInfo.find(ir => ir.user_ref === req.user.id);
        let requestorInductionInfo = usersInductionInfo.find(ir => ir.user_ref === permit_request.requestor_ref);

        let permit_title = permitTemplate.permit_type;
        //notification: To next signatories
        let nextSignatoriesInfo = sendToUsers.filter(u => u.id != permit_request.requestor_ref);
        if (nextSignatoriesInfo && nextSignatoriesInfo.length) {
            let mail_title = `Permit Closeout Sign-off Request (${permit_title}): ${project.name}`;
            let message = `You have been assigned a permit closeout sign-off request @ ${project.name}`;
            await sendMailToUsers(permitTemplate, permit_request, nextSignatoriesInfo, project, {
                mail_title,
                body_text: message,
                assigned_by: `${signedByInductionInfo.user_name} (${signedByInductionInfo.user_employer})`,
                permit_requestor: `${requestorInductionInfo.user_name} (${requestorInductionInfo.user_employer})`,
            });

            await notifyAppUsers (req, message, mail_title, project, permit_request, nextSignatoriesInfo, NOTIFICATION_CATEGORY.PERMIT_CLOSEOUT_SIGNED_OFF);
        }

        //notification: To Requester
        let requestorInfo = sendToUsers.filter(u => u.id === permit_request.requestor_ref);
        if (PERMIT_STATUS.CLOSED_OUT === permit_request.status) {
            let message = `Your permit request has been Closed out @ ${project.name}`;
            let mail_title = `Permit Request Closed Out (${permitTemplate.permit_type}): ${project.name}`;
            await sendMailToUsers(permitTemplate, permit_request, requestorInfo, project, {
                mail_title,
                body_text: message
            });
            await notifyAppUsers (req, message, mail_title, project, permit_request, requestorInfo, NOTIFICATION_CATEGORY.PERMIT_CLOSEOUT_SIGNED_OFF, {});
        } else {
            let {total_expected_signoff, signoff_count} = getCloseoutSignOffCount(permitTemplate, permit_request);
            let message = `The permit that you requested has passed closeout sign-off step ${signoff_count} of ${total_expected_signoff} @ ${project.name}`;
            let mail_title = `Permit Request Update (${permitTemplate.permit_type}): ${project.name}`;
            await sendMailToUsers(permitTemplate, permit_request, requestorInfo, project, {
                mail_title,
                body_text: message,
                sign_off_by: `${signedByInductionInfo.user_name} (${signedByInductionInfo.user_employer})`,
                sign_off_step_assign_to: `Step ${signoff_count + 1} of ${total_expected_signoff} Assigned to ${usersString}`
            });

            await notifyAppUsers (req, message, mail_title, project, permit_request, requestorInfo, NOTIFICATION_CATEGORY.PERMIT_CLOSEOUT_SIGNED_OFF, {});
        }

        sails.log.info(`Permit request closeout sign-off has been updated successfully.`)
        return successResponse(res, {permit_request});
    },

    getPermitRequestsByRequestor: async (req, res) => {
        let userId = req.user.id;
        let projectId = +req.param('projectId');
        let searchTerm = (req.param('q', '')).toString().trim();
        let statusFilter = (req.param('status') && req.param('status').toLowerCase() != 'null') ? req.param('status').split(",").map(a=>+a): [];
        let permitTypeFilter = (req.param('permit_type') && req.param('permit_type').toLowerCase() != 'null') ? req.param('permit_type').split(",").map(a=>a): [];
        let pageNumber = +req.param('pageNumber', 0);
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        sails.log.info(`Fetching permit requests submitted by user ${userId} on project ${projectId}, pageNumber: ${pageNumber}, pageSize: ${pageSize}.`);

        let defaultResponse = {
            permit_requests: [],
            q: searchTerm,
            pageSize,
            pageNumber,
            totalCount: 0,
        };
        let {
            records: permit_requests,
            total: totalCount,
            permit_types
        } = await getPaginatePermitRequests(projectId, userId, statusFilter, permitTypeFilter, searchTerm, pageSize, (pageSize * pageNumber));

        permit_requests = permit_requests.map(permitRequest => {
            permitRequest.status_meta = getPermitRequestStatusLabel(permitRequest.status);
            return permitRequest;
        });

        sails.log.info(`found ${permit_requests.length} permit requests by user ${userId} on project ${projectId}.`);
        return successResponse(res, {...defaultResponse, permit_requests, totalCount, permit_types, permit_status_meta});
    },

    getPermitRequestsByProject: async (req, res) => {
        let projectId = +req.param('projectId');
        let searchTerm = (req.param('q', '')).toString().trim();
        let statusFilter = (req.param('status') && req.param('status').toLowerCase() != 'null') ? req.param('status').split(",").map(a => +a): [];
        let permitTypeFilter = (req.param('permit_type') && req.param('permit_type').toLowerCase() != 'null') ? req.param('permit_type').split(",") : [];
        let pageNumber = +req.param('pageNumber', 0);
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        sails.log.info(`Fetching permit requests submitted on project ${projectId}, pageNumber: ${pageNumber}, pageSize: ${pageSize}.`);
        let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, projectId);
        sails.log.info("getPermitRequestsByProject hasOnlySiteManagement: ", hasOnlySiteManagement);
        let userEmployer = null;
        if (hasOnlySiteManagement) {
            userEmployer = await getUserInductionEmployer(req.user, projectId, [], 'id');
        }

        let defaultResponse = {
            permit_requests: [],
            q: searchTerm,
            pageSize,
            pageNumber,
            totalCount: 0,
        };

        if(hasOnlySiteManagement && !userEmployer) {
            sails.log.info(`User  ${req.user.id} has site management access only with no induction on project ${projectId}`);
            return successResponse(res, {...defaultResponse, permit_status_meta});
        }

        let {
            records: permit_requests,
            total: totalCount,
            permit_types
        } = await getPaginatePermitRequests(projectId, '', statusFilter, permitTypeFilter, searchTerm, pageSize, (pageSize * pageNumber), userEmployer);

        permit_requests = permit_requests.map(permitRequest => {
            permitRequest.status_meta = getPermitRequestStatusLabel(permitRequest.status);
            return permitRequest;
        });

        sails.log.info(`found ${permit_requests.length} permit requests on project ${projectId}.`);
        return successResponse(res, {...defaultResponse, permit_requests, totalCount, permit_types, permit_status_meta});
    },

    getPermitRequestById: async (req, res) => {
        let prId = +req.param('prId');
        let projectId = +req.param('projectId');
        sails.log.info(`get permit request by id ${prId} on project ${projectId}`)
        let permit_request = await sails.models.permitrequest_reader.findOne({id: prId, project_ref: projectId})
            .populate('permit_ref');

        let fileIds = [];
        let signatoryUserIDs = [permit_request.requestor_ref];
        let signatoryIds = permit_request.signatures.map(sign => sign.signatory_user_ref);
        signatoryUserIDs.push(...signatoryIds);

        let mandatoryAttachmentsFileRefs = permit_request.mandatory_attachments.reduce((arr, attach) => {
            arr.push(...attach.attachment_file_refs);
            return arr;
        }, [])
        fileIds.push(...(permit_request.permit_ref.ref_docs || []), ...mandatoryAttachmentsFileRefs);

        permit_request.status_meta = getPermitRequestStatusLabel(permit_request.status);

        let files = [];
        if (fileIds.length) {
            files = await fetchFileDataByIds(_uniq(fileIds));
        }

        let usersInfo = [];
        if (signatoryUserIDs.length) {
            usersInfo = await getInductionEmployerByUserIds(_uniq(signatoryUserIDs), projectId, [6, 2, 4, 5], true);
        }

        permit_request.requestor_ref.user_induction_info = usersInfo.find(user => user.user_ref === permit_request.requestor_ref);
        permit_request.signatures = permit_request.signatures.map(sign => {
            sign.signatory_user_ref = usersInfo.find(user => user.user_ref === sign.signatory_user_ref);
            return sign;
        });

        if (permit_request.permit_ref.ref_docs && permit_request.permit_ref.ref_docs.length) {
            permit_request.permit_ref.ref_docs = files.filter(file => (permit_request.permit_ref.ref_docs).includes(file.id));
        }

        permit_request.mandatory_attachments = permit_request.mandatory_attachments.map(attach => {
            attach.attachment_file_refs = files.filter(file => attach.attachment_file_refs.includes(file.id));
            return attach;
        })

        return successResponse(res, {permit_request, permit_status_meta});
    },

    fetchPermitSignOffUsers: async (req, res) => {
        let projectId = +req.param('projectId');
        let configId = +req.param('configId');

        sails.log.info(`Get sign-off users from project permit config: ${configId}`);

        let permitTemplatesConfig = await sails.models.projectpermitconfig_reader.findOne({
            where: {id: configId, is_active: true}
        });

        if (!permitTemplatesConfig) {
            sails.log.error(`Something went wrong, permit configuration not found for id : ${configId}.`);
            return errorResponse(res, `Something went wrong, permit configuration not found for id : ${configId}.`);
        }

        let userIds = [...permitTemplatesConfig.master_permit_managers];
        for (let i=0; i < permitTemplatesConfig.sign_off.length; i++) {
            let item = permitTemplatesConfig.sign_off[i];
            if (item.is_requestor || item.is_closeout) {
                continue;
            }
            userIds.push(...item.signatories);
            break;
        }

        let sign_off_users = await prepareSignOffUsers(projectId, userIds);

        sails.log.info(`Found ${sign_off_users.length} to sign-off.`);
        return successResponse(res, {sign_off_users});
    },

    permitRequestsToSignOff: async (req, res) => {
        let userId = req.user.id;
        let projectId = +req.param('projectId');
        let reqType = (req.param('type', '')).toString().trim()
        sails.log.info(`Fetching permit requests to sign-off for user ${userId} on project ${projectId}.`);
        let configRecords = await getPermitConfigBySignOffUser(projectId, userId);
        sails.log.info(`Found ${configRecords.length} permit config records.`);

        let permit_requests = [];
        let permitConfigIds = [];
        for (let i=0; i < configRecords.length; i++) {
            let record = configRecords[i];
            let where = {
                project_ref: projectId,
                permit_ref: record.permit_ref,
                status: (reqType === 'closeout') ? [PERMIT_STATUS.ACTIVE, PERMIT_STATUS.EXPIRED, PERMIT_STATUS.CLOSEOUT_REQUESTED] : [PERMIT_STATUS.PENDING]
            }

            if (reqType === 'closeout') {
                where.closeout_requested = true;
                where.state = {'>=': CLOSEOUT_INITIAL_STATE, '<': FINAL_STATE}
            } else {
                where.state = (record.master_permit_managers.includes(userId)) ? {'<=': SIGNOFF_FINAL_STATE} : record.state;
            }

            let permit_request = await sails.models.permitrequest_reader.find(where)
                .populate('permit_ref');

            if (permit_request && permit_request.length) {
                permit_requests.push(...permit_request);
                permitConfigIds.push(record.id);
            }
        }

        if (!permit_requests.length) {
            sails.log.info(`No permit requests found to sign-off for user ${userId} on project ${projectId}.`);
            return successResponse(res, {permit_requests});
        }

        permit_requests = [...new Map(permit_requests.map(item =>
            [item['id'], item])).values()];

        //Associate next sign-off users with permit request
        let signOffUsers = [];
        let signOffUserIds = [];
        let permitsConfig = await sails.models.projectpermitconfig_reader.find({
            where: {id: _uniq(permitConfigIds)},
            select: ['sign_off', 'master_permit_managers']
        });
        (permitsConfig || []).forEach(prc => {
            prc.sign_off.forEach(signOff => {
                let userIds = _uniq([...signOff.signatories, ...prc.master_permit_managers]);
                signOffUsers.push({
                    config_id: prc.id,
                    state: signOff.state,
                    user_ids: userIds
                });
                signOffUserIds.push(...userIds)
            });
        });

        let signOffUsersInfo = await prepareSignOffUsers(projectId, _uniq(signOffUserIds));

        for (let i=0; i < permit_requests.length; i++) {
            let pr = permit_requests[i];

            let {total_expected_signoff, signoff_count} = (reqType !== 'closeout') ? getSignOffCount(pr.permit_ref, pr) : getCloseoutSignOffCount(pr.permit_ref, pr);
            pr.total_expected_signoff = total_expected_signoff;
            pr.signoff_count = signoff_count;

            let config_ref = permitsConfig.find(config => config.id === pr.config_ref);
            pr.master_permit_managers = config_ref.master_permit_managers || [];

            let nextState = pr.state;
            if (nextState < FINAL_STATE && (config_ref.sign_off.length > pr.signatures.length)) {
                let nextSignOff = config_ref.sign_off[pr.signatures.length];
                nextState = nextSignOff.state;
            }
            sails.log.info(`Current state is ${pr.state} for permit request ${pr.id} and next state is ${nextState}.`);

            let matchedConfig = signOffUsers.find(record => (record.config_id === pr.config_ref && record.state === nextState));
            pr.next_signoff_users = [];
            //next signoff users will be empty for last sign-off
            if (matchedConfig && (pr.total_expected_signoff - pr.signoff_count) > 1) {
                pr.next_signoff_users = signOffUsersInfo.filter(ui => matchedConfig.user_ids.includes(ui.id));
            }

            pr.signatures = (pr.signatures).map(sign => {
                sign.signatory_user_ref = signOffUsersInfo.find(user => user.id === sign.signatory_user_ref);
                return sign;
            });

            permit_requests[i] = pr;
        }

        permit_requests = await populateAttachmentFileRefs(permit_requests, 'mandatory_attachments', 'attachment_file_refs');
        permit_requests = await populateUserFileRefs(permit_requests, ['name', 'file_url']);
        permit_requests = await populateUserRefs(permit_requests, 'requestor_ref', ['first_name', 'last_name']);

        sails.log.info(`found ${permit_requests.length} permit requests to sign-off for user ${userId} on project ${projectId}.`);
        return successResponse(res, {permit_requests});
    },

    permitRequestAction: async (req, res) => {
        let prId = +req.param('id');
        let actionType = req.param('action');
        let requestBody = _.pick((req.body || {}), [
            'signature',
            'comment',
        ]);
        let comment = requestBody.comment || '';

        if (!Object.keys(action_status).includes(actionType)) {
            sails.log.error(`Invalid permit request action: ${actionType}.`);
            return errorResponse(res, 'Invalid request.');
        }

        let isCloseoutRequest = (actionType === 'closeout');
        if (isCloseoutRequest && !requestBody.signature && !requestBody.signature.sign) {
            sails.log.error('Invalid closeout sign off request.');
            return errorResponse(res, 'Invalid closeout sign off request.');
        }

        let permit_request = await sails.models.permitrequest_reader.findOne({
            where: {id: prId},
            select: ['status_logs', 'signatures',  'status', 'state', 'start_on', 'permit_ref', 'config_ref', 'project_ref', 'requestor_ref', 'pdf_fields_value', 'createdAt']
        });

        if (!permit_request) {
            sails.log.error(`Invalid request, permit request not found for id: ${prId}.`);
            return errorResponse(res, 'Invalid request, permit request not found.');
        }

        let permitConfig = {};
        if (actionType === 'change-request' || actionType === 'cancelled') {
            permitConfig = await sails.models.projectpermitconfig_reader.findOne({
                where: {id: permit_request.config_ref},
                select: ['sign_off', 'master_permit_managers']
            });
        }

        if (actionType === 'cancelled' && (!permitConfig.master_permit_managers.includes(req.user.id) || ![PERMIT_STATUS.APPROVED, PERMIT_STATUS.EXPIRED, PERMIT_STATUS.ACTIVE].includes(permit_request.status))) {
            sails.log.error(`Invalid cancelled request for permit: ${prId}, user: ${req.user.id}`);
            return errorResponse(res, 'Invalid cancelled request for permit.');
        }

        let permitTemplate = await sails.models.permittemplate_reader.findOne({
            where: {id: permit_request.permit_ref},
            select: ['permit_type', 'signatures']
        });

        if (isCloseoutRequest) {
            let signWithMetaInfo = await attachMetaInfoWithSignature(req, permitTemplate.signatures, requestBody.signature, permit_request.signatures);
            if (!signWithMetaInfo || !signWithMetaInfo.sign_ref) {
                sails.log.error(signWithMetaInfo);
                return sendResponse(res, signWithMetaInfo);
            }
            permit_request.signatures = [
                ...(permit_request.signatures || []),
                signWithMetaInfo
            ];
            delete requestBody.signature;
        }

        let state = (isCloseoutRequest || actionType === 'cancelled') ? FINAL_STATE : permit_request.state;
        if(actionType === 'change-request') {
            state = SIGNOFF_INITIAL_STATE;
            let permitConfig = await sails.models.projectpermitconfig_reader.findOne({
                where: {id: permit_request.config_ref},
                select: ['sign_off']
            });

            let hasRequestorSign = permitConfig.sign_off.find(sign => sign.is_requestor);
            state = (hasRequestorSign) ? 20 : state;
        }

        let note = 'Closed Out';
        if (actionType === 'reject') {
            note = 'Rejected';
        } else if (actionType === 'cancelled') {
            note = 'Cancelled';
        } else if (actionType === 'change-request') {
            note = 'Change Requested';
        }

        let status_log = prepareStatusLog(req, actionType, note, 'user', getPermitRequestStatusLabel(status_vs_label[actionType]), comment, status_vs_label[actionType]);
        sails.log.info(`Permit Request ${prId} Current Status: ${permit_request.status} and State: ${permit_request.state}.`);
        permit_request = await sails.models.permitrequest.updateOne({
            id: prId
        }).set({
            status_logs: [
                status_log,
                ...(permit_request.status_logs || [])
            ],
            status: status_vs_label[actionType],
            state,
            signatures: (actionType === 'change-request') ? [] : permit_request.signatures
        });
        sails.log.info(`Permit Request ${prId} Updated Status: ${permit_request.status} and State: ${permit_request.state}.`);


        //notification: To Requester
        let sendToUser = await sails.models.user_reader.findOne({
            where: {id: permit_request.requestor_ref},
            select: ['first_name', 'last_name', 'email']
        });

        let project = await sails.models.project_reader.findOne({
            where: {id: permit_request.project_ref},
            select: ['name', 'custom_field', 'project_number', 'parent_company']
        });
        let tz = await getProjectTimezone(project);

        let action_type = actionType;
        let action_by_user = getUserFullName(req.user);
        let recipientsInfo = [{id: sendToUser.id, name: getUserFirstName(sendToUser), email: sendToUser.email}];
        let message = ``;
        let actionLabel = '';
        let additionalInfo = {};
        if (actionType === 'reject') {
            actionLabel = 'REJECTED';
            message = `Your permit request has been REJECTED @ ${project.name}`;
            additionalInfo.reject_comment = comment;
        } else if (actionType === 'change-request') {
            actionLabel = 'Change Requested';
            message = `A change request has been raised on your permit request @ ${project.name}`;
            additionalInfo.change_request_comment = comment;
        } else if (actionType === 'cancelled') {
            let userEmployer = await getUserInductionEmployer({id: req.user.id}, permit_request.project_ref, [2,6],  'name');
            actionLabel = 'CANCELLED';
            message = `Your permit request has been CANCELLED @ ${project.name}`;
            additionalInfo.datetime_cancelled_at = dayjs().tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss);
            additionalInfo.cancelled_by = `${action_by_user}(${userEmployer})`;
            additionalInfo.cancelled_comment = comment;
        } else {
            actionLabel = 'Closeout APPROVED';
            message = `Your permit closeout request has been APPROVED @ ${project.name}`;
            additionalInfo.datetime_closeout_request = dayjs().tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss);
            additionalInfo.permit_expiry_datetime = dayjs(+permit_request.expire_on).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss);

            let hasAsite = await checkIfAsiteEnabled(project.id);
            if (hasAsite) {
                sails.log.info(`Project has asite enabled trying to sync permit document for project ${project.id}`);

                let userEmployer = await getUserInductionEmployer({id: permit_request.requestor_ref}, permit_request.project_ref);
                // Check if user's induction employer not there, Load employer from profile data.
                if(!userEmployer) {
                    let [pReq] = await populateUserRefs([permit_request], 'requestor_ref', ['parent_company']);
                    userEmployer = pReq.requestor_ref.parent_company;
                }
                // Not awaiting syncClosedPermitReqToAsite fn, to run the PDF generation and asite upload in backend.
                let localsObj = Object.assign({}, res.locals); // Cloning the res.locals, as the object gets cleared after the API response sent.
                syncClosedPermitReqToAsite(req, res, project, permit_request.id, localsObj, userEmployer).catch(sails.log.error);
            }
        }

        let mail_title = `Permit Request ${actionLabel} (${permitTemplate.permit_type}): ${project.name}`;
        await sendMailToUsers(permitTemplate, permit_request, recipientsInfo, project, {
            mail_title,
            body_text: message,
            ...additionalInfo
        });

        additionalInfo = {
            action_type,
            action_by_user,
            comment
        }
        await notifyAppUsers (req, message, mail_title, project, permit_request, recipientsInfo, NOTIFICATION_CATEGORY.PERMIT_REQUEST_ACTION, additionalInfo);

        sails.log.info(`Permit request status has been updated successfully to ${permit_request.status}`)
        return successResponse(res, {permit_request});
    },

    requestCloseoutNotify: async (req, res) => {
        let id = +req.param('id');
        let projectId = +req.param('projectId');
        sails.log.info(`request closeout for permit request ${id} on project ${projectId} by ${req.user.id}`);

        let requestBody = _.pick((req.body || {}), [
            'users_to_notify',
            'pdf_fields_value',
            'sign', //closeout requestor sign
            'mandatory_attachments',
        ]);

        if (!requestBody.users_to_notify || !requestBody.users_to_notify.length) {
            sails.log.error(`Recipients for request closeout not found.`);
            return errorResponse(res, 'Recipients for request closeout not found.');
        }

        //notification: To selected signatories
        let permitRequest = await sails.models.permitrequest_reader.findOne({
            where: {id: +id},
            select: ['id', 'requestor_ref', 'status', 'state', 'permit_ref', 'config_ref', 'signatures', 'status_logs', 'pdf_fields_value', 'mandatory_attachments']
        });

        let permitTemplate = await sails.models.permittemplate_reader.findOne({
            where: {id: permitRequest.permit_ref},
            select: ['permit_type', 'signatures', 'require_closeout']
        });

        if (permitTemplate.id && !permitTemplate.require_closeout) {
            sails.log.error('No closeout required for this permit request.');
            return errorResponse(res, 'No closeout required for this permit request.');
        }

        if(permitRequest.status === PERMIT_STATUS.CLOSEOUT_REQUESTED) {
            sails.log.error(`Closeout has already been requested for this permit, State: ${permitRequest.state}`);
            return errorResponse(res, 'Closeout has already been requested for this permit.');
        }

        //CLOSEOUT_FINAL_STATE check can be remove later
        if (permitRequest.state !== CLOSEOUT_REQUESTOR_STATE && ![CLOSEOUT_INITIAL_STATE, CLOSEOUT_FINAL_STATE].includes(permitRequest.state)) {
            sails.log.error(`Invalid request to closeout, Current Status: ${permitRequest.status} and State: ${permitRequest.state}`);
            return errorResponse(res, 'Invalid request to closeout.');
        }

        let {validationError} = requestCloseoutNotifyValidator(requestBody);
        if(validationError) {
            return errorResponse(res, 'Invalid Request.', {validationError});
        }

        let permitConfig = await sails.models.projectpermitconfig_reader.findOne({
            where: {id: permitRequest.config_ref},
            select: ['sign_off']
        });

        let payload = {
            status: PERMIT_STATUS.CLOSEOUT_REQUESTED
        };
        if (permitRequest.state === CLOSEOUT_REQUESTOR_STATE) {
            let nextSignOff = permitConfig.sign_off[permitRequest.signatures.length + 1];
            payload.state = nextSignOff.state;
        }

        let closeoutRequestor = permitTemplate.signatures.find(sign => sign.is_closeout_requestor);
        if (closeoutRequestor && closeoutRequestor.field_name) {
            if (!requestBody.sign || permitRequest.requestor_ref != req.user.id) {
                sails.log.error(`Invalid sign for request to closeout.`);
                return errorResponse(res, 'Invalid sign for request to closeout.');
            }

            let sign_ref = await saveUserSign(req, requestBody.sign);
            payload.signatures = [
                ...(permitRequest.signatures || []),
                {
                    "field_name": closeoutRequestor.field_name,
                    "field_label": closeoutRequestor.field_label,
                    "link_fields": closeoutRequestor.link_fields,
                    "sign_number": closeoutRequestor.sign_number,
                    "is_requestor": closeoutRequestor.is_requestor,
                    "is_closeout_requestor": closeoutRequestor.is_closeout_requestor,
                    "is_closeout": closeoutRequestor.is_closeout,
                    "date_signed": dayjs().valueOf(),
                    "signatory_user_ref": req.user.id,
                    sign_ref
                }
            ];
        }

        let {sendToUsers, usersString} = await getUsersInfo(requestBody.users_to_notify);
        let comment = (usersString.length) ? `${usersString} was selected to closeout` : '';
        payload.closeout_requested = true;
        let status_log = prepareStatusLog(req, 'closeout-requested', 'Closeout Requested', 'system', getPermitRequestStatusLabel(PERMIT_STATUS.CLOSEOUT_REQUESTED), comment, PERMIT_STATUS.CLOSEOUT_REQUESTED);
        payload.status_logs = [
            status_log,
            ...permitRequest.status_logs
        ];
        payload.pdf_fields_value = [...(permitRequest.pdf_fields_value || []), ...(requestBody.pdf_fields_value || [])];
        payload.mandatory_attachments = [...(permitRequest.mandatory_attachments || []), ...(requestBody.mandatory_attachments || [])];
        sails.log.info(`Permit Request ${id} Current Status: ${permitRequest.status} and State: ${permitRequest.state}.`, permitRequest.signatures);
        permitRequest = await sails.models.permitrequest.updateOne({
            id: id
        }).set(payload);
        sails.log.info(`Permit Request ${id} Updated Status: ${permitRequest.status} and State: ${permitRequest.state}.`, permitRequest.signatures);
        let project = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['name', 'custom_field']
        });
        let tz = await getProjectTimezone(project);

        let usersInductionInfo = await getInductionEmployerByUserIds([permitRequest.requestor_ref], projectId, [2, 6], true);
        let requestorInductionInfo = usersInductionInfo.find(ir => ir.user_ref === permitRequest.requestor_ref);

        let message = `You have been assigned a permit closeout request @ ${project.name}`;
        let mail_title = `Permit Closeout Request (${permitTemplate.permit_type}): ${project.name}`;
        await sendMailToUsers(permitTemplate, permitRequest, sendToUsers, project, {
            mail_title,
            body_text: message,
            permit_requestor: `${requestorInductionInfo.user_name} (${requestorInductionInfo.user_employer})`,
            permit_expiry_datetime: dayjs(+permitRequest.expire_on).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss)
        });

        await notifyAppUsers (req, message, mail_title, project, permitRequest, sendToUsers, NOTIFICATION_CATEGORY.REQUEST_CLOSEOUT_PERMIT);
        sails.log.info(`Request to closeout has been sent successfully to ${sendToUsers.length} users.`);

        return successResponse(res, {message: `Request to closeout has been sent successfully to ${sendToUsers.length} users.`});
    },

    getNextSignOffUsers: async (req, res) => {
        let prId = +req.param('id');
        let projectId = +req.param('projectId');
        sails.log.info(`get next sign-off users list for permit request ${prId} on project ${projectId} by ${req.user.id}`);

        let permitRequest = await sails.models.permitrequest_reader.findOne({
            where: {id: +prId},
            select: ['id', 'state', 'status', 'permit_ref', 'config_ref', 'signatures']
        });

        let permitTemplate = await sails.models.permittemplate_reader.findOne({
            where: {id: permitRequest.permit_ref},
            select: ['signatures']
        });

        let permitConfig = await sails.models.projectpermitconfig_reader.findOne({
            where: {id: permitRequest.config_ref},
            select: ['sign_off', 'master_permit_managers']
        });

        if (!permitRequest || !permitRequest.id || !permitConfig || !permitConfig.id) {
            sails.log.error(`permit request or associate config not found for permit request ${prId}.`);
            return errorResponse(res, `permit request or associate config not found for permit request ${prId}`);
        }

        let signOffUsers = [];
        let signOffUserIds = [];
        permitConfig.sign_off.forEach(signOff => {
            let userIds = _uniq([...signOff.signatories, ...permitConfig.master_permit_managers]);
            signOffUsers.push({
                config_id: permitConfig.id,
                state: signOff.state,
                user_ids: userIds
            });
            signOffUserIds.push(...userIds)
        });

        let signOffUsersInfo = await prepareSignOffUsers(projectId, _uniq(signOffUserIds));

        let nextState = permitRequest.state;
        if (nextState < FINAL_STATE && (permitConfig.sign_off.length > permitRequest.signatures.length)) {
            let nextSignOff = permitConfig.sign_off[permitRequest.signatures.length];
            nextState = nextSignOff.state;
        }
        sails.log.info(`Current state is ${permitRequest.state} for permit request ${permitRequest.id} and next state is ${nextState}.`);

        let matchedConfig = signOffUsers.find(record => (record.config_id === permitRequest.config_ref && record.state === nextState));
        permitRequest.next_signoff_users = [];
        if (matchedConfig) {
            permitRequest.next_signoff_users = signOffUsersInfo.filter(ui => matchedConfig.user_ids.includes(ui.id));
        }

        sails.log.info(`Found ${permitRequest.next_signoff_users.length} next sign-off users for permit request.`);
        return successResponse(res, {next_signoff_users: permitRequest.next_signoff_users});
    },

    viewPermitDocument: async (req, res) => {
        let prId = +req.param('id');
        let includeOperativeRegister = req.param('operative_register', 'false') === 'true';
        let attachmentType = req.param('attachment_type');
        const attachmentTypes = ['full', 'grid'];
        let filled_pdf;
        let permitRequest = await sails.models.permitrequest_reader.findOne({
            where: {id: prId},
            select: ['status_logs', 'signatures',  'status', 'state', 'start_on', 'expire_on', 'permit_ref', 'config_ref', 'project_ref', 'requestor_ref', 'pdf_fields_value', 'createdAt', 'has_register', 'mandatory_attachments', 'record_id']
        });

        let permitTemplate = await sails.models.permittemplate_reader.findOne({
            where: permitRequest.permit_ref,
            select: ['fillable_pdf_fields', 'field_sections', 'fillable_pdf_ref', 'permit_type', 'ref_number', 'take_register_when', 'font_size']
        }).populate('fillable_pdf_ref');

        [permitRequest] = await populateProjectRefs([permitRequest], 'project_ref', ['name', 'custom_field', 'parent_company', 'contractor', 'project_number']);

        // check for download then do parallel pdf generation
        if (attachmentType && attachmentTypes.includes(attachmentType) && permitRequest.mandatory_attachments && permitRequest.mandatory_attachments.length) {
            let [ filled_permit_pdf, attachment ] = await Promise.all([
                fillPermitPdf(req, res, permitRequest, permitTemplate, includeOperativeRegister),
                getPermitAttachments(req, res, permitRequest, permitTemplate, attachmentType)
            ]);

            //{ attachmentPdf, pdfAttachments };

            filled_pdf = filled_permit_pdf;

            if (attachment && (attachment.imageAttachmentPdf || (attachment.pdfAttachments && attachment.pdfAttachments.length))) {
                sails.log.info('viewPermitDocument Permit: got mandatory attachments');
                let pdfFiles = [filled_permit_pdf.file_url];
                if (attachment.imageAttachmentPdf) {
                    pdfFiles.push(attachment.imageAttachmentPdf);
                }
                pdfFiles.push(...attachment.pdfAttachments);

                const mergedFile = await mergePdfsViaUrls(req, res, pdfFiles, permitTemplate.fillable_pdf_ref.name);

                return successResponse(res, {
                    filled_pdf: {
                        file_name: mergedFile.name,
                        file_url:  mergedFile.location
                    }
                });
            }
        }

        // If(attachment) fallback from above return original permit request pdf
        if (!filled_pdf) {
            filled_pdf = await fillPermitPdf(req, res, permitRequest, permitTemplate, includeOperativeRegister);
        }
        if (!filled_pdf || !filled_pdf.file_url) {
            return errorResponse(res,'Something went wrong fetching permit document, please try later.');
        }
        return successResponse(res, {filled_pdf});
    },

    downloadPermitRequests: async (req, res) => {
        let prId = +req.param('id');
        let projectId = +req.param('projectId');
        sails.log.info(`Getting request to download register for permit request ${prId} on project ${projectId} by ${req.user.id}`);

        let permitRequests = await sails.models.permitrequest_reader.find({
            where: {project_ref: projectId},
            select: ['id', 'record_id', 'status', 'permit_ref', 'start_on', 'expire_on', 'createdAt', 'requestor_ref', 'status_logs', 'mandatory_attachments']
        });

        if (!permitRequests.length) {
            return errorResponse(res, 'No records available to export.');
        }

        permitRequests = await populateUserRefs(permitRequests, 'requestor_ref', ['id', 'first_name', 'last_name']);

        sails.log.info(`Found ${permitRequests.length} permit requests to download.`)

        let project = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['name']
        });

        let userIds = permitRequests.map(pr => pr.requestor_ref.id);
        let requestorsInfo = await getInductionEmployerByUserIds(_uniq(userIds), projectId, [6, 2, 4, 5], true);

        let permitTemplateIds = (permitRequests || []).map(pr => pr.permit_ref);
        let permitTemplates = await sails.models.permittemplate_reader.find({
            where: {id: _uniq(permitTemplateIds)},
            select: ['ref_number', 'permit_type', 'require_closeout']
        });

        let recordsToExport = [];
        for (let i = 0; i < permitRequests.length; i++) {
            let permitRequest = permitRequests[i];
            let permitTemplate = permitTemplates.find(pt => pt.id === permitRequest.permit_ref);
            let requestorInfo = requestorsInfo.find(requestor => requestor.user_ref === permitRequest.requestor_ref.id);
            let statusInfo = getPermitRequestStatusLabel(permitRequest.status);

            let closeoutInfo = {};
            if (permitRequest.status === PERMIT_STATUS.CLOSED_OUT) {
                closeoutInfo = (permitRequest.status_logs || []).find(log => log.action === 'closeout') || {};
            }
            recordsToExport.push({
                record_id: permitRequest.record_id,
                permit_type: `${permitTemplate.ref_number} - ${permitTemplate.permit_type}`,
                requested_at: dayjs(permitRequest.createdAt).format(displayDateFormat_DD_MM_YYYY),
                requested_by: getUserFullName(permitRequest.requestor_ref),
                requestor_company: requestorInfo.user_employer,
                status: statusInfo.label,
                start_date: dayjs(+permitRequest.start_on).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss),
                expiry_date: (+permitRequest.expire_on) ? dayjs(+permitRequest.expire_on).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : '',
                closeout_date: (+closeoutInfo.timestamp) ? dayjs(+closeoutInfo.timestamp).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : '',
                closeout_by: (closeoutInfo.name) ? closeoutInfo.name : '',
            });
        }

        let workbook = await exportPermitRequestsReport(recordsToExport);
        let fileName = `${project.name}-permit-register-${dayjs().format(displayDateFormat_DD_MM_YYYY)}.xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    },

    sharePermit: async (req, res) => {
        let prId = +req.param('id');
        let sendToUsers = req.body.email;
        let projectId = +req.param('projectId');
        let byUser = req.user;
        sails.log.info(`User ${byUser.id} requested to share permit request on project ${projectId}.`);
        let permitRequest = await sails.models.permitrequest_reader.findOne({
            where: {id: prId},
            select: ['record_id', 'status_logs', 'signatures',  'status', 'state', 'start_on', 'expire_on', 'permit_ref', 'config_ref', 'project_ref', 'requestor_ref', 'pdf_fields_value', 'createdAt', 'has_register', 'mandatory_attachments']
        });

        if (permitRequest && permitRequest.id) {
            let permitTemplate = await sails.models.permittemplate_reader.findOne({
                where: permitRequest.permit_ref,
                select: ['fillable_pdf_fields', 'field_sections', 'fillable_pdf_ref', 'permit_type', 'ref_number', 'take_register_when', 'font_size']
            }).populate('fillable_pdf_ref');
            [permitRequest] = await populateProjectRefs([permitRequest], 'project_ref', ['name', 'custom_field', 'parent_company', 'contractor', 'project_number']);

            let filled_pdf;

            // check for download then do parallel pdf generation
            if (permitRequest.mandatory_attachments && permitRequest.mandatory_attachments.length) {
                let [ filled_permit_pdf, attachment ] = await Promise.all([
                    fillPermitPdf(req, res, permitRequest, permitTemplate, true),
                    getPermitAttachments(req, res, permitRequest, permitTemplate, 'grid')
                ]);

                filled_pdf = filled_permit_pdf;
                if (attachment && (attachment.imageAttachmentPdf || (attachment.pdfAttachments && attachment.pdfAttachments.length))) {
                    sails.log.info('SharePermit Permit: got mandatory attachments');
                    let pdfFiles = [filled_permit_pdf.file_url];
                    if (attachment.imageAttachmentPdf) {
                        pdfFiles.push(attachment.imageAttachmentPdf);
                    }
                    pdfFiles.push(...attachment.pdfAttachments);
                    const mergedFile = await mergePdfsViaUrls(req, res, pdfFiles, filled_pdf.file_name);
                    filled_pdf = {
                        file_name: mergedFile.name,
                        file_url:  mergedFile.location
                    }
                }
            }

            // If(attachment) fallback from above return original permit request pdf
            if (!filled_pdf) {
                filled_pdf = await fillPermitPdf(req, res, permitRequest, permitTemplate, true);
            }

            if (!filled_pdf || !filled_pdf.file_url) {
                return errorResponse(res,'Something went wrong while sharing permit, please try later.');
            }

            let permit_request_url = filled_pdf.file_url;
            let projectInfo = permitRequest.project_ref;
            let permitPhrase = projectInfo.custom_field ? projectInfo.custom_field.permit_phrase_singlr : 'Permit Request';
            let mail_title = `${permitPhrase} Report Shared`;
            let message = `${req.user.first_name} ${req.user.last_name} has shared ${permitTemplate.permit_type} report #${permitRequest.record_id} on project ${projectInfo.name}`;
            successResponse(res, { message: `The ${permitPhrase} report is being prepared and will be shared shortly.` });

            queueEmailNotifications(EMAIL_NOTIFICATION_FEATURE_CODES.SHARE_TOOL_REPORT, sendToUsers, {
                messageInfo: {
                    mail_title,
                    body_text: message,
                    has_link: true,
                    file_url: permit_request_url,
                },
                project: {
                    id: projectInfo.id,
                    name: projectInfo.name,
                },
            }).catch(e => {sails.log.info('failed to sharing permit', e)});

            return;
        }

        return errorResponse(res,'Something went wrong while sharing permit, please try later.');
    },

    copyPermitTemplates: async(req, res) => {
        let fromCompanyId = +req.param('company_from', 0);
        let toCompanyId = +req.param('company_to', 0);
        let permitIds = req.param('permits_to_copy');
        let permitTemplates = [];
        sails.log.info(`[ADMIN] Copy Permit templates, request from ${fromCompanyId} => ${toCompanyId}, for permitIds ${permitIds}`);
        let permit_template = await sails.models.permittemplate.find({
            where: {id: permitIds},
        });
        if(!permit_template.length){
            sails.log.info(`[ADMIN] permit template does not exist.`);
            return errorResponse(res, `Permit template does not exist for the company: ${fromCompanyId}`);
        }
        for(let i = 0; i < permit_template.length; i++){
            let permit_to_copy = Object.assign({}, permit_template[i]);
            delete permit_to_copy.updatedAt;
            delete permit_to_copy.createdAt;
            delete permit_to_copy.company_record_id;
            delete permit_to_copy.id;
            permit_to_copy.company_ref = toCompanyId;
            permit_to_copy.created_by = req.user.id;
            permit_to_copy.is_active = false;
            let new_record = await sails.models.permittemplate.create(permit_to_copy);
            sails.log.info(`[ADMIN] Permit-templates => ${permitIds[i]} copied to company => ${toCompanyId}`);
            permitTemplates.push(new_record);
        }
        return sendResponse(res, {success: true, message: `${permitTemplates.length} Permit ${(permitTemplates.length) > 1 ? `templates` : `template`} copied successfully.`, permitTemplates});
    },

    getBriefedPermitsByUser: async (req, res) => {
        let projectId = +req.param('projectId');
        let userId = req.user.id;
        let searchTerm = (req.param('q', '')).toString().trim();
        let statusFilter = (req.param('status') && req.param('status').toLowerCase() != 'null') ? req.param('status').split(",").map(a=>+a): [];
        let permitTypeFilter = (req.param('permit_type') && req.param('permit_type').toLowerCase() != 'null') ? req.param('permit_type').split(",").map(a=>a): [];
        let pageNumber = +req.param('pageNumber', 0);
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        sails.log.info(`Briefed Permits by user ${userId} on project ${projectId}, pageNumber: ${pageNumber}, pageSize: ${pageSize}.`);

        let defaultResponse = {
            permit_requests: [],
            q: searchTerm,
            pageSize,
            pageNumber,
            totalCount: 0,
        };
        let {
            records: permit_requests,
            total: totalCount,
            permit_types
        } = await getBriefedByUserPermits(projectId, userId, statusFilter, permitTypeFilter, searchTerm, pageSize, (pageSize * pageNumber));

        permit_requests = permit_requests.map(permitRequest => {
            permitRequest.status_meta = getPermitRequestStatusLabel(permitRequest.status);
            return permitRequest;
        });

        sails.log.info(`found ${permit_requests.length} permit requests briefed on by user ${userId} on project ${projectId}.`);
        return successResponse(res, {...defaultResponse, permit_requests, totalCount, permit_types, permit_status_meta});
    },

    getPermitStatus: async (req, res) => {
        let prId = +req.param('prId');
        let projectId = +req.param('projectId');
        sails.log.info(`get permit status by id ${prId} on project ${projectId}`)
        let permit_request = await sails.models.permitrequest.findOne({
            select: ['status'],
            where: {id: prId, project_ref: projectId}
        });
        let notification = {}
        if (permit_request.status === PERMIT_STATUS.CLOSED_OUT) {
            notification = {
                "header": "Permit Closeout Already Completed",
                "body": "The closeout request has already been completed. No further action is required"
            }
        }

        return successResponse(res, {permit_request, notification});
    }
}
