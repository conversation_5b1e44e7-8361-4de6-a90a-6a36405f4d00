const _pick = require('lodash/pick');
const {
    UserRevisionService: {getLatestUserRevision},
    DataProcessingService: {
        getUserFullName,
        populateUserRefs,
        populateProjectRefs,
        capitalizeFirstLetter: ucFirst,
        buildAssetStatusMessage
    },
    ResponseService: {
        errorResponse,
        successResponse
    },
    TokenUtil: {
        getCompanyInfo,
        hasOnlySiteManagementAccess,
        resourceIdentifier,
    },
    EmailService: {
        sendMail,
    },
    SharedService: {
        instantPdfGenerator,
        downloadPdfViaGenerator,
        getCountryCodeOfProject,
    },
    ExcelService: {
        assetRegisterReport,
        streamExcelDownload
    },
    HttpService: {
        typeOf,
        decodeURIParam
    },
} = require('./../services');

const {
    inductionFn: {
        getUserInductionEmployer
    },
    assetManagementFn: {
        getProjectTemporaryAssets,
        getTemporaryWorkFaults,
        getVehiclesFaults,
        getEquipmentFaults,
        getProjectVehiclesAssetsList,
        getProjectEquipmentAssetsList
    },
    companyFn: { getTaggedOwnersList }
} = require('../sql.fn');

const momentTz = require('moment-timezone');
const moment = require('moment');
const _uniq = require('lodash/uniq');
const {
    fall_back_timezone,
    DEFAULT_PAGE_SIZE,
    dateFormat_DD_MM_YYYY_HH_mm_ss,
    ASSET_TYPES,
    ASSET_TYPE_DATA,
    dbDateFormat_YYYY_MM_DD
} = sails.config.constants;

const {
    AssetValidator: { addEquipmentInspection, closeOutInspectionFault, addTemporaryWorkInspection }
} = require('./../validators');

const lcFirst = (string) => {
    let newString = string.charAt(0).toLowerCase() + string.slice(1);
    return newString;
};

const formatTimeInTimezone = (n, format, timezone) => {
    return timezone ? momentTz(+n).tz(timezone).format(format) : (+n) ? moment(+n).format(format) : moment().format(format);
};

const replaceAll = (str, find, replace) => {
    const escapedFind = find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
    return str.replace(new RegExp(escapedFind, 'g'), replace);
};


const getTypeOfAssetVehicles = async () => {
    let record = await sails.models.inndexsetting_reader.findOne({ name: 'type_of_asset_vehicles_en_gb' });
    return record.value;
};

const getTypeOfAssetEquipment = async () => {
    let record = await sails.models.inndexsetting_reader.findOne({ name: 'type_of_asset_equipments_en_gb' });
    return record.value;
};

const getTypeOfAssetTemporaryWork = async () => {
    let record = await sails.models.inndexsetting_reader.findOne({ name: 'type_of_temporary_works_en_gb' });
    return record.value;
};

const getEquipmentSubTypes = async () => {
    sails.log.info('Get equipment sub type items')
    let record = await sails.models.inndexsetting_reader.findOne({ name: 'asset_equipment_items' });
    return record.value || {};
}

let prepareChecklistItems = (vi, assetType) => {
    for (let key in vi.general_checklist) {
        vi[key] = vi.general_checklist[key];
    }

    for (let key in vi.vehicle_specific_checklist) {
        vi[key] = vi.vehicle_specific_checklist[key];
    }

    for (let key in vi.specific_checklist) {
        vi[key] = vi.specific_checklist[key];
    }
    return vi;
};

let updateRecord = async (payload, assetType, id, req, res) => {
    let assetExistingData = [];
    let assetInfo = {};
    let assetData = ASSET_TYPE_DATA[assetType];
    let assetDbTable = assetData['databaseTable'];
    let assetDbTableReader = assetData['databaseTableReader'];
    assetExistingData =  await sails.models[assetDbTableReader].findOne({
        where: { "id": id},
        select: ["approval_pending"]
    });
    assetInfo = await sails.models[assetDbTable].updateOne({id: id}).set(payload);
    let comment = (payload.declined_comment) ? payload.declined_comment : 'N/A';
    if(assetInfo && assetInfo.id) {
        if (assetExistingData.approval_pending != assetInfo.approval_pending) {
            if (assetInfo.approval_pending === 2) { //Approved
                await sendMailToSubmittedByUser(assetInfo, assetType, req.user, 'approved');
            } else if (assetInfo.approval_pending === 3) { //Declined
                sails.log.info("Declined Comment ", comment);
                await sendMailToSubmittedByUser(assetInfo, assetType, req.user, 'declined', comment);
            }
        }
        sails.log.info(`asset ${assetType} updated successfully.`);
        return successResponse(res, (assetType == 'equipment') ? {asset_equipment: assetInfo} : {asset_vehicle: assetInfo});
    }

    sails.log.info('Failed to update asset ${assetType}.');
    return errorResponse(res, sails.__('Failed to update asset ${assetType}.'));
};

const sendMailToSubmittedByUser =  async (assetInfo, assetType, user, status, comment = '') => {
    let receiverInfo = await sails.models.user_reader.findOne({
        where: {'id': assetInfo.user_ref},
        select: ['email', 'first_name', 'last_name']
    });

    let project = await sails.models.project.findOne({
        where: {"id": assetInfo.project_ref},
        select: ["name"]
    });

    await sendMailToUser(receiverInfo, user, assetInfo, assetType, project.name, status, comment);
    sails.log.info(`Email has been sent to user who added asset ${assetType}.`);
}

const sendMailToAssetManagers = async ({alertFor= 'new_asset', assetInfo, assetInspection, assetType, user, status, comment = ''}) => {
    let project = await sails.models.project_reader.findOne({
        where: {"id": assetInfo.project_ref},
        select: ["name", "custom_field"]
    });

    if (project.custom_field) {
        let assetManagerIds = [];
        if (assetType == ASSET_TYPES.AssetEquipment && project.custom_field.equipment_managers && project.custom_field.equipment_managers.length) {
            assetManagerIds = project.custom_field.equipment_managers;
        } else if (assetType == ASSET_TYPES.AssetVehicle && project.custom_field.vehicle_managers && project.custom_field.vehicle_managers.length) {
            assetManagerIds = project.custom_field.vehicle_managers;
        } else if(assetType == 'temporary work' && project.custom_field.temp_works_managers && project.custom_field.temp_works_managers.length){
            assetManagerIds = project.custom_field.temp_works_managers;
        }

        let asset_managers = await sails.models.user_reader.find({
            where: {'id': _uniq(assetManagerIds)},
            select: ['email', 'first_name', 'last_name']
        });

        if (asset_managers.length) {
            sails.log.info(`Sending mails to ${assetType} managers.`)
            for (let key in asset_managers) {
                let receiverInfo = asset_managers[key];
                if (alertFor == 'new_asset') {
                    await sendMailToUser(receiverInfo, user, assetInfo, assetType, project.name, status, comment = '');
                } else if(alertFor == 'fault_present') {
                    let assetTypTitle = asset_type = asset_owner = asset_id = '';
                    if (assetType == ASSET_TYPES.AssetEquipment) {
                        assetTypTitle = 'Equipment';
                        let typeOfEquipments = await getTypeOfAssetEquipment();
                        let equipmentType = (typeOfEquipments || []).find(equipment => equipment.key === assetInfo.equipment_type);
                        asset_type = equipmentType.value;
                        asset_id = assetInfo.equipment_id;
                        asset_owner = (assetInfo.tagged_owner.length) ? (assetInfo.tagged_owner).reduce((arr, owner) => {
                            arr.push(owner.name);
                            return arr;
                        },[]).join(',') : "";
                    } else if(assetType == ASSET_TYPES.AssetVehicle) {
                        assetTypTitle = 'Vehicle';
                        let typeOfVehicles = await getTypeOfAssetVehicles();
                        let vehicleType = (typeOfVehicles || []).find(vehicle => vehicle.key === assetInfo.type_of_vehicle);
                        asset_type = vehicleType.value;
                        asset_id = assetInfo.vehicle_id;
                        asset_owner = (assetInfo.tagged_owner.length) ? (assetInfo.tagged_owner).reduce((arr, owner) => {
                            arr.push(owner.name);
                            return arr;
                        },[]).join(',') : "";
                    } else if(assetType == ASSET_TYPES.AssetTemporaryWork) {
                        assetTypTitle = 'Temporary Works';
                        let typeOfWorks = await getTypeOfAssetTemporaryWork();
                        let workType = (typeOfWorks || []).find(w => w.key === assetInfo.type_of_works);
                        asset_type = workType.value;
                        asset_id = assetInfo.item_id;
                        asset_owner = assetInfo.tagged_owner.map(owner => owner.name).join(',');

                    }
                    let main_content = `A ${assetTypTitle} fault has been identified by ${getUserFullName(user)} @ <b>${project.name}</b>.`
                    let subject = `${assetTypTitle} Fault: ${project.name}`;
                    let emailHtml = await sails.renderView('pages/mail/mail-content', {
                        title: subject,
                        mail_body: 'notify-vm-on-asset-faults',
                        main_content,
                        assetTypTitle,
                        assetInfo,
                        asset_type,
                        asset_owner,
                        asset_id,
                        receiver_name: receiverInfo.first_name,
                        layout: false
                    });
                    sails.log.info('Sending mail to', receiverInfo.email);
                    await sendMail(subject, [receiverInfo.email], emailHtml);
                }
                sails.log.info(`Email has been sent to ${assetType} manager.`);
            }
        }
    }
}

const sendMailToUser =  async (receiverInfo, userInfo, assetInfo, assetType, projectName, status, comment = '') => {
    let assetTypeTitle = ASSET_TYPE_DATA[assetType].title;
    let main_content = `${getUserFullName(userInfo)} has added ${lcFirst(assetTypeTitle)} ${assetInfo.serial_number} onto project ${projectName}.`;
    let additional_comment = 'Please log in to Approve or Decline the request.';
    let subject = `${ucFirst(assetTypeTitle)} approval pending: Project - ${projectName}`;
    if (status === 'approved') {
        main_content = `${ucFirst(assetTypeTitle)} ${assetInfo.serial_number} has been approved on to project ${projectName} by ${getUserFullName(userInfo)}.`
        additional_comment = '';
        subject = `${ucFirst(assetTypeTitle)}  approved: Project - ${projectName}`;
    } else if (status === 'declined') {
        main_content = `${ucFirst(assetTypeTitle)} ${assetInfo.serial_number} has been declined on to project ${projectName} by ${getUserFullName(userInfo)} for the following reason:`
        additional_comment = comment;
        subject = `${ucFirst(assetTypeTitle)}  declined: Project - ${projectName}`;
    }

    let emailHtml = await sails.renderView('pages/mail/mail-content', {
        title: subject,
        mail_body: 'notify-vm-on-asset-vehicle-actions',
        main_content,
        additional_comment,
        receiver_name: receiverInfo.name,
        layout: false
    });
    sails.log.info('Sending mail to', receiverInfo.email);
    await sendMail(subject, [receiverInfo.email], emailHtml);
}

const cleanFields = (data) => {

    if(data.vehicle_photos) {
        data.vehicle_photos = (data.vehicle_photos || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
    }

    if(data.equipment_photos) {
        data.equipment_photos = (data.equipment_photos || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
        //sails.log.info('Examination Certificates now is', data.examination_certificates);
    }

    if(data.photos) {
        data.photos = (data.photos || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
        //sails.log.info('Examination Certificates now is', data.examination_certificates);
    }

    if(data.examination_certificates) {
        data.examination_certificates = (data.examination_certificates || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
        //sails.log.info('Examination Certificates now is', data.examination_certificates);
    }

    if(data.service_certificates) {
        data.service_certificates = (data.service_certificates || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
        //sails.log.info('Service Certificates now is', data.service_certificates);
    }

    if(data.mot_certificates) {
        data.mot_certificates = (data.mot_certificates || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
        //sails.log.info('MOT Certificates now is', data.mot_certificates);
    }

    if (data.tagged_owner && data.tagged_owner.length) {
        let tagged_owner = (data.tagged_owner || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);

        data.tagged_owner = _.uniq(tagged_owner);
    }

    

    if(data.custom_fields && data.custom_fields.length) {
        data.custom_fields = data.custom_fields.map(field => {
            if (field.type === 'Certification' && Array.isArray(field.certificates) && field.certificates.length) {
              return {
                ...field,
                certificates: field.certificates.map(cert => cert.id)
              };
            }
            return field;
          });
    }


    return data;
}

const prepareWeeklyInspections = async ({
    assetRecord,
    assetInspections,
    timezone
}) => {
    let dates = [];
    assetInspections = assetInspections.reduce((inspections, vi) => {
        if (!dates.includes(formatTimeInTimezone(vi.createdAt, 'DD-MM-YYYY', timezone))) {
            dates.push(formatTimeInTimezone(vi.createdAt, 'DD-MM-YYYY', timezone));
            inspections.push(vi);
        }
        return inspections;
    }, []);

    let numberOfFaults = 0;
    let hasFault = false;
    let faultImageIds = [];
    let generalChecklistImgIds = [];
    let generalChecklistCount = 0;
    let days = {
        "Mon": {},
        "Tue": {},
        "Wed": {},
        "Thu": {},
        "Fri": {},
        "Sat": {},
        "Sun": {}
    };
    assetInspections.reduce((obj, vi) => {
        if (vi.fault_details.length) {
            vi.fault_details.map(fault_detail => {
                if (fault_detail.images && fault_detail.images.length) {
                    faultImageIds.push(...fault_detail.images);
                }

                if (fault_detail.closedout_images && fault_detail.closedout_images.length) {
                    faultImageIds.push(...fault_detail.closedout_images);
                }
            });
            hasFault = true;
        }

        if (vi.general_checklist && Object.keys(vi.general_checklist).length && vi.general_checklist.images && vi.general_checklist.images.length) {
            generalChecklistImgIds.push(...(vi.general_checklist.images));
        }

        if (vi.general_checklist && Object.keys(vi.general_checklist).length) {
            generalChecklistCount += 1;
        }

        numberOfFaults += vi.fault_details.length;
        let day = formatTimeInTimezone(vi.createdAt, 'ddd', timezone);
        vi = prepareChecklistItems(vi, 'vehicle');
        days[day] = vi;
        return obj;
    }, {});

    //Expanding fault images
    if (faultImageIds.length || generalChecklistImgIds.length) {
        let imagesObj = await sails.models.userfile_reader.find({id: _uniq([...faultImageIds, ...generalChecklistImgIds])});
        assetInspections.map(vi => {
            if (vi.fault_details.length) {
                vi.fault_details = vi.fault_details.map(fault_detail => {
                    if (fault_detail.images && fault_detail.images.length) {
                        fault_detail.images = imagesObj.filter(image => fault_detail.images.includes(image.id));
                    }

                    if (fault_detail.closedout_images && fault_detail.closedout_images.length) {
                        fault_detail.closedout_images = imagesObj.filter(image => fault_detail.closedout_images.includes(image.id));
                    }
                    return fault_detail;
                });
            }

            if (vi.general_checklist && Object.keys(vi.general_checklist).length && vi.general_checklist.images && vi.general_checklist.images.length) {
                vi.general_checklist.images = imagesObj.filter(image => (vi.general_checklist.images).includes(image.id));
            }
            return vi;
        });
    }

    let { project_logo_file, companyName } = await getCompanyInfo(assetRecord.project_ref);

    return { inspections: assetInspections, days, hasFault, numberOfFaults, project_logo_file, companyName, generalChecklistCount }
};

const expandInspectionImages = async (assetInspections) => {
    let faultImageIds = [];
    assetInspections.reduce((obj, vi) => {
        if (vi.fault_details.length) {
            vi.fault_details.map(fault_detail => {
                if (fault_detail.images && fault_detail.images.length) {
                    faultImageIds.push(...fault_detail.images);
                }

                if (fault_detail.closedout_images && fault_detail.closedout_images.length) {
                    faultImageIds.push(...fault_detail.closedout_images);
                }
            });
        }
        return obj;
    }, {});

    //Expanding fault images
    if (faultImageIds.length) {
        let imagesObj = await sails.models.userfile_reader.find({
            where: {id: _uniq([...faultImageIds])},
            select: ['sm_url', 'md_url', 'file_url']
        });
        assetInspections.map(vi => {
            if (vi.fault_details.length) {
                vi.fault_details = vi.fault_details.map(fault_detail => {
                    if (fault_detail.images && fault_detail.images.length) {
                        fault_detail.images = imagesObj.filter(image => fault_detail.images.includes(image.id));
                    }

                    if (fault_detail.closedout_images && fault_detail.closedout_images.length) {
                        fault_detail.closedout_images = imagesObj.filter(image => fault_detail.closedout_images.includes(image.id));
                    }
                    return fault_detail;
                });
            }

            return vi;
        });
    }

    return assetInspections;
};

const associateFaultIdToFault = async (faults, assetRef, assetType) => {
    let rawResult = {};
    if (assetType == ASSET_TYPES.AssetVehicle) {
        rawResult = await sails.sendNativeQuery(`SELECT id,
                                                        fault_details,
                                                        "createdAt"
                                                 FROM asset_vehicle_inspection
                                                 WHERE vehicle_ref = $1
                                                 ORDER BY id DESC`, [assetRef]);
    } else if (assetType == ASSET_TYPES.AssetEquipment) {
        rawResult = await sails.sendNativeQuery(`SELECT id,
                                                        fault_details,
                                                        "createdAt"
                                                 FROM asset_equipment_inspection
                                                 WHERE equipment_ref = $1
                                                 ORDER BY id DESC`, [assetRef]);
    } else if (assetType == ASSET_TYPES.AssetTemporaryWork) {
        rawResult = await sails.sendNativeQuery(`SELECT id,
                                                        fault_details,
                                                        "createdAt"
                                                 FROM asset_temporary_work_inspection
                                                 WHERE temporary_work_ref = $1
                                                 ORDER BY id DESC`, [assetRef]);
    }

    let previousInspections = [];
    if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
        previousInspections = rawResult.rows;
    }

    let dates = [];
    //pick only last inspection of the date(Filter Duplicate)
    previousInspections = previousInspections.reduce((inspections, vi) => {
        if (!dates.includes(moment(+vi.createdAt).format('DD-MM-YYYY'))) {
            dates.push(moment(+vi.createdAt).format('DD-MM-YYYY'));
            inspections.push(vi);
        }
        return inspections;
    }, []);

    let lastFaultId = 0;
    if (previousInspections.length && previousInspections[0].fault_details && previousInspections[0].fault_details.length) {
        let previousInsFaultIds = [];
        (previousInspections[0].fault_details || []).forEach(fault => {
            if (fault && fault.fault_id) {
                previousInsFaultIds.push(fault.fault_id);
            }
        });
        lastFaultId = Math.max(...previousInsFaultIds);
    }

    return (faults).map(fault => {
        lastFaultId += 1;
        fault.fault_id = lastFaultId;
        return fault;
    });
};

const expandCompanies = async (records) => {
    let taggedOwnerIds = (records || []).reduce((arr, asset) => {
        if(asset.tagged_owner && asset.tagged_owner.length) {
            arr.push(...asset.tagged_owner);
        }
        return arr;
    }, []);
    taggedOwnerIds = taggedOwnerIds.filter(ref => ref != null);
    sails.log.info('Expanding tagged owner/companies',  _.uniq(taggedOwnerIds));
    return (taggedOwnerIds.length) ? await sails.models.createemployer_reader.find({
        where: {id: _.uniq(taggedOwnerIds)},
        select: ['name']
    }) : [];
}

const getPlantHoursForAsset = (record, checklistType, timezone) => {
    let inpsectionRecordIndex = record.inspections.findIndex(inspection => inspection && inspection[checklistType] && inspection[checklistType].plant_hours && inspection[checklistType].plant_hours !== null && inspection[checklistType].plant_hours !== false);
    let plant_hours = null;
    if (inpsectionRecordIndex !== -1) {
        plant_hours = record.inspections[inpsectionRecordIndex][checklistType].plant_hours + ' (' + formatTimeInTimezone(record.inspections[inpsectionRecordIndex].createdAt, "DD/MM/YY", timezone) +  ')';
    }
    return plant_hours;
};

const setPlantHours =  (assetRecords, projectInfo) => {
    let timezone = (projectInfo && projectInfo.custom_field && projectInfo.custom_field.timezone) || fall_back_timezone;
    assetRecords = assetRecords.map(record => {
        let sc_plant_hours = getPlantHoursForAsset(record, 'specific_checklist', timezone);
        let vsc_plant_hours = getPlantHoursForAsset(record, 'vehicle_specific_checklist', timezone);
        let plant_hours = (sc_plant_hours && sc_plant_hours.length) ? sc_plant_hours : vsc_plant_hours;
        if(!plant_hours) {
            plant_hours = getPlantHoursForAsset(record, 'general_checklist', timezone);
        }
        return {
          ...record,
          plant_hours: plant_hours,
        };
    });
    return assetRecords;
};

const downloadRegister = async (res, assetRecords, assetType, projectInfo) => {
    (assetRecords || []).map((record) => {
        let lastInspection = (record.inspections && record.inspections.length) ? record.inspections[0] : null;
        record.faultCount  = (record.inspections || []).reduce((faultCount, inspection, index) => {
            if (inspection.fault_details && inspection.fault_details.length) {
                let openFaults = (inspection.fault_details || []).filter(fault => fault.status == 'open');
                faultCount += openFaults.length;
            }
            return faultCount;
        }, 0);
        record.lastInspectionCreatedAt = (lastInspection) ? lastInspection.createdAt : null;
        return record;
    });
    if(assetType == 'vehicle') {
        assetRecords = setPlantHours(assetRecords, projectInfo);
    }
    sails.log.info(`Number of asset records: ${assetRecords.length}.`);

    let liveAssetRecords = (assetRecords || []).filter(record => !record.is_archived);
    let archivedAssetRecords = (assetRecords || []).filter(record => record.is_archived);
    sails.log.info(`Number of live asset records: ${liveAssetRecords.length}.`);
    sails.log.info(`Number of archive asset records: ${archivedAssetRecords.length}.`);

    let workbook = await assetRegisterReport(liveAssetRecords, archivedAssetRecords, assetType, projectInfo);
    let fileName = `asset-register-${assetType}-${projectInfo.project_number}-${projectInfo.name}.xlsx`
    return streamExcelDownload(res, workbook, fileName);
}

const getAssetInspections = async(assetType, assetIds, fieldsToPopulate = []) => {
    let assetData = ASSET_TYPE_DATA[assetType];
    let assetDbTable = assetData['databaseTableReader'];
    let assetInspectionTable = assetData['inpsectionDBTableReader'];
    let inspectionKey = assetData['inspectionTypeKey'];
    let filter = {};
    filter[inspectionKey] = assetIds;
    let selectFields = ['fault_details', 'createdAt', ...fieldsToPopulate];
    selectFields.push(inspectionKey);
    let assetInspections =  await sails.models[assetInspectionTable].find({
        where: filter,
        select: selectFields
    }).sort([
        {id: 'DESC'}
    ]);
    sails.log.info(`Found ${assetInspections.length} asset inspections.`);
    return assetInspections;
}

const expandAsset = async(asset, expand_inspections, assetData) => {
    let assetInspectionTable = assetData['inpsectionDBTableReader'];
    let inspectionKey = assetData['inspectionTypeKey'];
    let photosKey = assetData['photosKey'];
    if(asset && asset.id) {
        let eaxmCertificates = (asset.examination_certificates && asset.examination_certificates.length) ? asset.examination_certificates: [];
        let serviceCertificates = (asset.service_certificates && asset.service_certificates.length) ? asset.service_certificates: [];
        let motCertificates = (asset.mot_certificates && asset.mot_certificates.length) ? asset.mot_certificates: [];
        let customCertificateIds = [];
        asset.custom_fields.forEach(field => {
            if (field.type === 'Certification' && Array.isArray(field.certificates)) {
                customCertificateIds = customCertificateIds.concat(field.certificates);
            }
        });
        let allFiles = [...asset[photosKey], ...eaxmCertificates, ...serviceCertificates, ...motCertificates, ...customCertificateIds]
        let photosFiles = await sails.models.userfile_reader.find({
            id: allFiles.filter(val => val != null)
        });
        asset[photosKey] = (photosFiles || []).filter(file => asset[photosKey].includes(file.id));
        if(eaxmCertificates.length) {
            asset.examination_certificates = (photosFiles || []).filter(file => asset.examination_certificates.includes(file.id));
        }
        if(serviceCertificates.length) {
            asset.service_certificates = (photosFiles || []).filter(file => asset.service_certificates.includes(file.id));
        }
        if(motCertificates.length) {
            asset.mot_certificates = (photosFiles || []).filter(file => asset.mot_certificates.includes(file.id));
        }
        if(customCertificateIds.length) {
            asset.custom_fields = asset.custom_fields.map(field => {
                if (field.type === 'Certification' && Array.isArray(field.certificates) && field.certificates.length) {
                  field.certificates = (photosFiles || []).filter(file => field.certificates.includes(file.id));
                  return field;
                }
                return field;
              });
        }
        asset.tagged_owner = await expandCompanies([asset]);
        if (expand_inspections) {
            let where = {};
            where[inspectionKey] =  asset.id;
            let assetInspections = await sails.models[assetInspectionTable].find(where);
            assetInspections = await populateUserRefs(assetInspections, 'user_ref', []);
            let dates = [];
            assetInspections = assetInspections.reduce((inspections, vi) => {
                if (!dates.includes(formatTimeInTimezone(vi.createdAt, 'DD-MM-YYYY', fall_back_timezone))) {
                    dates.push(formatTimeInTimezone(vi.createdAt, 'DD-MM-YYYY', fall_back_timezone));
                    inspections.push(vi);
                }
                return inspections;
            }, []);
            asset[assetData.key + '_inspections']= [];
            if (assetInspections && assetInspections.length) {
                assetInspections = await expandInspectionImages(assetInspections);
                asset[assetData.key + '_inspections'] = assetInspections;
            }
        }
    }
    return asset;
};

const codedAssetTypes = {
    'AMV': ASSET_TYPES.AssetVehicle,
    'AME': ASSET_TYPES.AssetEquipment,
    'AMT': ASSET_TYPES.AssetTemporaryWork,

};

const getOwnerFilter = async (user, projectId, queryOnly=true) => {
    let hasOnlySiteManagement = await hasOnlySiteManagementAccess(user, projectId);
    sails.log.info(`hasOnlySiteManagement: `, hasOnlySiteManagement);
    let ownerFilter = '';
    let userEmployer = null;
    let project = await sails.models.project_reader.findOne({where: {id: projectId}, select:['name','parent_company']});
    let uacRecord = (user.raw_uac).find(uac => (uac.resource === `project.${projectId}` || uac.resource === `company.${project.parent_company}`));
    if (hasOnlySiteManagement || uacRecord == undefined) {
        userEmployer = await getUserInductionEmployer(user, projectId);
        ownerFilter = `AND ${userEmployer} = ANY(tagged_owner)`;
    }
    if(queryOnly) {
        return ownerFilter;
    }
    return userEmployer;
};

const findAndPopulateAssets = async  (assetData, where, userEmployer, expand_inspections, assetType) => {
    let dbTable = assetData['databaseTableReader'];
    let assetInspectionTable = assetData['inpsectionDBTableReader'];
    let inspectionKey = assetData['inspectionTypeKey'];
    let assets = await sails.models[dbTable].find(where);
    //temporary fix unitl app update, to be removed after app update or September 30
    assets = assets.map(a=>{
        a.project_ref = {'id': a.project_ref};
        return a;
    });
    for(let i=0; i<assets.length; i++) {
        let where = {};
        where[inspectionKey] = assets[i].id;
        let latest_inspection = await sails.models[assetInspectionTable].find({
            where: where,
            select: ['createdAt', 'updatedAt', 'id', 'user_ref'],
            limit:1
        }).sort([
            { createdAt: 'DESC' },
        ]);
        assets[i].latest_inspection = (latest_inspection && latest_inspection.length) ? latest_inspection[0] : {};
    }
    let assetIds = assets.map(a=> a.id);
    let faults = [];
    let typeOfAssets = [];
    //get open fault count
    if(assetIds.length) {
        if(assetType === ASSET_TYPES.AssetEquipment) {
            faults = await getEquipmentFaults(assetIds);
            typeOfAssets = await getTypeOfAssetEquipment();
            //populate asset alternate phrasing
            assets = await mapAlternatePhrasing(assets, where.project_ref, ASSET_TYPES.AssetEquipment, 'equipment_type');

            let equipmentItems = await getEquipmentSubTypes();
            //populate inspection key for each equipment
            assets = assets.map(equipment => {
                if(equipment.equipment_type === ASSET_TYPES.AccessEquipment && equipmentItems.access_equipment_items.includes(equipment.item)) {
                    equipment.inspection_key = 'ae_'+equipment.item.toLowerCase().replace(/ /g, "_");
                } else if(equipment.equipment_type == 'fall_arrest_systems' && equipmentItems.fall_arrest_systems_items.includes(equipment.item)) {
                    equipment.inspection_key = 'fas_'+equipment.item.toLowerCase().replace(/ /g, "_");
                } else {
                    equipment.inspection_key = equipment.equipment_type;
                }
                return equipment;
            });
        } else if(assetType === ASSET_TYPES.AssetVehicle) {
            faults = await getVehiclesFaults(assetIds);
            typeOfAssets = await getTypeOfAssetVehicles();
            //populate asset alternate phrasing
            assets = await mapAlternatePhrasing(assets, where.project_ref, ASSET_TYPES.AssetVehicle, 'type_of_vehicle');
        } else if(assetType === ASSET_TYPES.AssetTemporaryWork) {
            faults = await getTemporaryWorkFaults(assetIds);
            typeOfAssets = await getTypeOfAssetTemporaryWork();
            //populate asset alternate phrasing
            assets = await mapAlternatePhrasing(assets, where.project_ref, ASSET_TYPES.AssetTemporaryWork, 'type_of_works');
        }
    }

    const mappedTypes = getAssetTypeMappedWithSpecificChecklist(typeOfAssets);
    assets = assets.map(item => {
        let faultData = faults.find(fault => fault[inspectionKey] == item.id);
        item.fault_count = (faultData && faultData.fault_count) ? +faultData.fault_count : 0;
        item.has_specific_checklist = mappedTypes[item[assetData['typeKey']]] || false;
        //populate asset alternate phrasing for each temporary work and vehicle
        if(assetType !== ASSET_TYPES.AssetEquipment) { item.inspection_key = item[assetData['typeKey']]; }
        return item;
    });
    if (userEmployer !== null) {
      assets = assets.filter(record => record.tagged_owner.includes(userEmployer));
    }
    return Promise.all(
      assets.map(asset => expandAsset(asset, expand_inspections, assetData))
    );
};

const getAssetTypeMappedWithSpecificChecklist = (types) => {
    const typeMapping = types.reduce((map, type) => {
        map[type.key] = type.has_specific_checklist;
        return map;
    }, {});
    return typeMapping;
}

const mapAlternatePhrasing = async(records, projectId, assetType, typeKey) => {
    let project = await sails.models.project_reader.findOne({where:{id: projectId}, select: ['id', 'parent_company']});
    let companyId = project.parent_company;
    let filter = {company_ref: companyId, asset_type: assetType};
    let columns = ['key', 'alternate_phrase']
    let assetConfig = await sails.models.assetcustomconfig_reader.find({where: filter, select: columns});
    const assetConfigMap = new Map(assetConfig.map(a => [a.key, a.alternate_phrase]));
    records = records.map(r=>{
        r.alternate_phrase = assetConfigMap.get(r[typeKey]);
        return r;
    });
    return records;
};

const getAssetVehiclesMappedWithSpecificChecklist = async (user, projectId, projectAssetVehicles, expand_inspections) => {
    let companiesInfo = await expandCompanies(projectAssetVehicles);
    projectAssetVehicles = await mapAlternatePhrasing(projectAssetVehicles, projectId, ASSET_TYPES.AssetVehicle, 'type_of_vehicle');
    projectAssetVehicles = await populateProjectRefs(projectAssetVehicles, 'project_ref', []);
    let typeOfVehicles = await getTypeOfAssetVehicles();
    const mappedTypes = getAssetTypeMappedWithSpecificChecklist(typeOfVehicles);
    projectAssetVehicles = projectAssetVehicles.map(record => ({
        ...record,
        has_specific_checklist: mappedTypes[record.type_of_vehicle] || false,
    }));

    if (projectAssetVehicles && projectAssetVehicles.length) {
        let fileRefs = [];
        let customCertificateIds = [];
        projectAssetVehicles = projectAssetVehicles.map(vehicle => {
            //attach expanded tagged owner/company
            vehicle.tagged_owner = (companiesInfo || []).filter(company => (vehicle.tagged_owner || []).includes(company.id));
            vehicle.custom_fields.forEach(field => {
                if (field.type === 'Certification' && Array.isArray(field.certificates)) {
                    customCertificateIds = customCertificateIds.concat(field.certificates);
                }
            });
            fileRefs.push(...(vehicle.vehicle_photos), ...(vehicle.examination_certificates), ...(vehicle.service_certificates), ...(vehicle.mot_certificates), ...customCertificateIds);
            vehicle.inspection_key = vehicle.type_of_vehicle;
            vehicle.approval_pending_message = buildAssetStatusMessage(vehicle.approval_pending);
            return vehicle;
        });

        //remove null values
        fileRefs = fileRefs.filter(function (ref) {
            return ref != null;
        });

        if (fileRefs.length) {
            let files = await sails.models.userfile_reader.find({
                where: {id: fileRefs},
                select: ['createdAt', 'id', 'name', 'file_url', 'sm_url', 'md_url', 'user_id', 'category', 'file_mime', 'img_translation']
            });
            projectAssetVehicles = projectAssetVehicles.map(assetVehicle => {
                assetVehicle.vehicle_photos = (files || []).filter(file => assetVehicle.vehicle_photos.includes(file.id));
                assetVehicle.examination_certificates = (files || []).filter(file => assetVehicle.examination_certificates.includes(file.id));
                assetVehicle.service_certificates = (files || []).filter(file => assetVehicle.service_certificates.includes(file.id));
                assetVehicle.mot_certificates = (files || []).filter(file => assetVehicle.mot_certificates.includes(file.id));
                assetVehicle.custom_fields = assetVehicle.custom_fields.map(field => {
                    if (field.type === 'Certification' && Array.isArray(field.certificates) && field.certificates.length) {
                        field.certificates = (files || []).filter(file => field.certificates.includes(file.id));
                        return field;
                    }
                    return field;
                });
                return assetVehicle;
            });
        }

        let vehicleIds = projectAssetVehicles.map(vehicle => vehicle.id);
        if (expand_inspections) {
            let vehicleInspections = await sails.models.assetvehicleinspection_reader.find({vehicle_ref: vehicleIds});
            vehicleInspections = await populateUserRefs(vehicleInspections, 'user_ref', []);
            if (vehicleInspections && vehicleInspections.length) {
                projectAssetVehicles = projectAssetVehicles.filter(vehicle => {
                    let vehicle_inspection = vehicleInspections.filter(u => u.vehicle_ref === vehicle.id);
                    let dates = [];
                    vehicleInspections = vehicleInspections.reduce((inspections, vi) => {
                        if (!dates.includes(formatTimeInTimezone(vi.createdAt, 'DD-MM-YYYY', fall_back_timezone))) {
                            dates.push(formatTimeInTimezone(vi.createdAt, 'DD-MM-YYYY', fall_back_timezone));
                            inspections.push(vi);
                        }
                        return inspections;
                    }, []);
                    vehicle.vehicle_inspections = vehicle_inspection;
                    return vehicle;
                });
            }
        } else {
            for (let key in projectAssetVehicles) {
                let assetVehicle = projectAssetVehicles[key];
                let latest_inspection;
                latest_inspection = assetVehicle.latest_inspection;
                assetVehicle.latest_inspection = latest_inspection !== null ? latest_inspection : {};

                if (assetVehicle.latest_inspection && assetVehicle.latest_inspection.user_ref) {
                    assetVehicle.latest_inspection.user_ref = await sails.models.user_reader.findOne({
                        where: {id: assetVehicle.latest_inspection.user_ref},
                        select: ['id', 'first_name', 'last_name']
                    });
                }
                projectAssetVehicles[key] = assetVehicle;
            }

            projectAssetVehicles = projectAssetVehicles.map(vehicle => {
                vehicle.fault_count = (+vehicle.fault_count) ? +vehicle.fault_count : 0;
                return vehicle;
            });
        }
    }

    let userEmployer = {};
    let userEmployerId = await getUserInductionEmployer(user, projectId);
    if(userEmployerId){
        userEmployer = await sails.models.createemployer_reader.findOne({
            where: {id: userEmployerId},
            select: ['id', 'name', 'country_code']
        });
    }

    sails.log.info(`vehicle: projectAssetVehicles count ${projectAssetVehicles.length}`);

    let total_asset = await sails.models.projectassetvehicles_reader.count({project_ref: projectId, approval_pending: { '!=' : 3 }});
    return {records:projectAssetVehicles, total_asset, userEmployer };
}

const getAssetEquipmentMappedWithSpecificChecklist = async (user, projectId, projectAssetEquipments, expand_inspections) =>{
    let companiesInfo = await expandCompanies(projectAssetEquipments);
    projectAssetEquipments = await mapAlternatePhrasing(projectAssetEquipments, projectId, ASSET_TYPES.AssetEquipment, 'equipment_type');
    projectAssetEquipments = await populateProjectRefs(projectAssetEquipments, 'project_ref', []);
    let typeOfEquipment = await getTypeOfAssetEquipment();
    const mappedTypes =  getAssetTypeMappedWithSpecificChecklist(typeOfEquipment);
    projectAssetEquipments = projectAssetEquipments.map(record => ({
        ...record,
        has_specific_checklist: mappedTypes[record.equipment_type] || false,
    }));

    if (projectAssetEquipments && projectAssetEquipments.length) {
        let fileRefs = [];
        let equipmentItems = await getEquipmentSubTypes();
        projectAssetEquipments = projectAssetEquipments.map(equipment => {
            //attach expanded tagged owner/company
            equipment.tagged_owner = (companiesInfo || []).filter(company => (equipment.tagged_owner).includes(company.id));
            fileRefs.push(...(equipment.equipment_photos), ...(equipment.examination_certificates));
            if(equipment.equipment_type === ASSET_TYPES.AccessEquipment && equipmentItems.access_equipment_items.includes(equipment.item)) {
                equipment.inspection_key = 'ae_'+equipment.item.toLowerCase().replace(/ /g, "_");
            } else if(equipment.equipment_type == 'fall_arrest_systems' && equipmentItems.fall_arrest_systems_items.includes(equipment.item)) {
                equipment.inspection_key = 'fas_'+equipment.item.toLowerCase().replace(/ /g, "_");
            } else {
                equipment.inspection_key = equipment.equipment_type;
            }
            equipment.approval_pending_message = buildAssetStatusMessage(equipment.approval_pending);
            return equipment;
        });

        //remove null values
        fileRefs = fileRefs.filter(function (ref) {
            return ref != null;
        });

        if (fileRefs.length) {
            let files = await sails.models.userfile.find({
                where: {id: fileRefs},
                select: ['createdAt','file_mime', 'id', 'name', 'file_url', 'sm_url', 'md_url', 'user_id', 'category', 'img_translation']
            });

            projectAssetEquipments = projectAssetEquipments.map(assetEquipment => {
                assetEquipment.equipment_photos = (files || []).filter(file => assetEquipment.equipment_photos.includes(file.id));
                assetEquipment.examination_certificates = (files || []).filter(file => assetEquipment.examination_certificates.includes(file.id));
                return assetEquipment;
            });
        }

        let equipmentIds = projectAssetEquipments.map(equipment => equipment.id);
        if (expand_inspections) {
            let equipmentInspections =  await sails.models.assetequipmentinspection.find({equipment_ref: equipmentIds});
            equipmentInspections = await populateUserRefs(equipmentInspections, 'user_ref', []);
            if(equipmentInspections && equipmentInspections.length) {
                projectAssetEquipments = projectAssetEquipments.filter(equipment => {
                    let equipment_inspection = equipmentInspections.filter(u => u.equipment_ref === equipment.id);
                    equipment.equipment_inspections = equipment_inspection;
                    return equipment;
                });
            }
        } else {
            for (let key in projectAssetEquipments) {
                let assetEquipment = projectAssetEquipments[key];
                let latest_inspection;
                latest_inspection = assetEquipment.latest_inspection;
                assetEquipment.latest_inspection = latest_inspection !== null ? latest_inspection : {};

                if(assetEquipment.latest_inspection && assetEquipment.latest_inspection.user_ref) {
                    assetEquipment.latest_inspection.user_ref = await sails.models.user_reader.findOne({
                        where: {id: assetEquipment.latest_inspection.user_ref},
                        select: ['id', 'first_name', 'last_name']
                    });
                }
                projectAssetEquipments[key] = assetEquipment;
            }

            projectAssetEquipments = projectAssetEquipments.map(equipment => {
                equipment.fault_count = (+equipment.fault_count) ? +equipment.fault_count : 0;
                return equipment;
            });
        }
    }

    let userEmployer = {};
    let userEmployerId = await getUserInductionEmployer(user, projectId);
    if(userEmployerId){
        userEmployer = await sails.models.createemployer_reader.findOne({
            where: {id: userEmployerId},
            select: ['id', 'name', 'country_code']
        });
    }
    sails.log.info(`equipment: projectAssetEquipments count ${projectAssetEquipments.length}`);

    let total_asset = await sails.models.projectassetequipment_reader.count({project_ref: projectId, approval_pending: { '!=' : 3 }});
    return { records:projectAssetEquipments, total_asset, userEmployer };
}

const getEquipmentKey = (assetRecord) => {
    let equipmentType;
    if(assetRecord.equipment_type == ASSET_TYPES.AccessEquipment ) {
        equipmentType= (assetRecord.equipment_type == ASSET_TYPES.AccessEquipment) ? `ae_${(replaceAll(assetRecord.item, ' ', '_')).toLowerCase()}` : '';
    } else if(assetRecord.equipment_type == 'fall_arrest_systems') {
        equipmentType= (assetRecord.equipment_type == ASSET_TYPES.AccessEquipment) ? `fas_${(replaceAll(assetRecord.item, ' ', '_')).toLowerCase()}` : '';
    } else {
        return assetRecord.equipment_type;
    }

    return equipmentType;
};

module.exports = {
    addVehicleAsset: async (req, res) => {
        let payload = _pick((req.body || {}), [
            'vehicle_photos',
            'vehicle_id',
            'type_of_vehicle',
            'power_output',
            'approval_number',
            'engine_manufacturer',
            'year_manufactured',
            'serial_number',
            'examination_cert_number',
            'examination_cert_expiry_date',
            'examination_certificates',
            'service_expiry_date',
            'service_certificates',
            'mot_expiry_date',
            'mot_certificates',
            'approval_pending',
            'activity_logs',
            'arrived_at',
            'tagged_owner',
            'custom_fields'
        ]);

        payload = cleanFields(payload);

        payload.user_ref = req.user.id;
        let revision = await getLatestUserRevision(req.user.id);
        payload.user_revision_ref = revision.id;
        payload.project_ref = +req.param('projectId');
        payload.tagged_owner = _.uniq([...(payload.tagged_owner || [])]);
        let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, payload.project_ref);
        sails.log.info("adding vehicles hasOnlySiteManagement: ", hasOnlySiteManagement);
        if (hasOnlySiteManagement) {
            payload.approval_pending = 1;
        }

        sails.log.info('adding asset vehicle with', payload);
        let assetVehicle = await sails.models.projectassetvehicles.create(payload);
        if(assetVehicle && assetVehicle.id) {
            sails.log.info(assetVehicle.approval_pending);
            //if approval pending status is 1 then send mail to vehicle managers
            if (assetVehicle.approval_pending === 1) {
                sails.log.info("Process to send mail to nominated managers.");
                await sendMailToAssetManagers({alertFor: 'new_asset', assetInfo: assetVehicle, assetType: ASSET_TYPES.AssetVehicle, user: req.user, status: 'approval_pending', comment: 'vehicle'});
            }

            sails.log.info('asset vehicle added successfully.');
            return successResponse(res, {asset_vehicle: assetVehicle});
        }

        sails.log.info('Failed to add asset vehicle.');
        return errorResponse(res, sails.__('Failed to add asset vehicle.'));
    },

    updateVehicleAsset: async (req, res) => {
        let id = +req.param('id');
        let payload = _pick((req.body || {}), [
            'vehicle_photos',
            'vehicle_id',
            'type_of_vehicle',
            'power_output',
            'approval_number',
            'engine_manufacturer',
            'year_manufactured',
            'serial_number',
            'examination_cert_number',
            'examination_cert_expiry_date',
            'examination_certificates',
            'service_expiry_date',
            'service_certificates',
            'mot_expiry_date',
            'mot_certificates',
            'approval_pending',
            'declined_comment',
            'activity_logs',
            'arrived_at',
            'tagged_owner',
            'custom_fields',
        ]);

        if (payload.tagged_owner) {
            payload.tagged_owner = _.uniq([...(payload.tagged_owner || [])]);
        }
        payload = cleanFields(payload);
        sails.log.info('updating asset vehicle with', payload);
        await updateRecord(payload, ASSET_TYPES.AssetVehicle, id, req, res);
    },

    // @Deprecated for web - We are using this only for mobile
    getProjectAssetVehicles: async (req, res) => {
        let projectId = +req.param('projectId');
        let expand_inspections = (req.query.expand_inspections || '').toString().trim() === 'true';
        let includeArchived = (req.query['include_archived'] || '').toString().trim() === 'true';
        let archivedFilter = '';
        if(!includeArchived) {
            archivedFilter = `AND is_archived = false`;
        }

        sails.log.info(`[Mobile] get asset vehicle list for projectId: ${projectId} with include_archived: ${includeArchived}`);
        //check if user has only site management access on project
        let ownerFilter = await getOwnerFilter(req.user, projectId, true);
        sails.log.info(`vehicle: ownerFilter ${ownerFilter}`);
        let rawResult = await sails.sendNativeQuery(`SELECT *
                                                     FROM project_asset_vehicles
                                                     WHERE project_ref = $1
                                                       AND approval_pending IN (1, 2)
                                                       ${ownerFilter}
                                                       ${archivedFilter}
                                                     ORDER BY approval_pending ASC, id DESC`,
            [projectId]
        );

        let projectAssetVehicles = (typeOf(rawResult.rows, 'array') && rawResult.rows.length)  ? rawResult.rows : [];
        let companiesInfo = await expandCompanies(projectAssetVehicles);
        projectAssetVehicles = await populateProjectRefs(projectAssetVehicles, 'project_ref', []);
        let typeOfVehicles = await getTypeOfAssetVehicles();
        const mappedTypes =  getAssetTypeMappedWithSpecificChecklist(typeOfVehicles);
        projectAssetVehicles = projectAssetVehicles.map(record => ({
            ...record,
            has_specific_checklist: mappedTypes[record.type_of_vehicle] || false,
        }));
        let archived_asset_vehicles = [];
        let unarchived_asset_vehicle = [];
        if (projectAssetVehicles && projectAssetVehicles.length) {
            let fileRefs = [];
            projectAssetVehicles.map(vehicle => {
                //attach expanded tagged owner/company
                vehicle.tagged_owner = (companiesInfo || []).filter(company => (vehicle.tagged_owner || []).includes(company.id));
                fileRefs.push(...(vehicle.vehicle_photos), ...(vehicle.examination_certificates), ...(vehicle.service_certificates), ...(vehicle.mot_certificates));
                return vehicle;
            });
            //remove null values
            fileRefs = fileRefs.filter(function (ref) {
                return ref != null;
            });
            if (fileRefs.length) {
                let files = await sails.models.userfile_reader.find({
                    where: {id: fileRefs},
                    select: ['createdAt','id', 'name', 'file_url', 'sm_url', 'md_url', 'user_id', 'category', 'file_mime', 'img_translation']
                });
                projectAssetVehicles = projectAssetVehicles.map(assetVehicle => {
                    assetVehicle.vehicle_photos = (files || []).filter(file => assetVehicle.vehicle_photos.includes(file.id));
                    assetVehicle.examination_certificates = (files || []).filter(file => assetVehicle.examination_certificates.includes(file.id));
                    assetVehicle.service_certificates = (files || []).filter(file => assetVehicle.service_certificates.includes(file.id));
                    assetVehicle.mot_certificates = (files || []).filter(file => assetVehicle.mot_certificates.includes(file.id));
                    return assetVehicle;
                });
            }
            let vehicleIds = projectAssetVehicles.map(vehicle => vehicle.id);
            if (expand_inspections) {
                let vehicleInspections =  await sails.models.assetvehicleinspection_reader.find({vehicle_ref: vehicleIds});
                vehicleInspections = await populateUserRefs(vehicleInspections, 'user_ref', []);
                if(vehicleInspections && vehicleInspections.length) {
                    projectAssetVehicles = projectAssetVehicles.filter(vehicle => {
                        let vehicle_inspection = vehicleInspections.filter(u => u.vehicle_ref === vehicle.id);
                        let dates = [];
                        vehicleInspections = vehicleInspections.reduce((inspections, vi) => {
                            if (!dates.includes(formatTimeInTimezone(vi.createdAt, 'DD-MM-YYYY', fall_back_timezone))) {
                                dates.push(formatTimeInTimezone(vi.createdAt, 'DD-MM-YYYY', fall_back_timezone));
                                inspections.push(vi);
                            }
                            return inspections;
                        }, []);
                        vehicle.vehicle_inspections = vehicle_inspection;
                        return vehicle;
                    });
                }
            } else {
                for (let key in projectAssetVehicles) {
                    let assetVehicle = projectAssetVehicles[key];
                    let latest_inspection = await sails.models.assetvehicleinspection_reader.find({
                        where: {
                            vehicle_ref: assetVehicle.id
                        },
                        select: ['createdAt', 'updatedAt', 'id', 'user_ref'],
                        limit:1
                    }).sort([
                        { createdAt: 'DESC' },
                    ]);
                    assetVehicle.latest_inspection = (latest_inspection && latest_inspection.length) ? latest_inspection[0] : {};
                    if(assetVehicle.latest_inspection && assetVehicle.latest_inspection.user_ref) {
                        assetVehicle.latest_inspection.user_ref = await sails.models.user_reader.findOne({
                            where: {id: assetVehicle.latest_inspection.user_ref},
                            select: ['id', 'first_name', 'last_name']
                        });
                    }
                    projectAssetVehicles[key] = assetVehicle;
                }
                //get open fault count for each vehicle and attach with respective vehicle
                let faults = await getVehiclesFaults(vehicleIds);
                projectAssetVehicles = projectAssetVehicles.map(vehicle => {
                    let faultData = faults.find(fault => fault.vehicle_ref == vehicle.id);
                    vehicle.fault_count = (faultData && faultData.fault_count) ? +faultData.fault_count : 0;
                    return vehicle;
                });
            }
            if (includeArchived === 'true') {
                unarchived_asset_vehicle = (projectAssetVehicles || []).filter(av => !av.is_archived);
                archived_asset_vehicles = (projectAssetVehicles || []).filter(av => av.is_archived);
            } else {
                unarchived_asset_vehicle = projectAssetVehicles;
            }
        }

        let userEmployer = {};
        let userEmployerId = await getUserInductionEmployer(req.user, projectId);
        if(userEmployerId){
            userEmployer = await sails.models.createemployer_reader.findOne({
                where: {id: userEmployerId},
                select: ['id', 'name', 'country_code']
            });
        }
        let total_asset = await sails.models.projectassetvehicles_reader.count({project_ref: projectId, approval_pending: { '!=' : 3 }});
        //projectAssetVehicles is being used in mobile app.
        return successResponse(res, {project_asset_vehicles: projectAssetVehicles, unarchived_asset_vehicle, archived_asset_vehicles, employerList: [], userEmployer, total_asset});
    },

    getProjectAssetVehiclesV2: async (req, res) => {
        let projectId = +req.param('projectId');
        let expand_inspections = req.param('expand_inspections', 'false').toString().trim() === 'true';
        let isArchived = req.param('is_archived', 'false').toString().trim() === 'true';
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let category = req.param('type') ? req.param('type').split(","):[];
        let searchTerm = decodeURIParam((req.param('q', '')).toString().trim());
        let owner = req.param('owner') ? req.param('owner').split(",").map(a=>+a): [];
        let sortKey = req.param('sortKey', 'id');
        let sortDir = req.param('sortDir', 'desc');
        let status = req.param('status') ? req.param('status').split(',').map(a=>+a): [];

        sails.log.info(`get asset vehicle list for projectId: ${projectId} with is_archived: ${isArchived}`);
        let defaultResponse = {
            q: searchTerm,
            pageSize,
            pageNumber,
            sortKey,
            sortDir,
            category,
            status
        };

        //check if user has only site management access on project
        let ownerFilter = await getOwnerFilter(req.user, projectId, true);
        sails.log.info(`vehicle: ownerFilter ${ownerFilter}`);
        let {
            total: totalCount,
            records: projectAssetVehicles
        } = await getProjectVehiclesAssetsList(projectId, pageSize, (pageSize * pageNumber), sortKey,isArchived, sortDir,{
            owner, searchTerm, category,
            ownerFilter, status,
        });

        let totalOnsiteAssets = await sails.models.projectassetvehicles_reader.count({project_ref: projectId, approval_pending: 2, is_archived: isArchived});
        const {records, total_asset, userEmployer} = await getAssetVehiclesMappedWithSpecificChecklist(req.user, projectId, projectAssetVehicles, expand_inspections);
        return successResponse(res, {...defaultResponse, totalCount, records, userEmployer, total_asset, totalOnsiteAssets});
    },

    getAssetTaggedOwners: async (req, res) =>{
        let projectId = +req.param('projectId');
        let isArchived = req.param('is_archived', 'false').toString().trim() === 'true';
        let assetType = req.param('assetType');
        let tagged_owners = [];
        sails.log.info(`[getAssetTaggedOwners]: Preparing tagged owner list for tool ${assetType} where is_archived value is ${isArchived}, project id ${isArchived}`);
        if(assetType === ASSET_TYPES.AssetVehicle){
            tagged_owners = await getTaggedOwnersList('project_asset_vehicles', projectId, true, isArchived);
        } else if(assetType === ASSET_TYPES.AssetEquipment){
            tagged_owners = await getTaggedOwnersList('project_asset_equipment', projectId, true, isArchived);
        } else if(assetType === ASSET_TYPES.AssetTemporaryWork){
            tagged_owners = await getTaggedOwnersList('project_asset_temporary_work', projectId, true, isArchived);
        }

        return successResponse(res, {tagged_owners});
    },
    addAssetVehicleInspection: async (req, res) => {
        let payload = _pick((req.body || {}), [
            'general_checklist',
            'vehicle_specific_checklist',
            'specific_checklist',
            'fault_details',
            'sign',
            'attachment_file_ref',
            'checklist_version',
        ]);

        let projectId = +req.param('projectId', 0);
        if(!projectId){
            sails.log.info('Missing project id')
            return errorResponse(res, "Invalid request");
        }

        payload.vehicle_ref = +req.param('vehicleId');
        payload.user_ref = req.user.id;
        payload.project_ref = projectId;
        let revision = await getLatestUserRevision(req.user.id);
        payload.user_revision_ref = revision.id;
        // if(!payload.vehicle_specific_checklist) {
        //     payload.vehicle_specific_checklist = payload.checklist_version ? [] : {};
        // }
        sails.log.info('Adding asset vehicle inspection.');

        //append fault id to each fault
        if (payload.fault_details && payload.fault_details.length) {
            //Fetch previous inspection contains faults to prepare fault_id
            payload.fault_details = await associateFaultIdToFault(payload.fault_details, payload.vehicle_ref, ASSET_TYPES.AssetVehicle);
        }

        //Prevent duplicate inspection for the day
        let todayInspections = await sails.models.assetvehicleinspection_reader.find({
            where: {
                vehicle_ref: +req.param('vehicleId'),
                createdAt: {'>=': moment().startOf('day').valueOf(), '<=': moment().endOf('day').valueOf()},
            },
            select: ['id']
        });

        if (todayInspections && todayInspections.length) {
            sails.log.info("Today's inspection report for the vehicle has been already submitted.");
            return errorResponse(res, "Today's inspection report for the vehicle has been already submitted.");
        }

        let assetVehicleInspection = await sails.models.assetvehicleinspection.create(payload);
        //Send mail to all vehicle managers if faults present.
        if (assetVehicleInspection.fault_details && assetVehicleInspection.fault_details.length) {
            let assetVehicle = await sails.models.projectassetvehicles_reader.findOne({
                where: {
                    id: assetVehicleInspection.vehicle_ref
                },
                select: ['serial_number', 'project_ref', 'vehicle_id', 'type_of_vehicle', 'tagged_owner']
            });

            assetVehicle.tagged_owner = await expandCompanies([assetVehicle]);

            let fileRefs = (assetVehicleInspection.fault_details).reduce((arr, fault) => {
                if(fault.images && fault.images.length) {
                    arr.push(...(fault.images));
                }
                return arr;
            }, []);

            if (fileRefs.length) {
                let files = await sails.models.userfile_reader.find({
                    where: {
                        id: fileRefs
                    },
                    select: ['id','file_url', 'file_mime']
                });

                assetVehicleInspection.fault_details = (assetVehicleInspection.fault_details).map(fault => {
                    if (fault.images && fault.images.length) {
                        fault.images = (files || []).filter(file => fault.images.includes(file.id));
                    }
                    return fault;
                })
            }

            await sendMailToAssetManagers({
                alertFor: 'fault_present',
                assetInfo: assetVehicle,
                assetInspection: assetVehicleInspection,
                assetType: ASSET_TYPES.AssetVehicle,
                user: req.user
            });
        }
        return successResponse(res,{asset_vehicle_inspection: (assetVehicleInspection && assetVehicleInspection.id) ? assetVehicleInspection : null});
    },

    archiveUnarchiveVehicleAsset: async (req, res) => {
        let id = +req.param('id');
        let payload = { 'is_archived': req.body.is_archived, 'activity_logs': req.body.activity_logs};
        sails.log.info('archive vehicle asset with', payload);
        await updateRecord(payload, ASSET_TYPES.AssetVehicle, id, req, res);
    },

    getMostRecentVehicleInspection: async (req, res) => {
        let vehicleId = +req.param('vehicleId');

        sails.log.info('Get most recent inspection of vehicle: ', vehicleId);
        let assetVehicleInspection = await sails.models.assetvehicleinspection_reader.find({
            where: {
                vehicle_ref: vehicleId
            },
            select: ['general_checklist', 'createdAt', 'id'],
            limit:1
        }).sort([
            { createdAt: 'DESC' },
        ]);
        return successResponse(res,{asset_vehicle_inspection: (assetVehicleInspection.length) ? assetVehicleInspection[0] : []});
    },

    addEquipmentAsset: async (req, res) => {
        let payload = _pick((req.body || {}), [
            'equipment_id',
            'equipment_type',
            'safe_working_load',
            'item',
            'equipment_photos',
            'serial_number',
            'examination_cert_number',
            'examination_cert_expiry_date',
            'examination_certificates',
            'pat_test_expiry_date',
            'approval_pending',
            'activity_logs',
            'arrived_at',
            'tagged_owner',
            'custom_fields'
        ]);

        payload = cleanFields(payload);

        payload.user_ref = req.user.id;
        let revision = await getLatestUserRevision(req.user.id);
        payload.user_revision_ref = revision.id;
        payload.project_ref = +req.param('projectId');
        payload.tagged_owner = _.uniq([...(payload.tagged_owner || [])]);
        let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, payload.project_ref);
        sails.log.info("adding equipment hasOnlySiteManagement: ", hasOnlySiteManagement);
        if (hasOnlySiteManagement) {
            payload.approval_pending = 1;
        }

        sails.log.info('adding asset equipment with', payload);
        let assetEquipment = await sails.models.projectassetequipment.create(payload);
        if(assetEquipment && assetEquipment.id) {
            sails.log.info(assetEquipment.approval_pending);
            //if approval pending status is 1 then send mail to equipment managers
            if (assetEquipment.approval_pending === 1) {
                sails.log.info("Process to send mail to nominated managers.");
                await sendMailToAssetManagers({alertFor: 'new_asset', assetInfo: assetEquipment, assetType: ASSET_TYPES.AssetEquipment, user: req.user, status: 'approval_pending'});
            }

            sails.log.info('asset equipment added successfully.');
            return successResponse(res, {asset_equipment: assetEquipment});
        }

        sails.log.info('Failed to add asset equipment.');
        return errorResponse(res, sails.__('Failed to add asset equipment.'));
    },

    updateEquipmentAsset: async (req, res) => {
        let id = +req.param('id');
        let payload = _pick((req.body || {}), [
            'equipment_id',
            'equipment_type',
            'safe_working_load',
            'item',
            'equipment_photos',
            'serial_number',
            'examination_cert_number',
            'examination_cert_expiry_date',
            'examination_certificates',
            'pat_test_expiry_date',
            'approval_pending',
            'declined_comment',
            'activity_logs',
            'arrived_at',
            'tagged_owner',
            'custom_fields'
        ]);

        if (payload.tagged_owner) {
            payload.tagged_owner = _.uniq([...(payload.tagged_owner || [])]);
        }
        payload = cleanFields(payload);
        sails.log.info('updating asset equipment with', payload);
        await updateRecord(payload, ASSET_TYPES.AssetEquipment, id, req, res);
    },

    // @Deprecated for web - We are using this only for mobile
    getProjectAssetEquipments: async (req, res) => {
        let projectId = +req.param('projectId');
        let expand_inspections = (req.query.expand_inspections || '').toString().trim() === 'true';
        let includeArchived = (req.query['include_archived'] || '').toString().trim() === 'true';

        let archivedFilter = '';
        if(includeArchived === 'false') {
            archivedFilter = `AND is_archived = false`;
        }

        //check if user has only site management access on project
        let ownerFilter = await getOwnerFilter(req.user, projectId, true);
        sails.log.info(`equipment: ownerFilter ${ownerFilter}`);
        let rawResult = await sails.sendNativeQuery(`SELECT *
                                                     FROM project_asset_equipment
                                                     WHERE project_ref = $1
                                                       AND approval_pending IN (1, 2)
                                                       ${ownerFilter}
                                                       ${archivedFilter}
                                                     ORDER BY approval_pending ASC, id DESC`,
                                                     [projectId]
        );

        let projectAssetEquipments = (typeOf(rawResult.rows, 'array') && rawResult.rows.length)  ? rawResult.rows : [];
        let companiesInfo = await expandCompanies(projectAssetEquipments);
        projectAssetEquipments = await populateProjectRefs(projectAssetEquipments, 'project_ref', []);
        let typeOfEquipment = await getTypeOfAssetEquipment();
        const mappedTypes =  getAssetTypeMappedWithSpecificChecklist(typeOfEquipment);
        projectAssetEquipments = projectAssetEquipments.map(record => ({
            ...record,
            has_specific_checklist: mappedTypes[record.equipment_type] || false,
        }));
        let archived_asset_equipments = [];
        let unarchived_asset_equipment = [];
        if (projectAssetEquipments && projectAssetEquipments.length) {
            let fileRefs = [];
            projectAssetEquipments.map(equipment => {
                //attach expanded tagged owner/company
                equipment.tagged_owner = (companiesInfo || []).filter(company => (equipment.tagged_owner).includes(company.id));
                fileRefs.push(...(equipment.equipment_photos), ...(equipment.examination_certificates));
                return equipment;
            });
            //remove null values
            fileRefs = fileRefs.filter(function (ref) {
                return ref != null;
            });
            if (fileRefs.length) {
                let files = await sails.models.userfile.find({
                    where: {id: fileRefs},
                    select: ['createdAt', 'file_mime', 'id', 'name', 'file_url', 'sm_url', 'md_url', 'user_id', 'category', 'img_translation']
                });
                projectAssetEquipments = projectAssetEquipments.map(assetEquipment => {
                    assetEquipment.equipment_photos = (files || []).filter(file => assetEquipment.equipment_photos.includes(file.id));
                    assetEquipment.examination_certificates = (files || []).filter(file => assetEquipment.examination_certificates.includes(file.id));
                    return assetEquipment;
                });
            }
            let equipmentIds = projectAssetEquipments.map(equipment => equipment.id);
            if (expand_inspections) {
                let equipmentInspections = await sails.models.assetequipmentinspection.find({equipment_ref: equipmentIds});
                equipmentInspections = await populateUserRefs(equipmentInspections, 'user_ref', []);
                if (equipmentInspections && equipmentInspections.length) {
                    projectAssetEquipments = projectAssetEquipments.filter(equipment => {
                        let equipment_inspection = equipmentInspections.filter(u => u.equipment_ref === equipment.id);
                        equipment.equipment_inspections = equipment_inspection;
                        return equipment;
                    });
                }
            } else {
                for (let key in projectAssetEquipments) {
                    let assetEquipment = projectAssetEquipments[key];
                    let latest_inspection = await sails.models.assetequipmentinspection.find({
                        where: {
                            equipment_ref: assetEquipment.id
                        },
                        select: ['createdAt', 'updatedAt', 'id', 'user_ref'],
                        limit: 1
                    }).sort([
                        {createdAt: 'DESC'},
                    ]);
                    assetEquipment.latest_inspection = (latest_inspection && latest_inspection.length) ? latest_inspection[0] : {};
                    if (assetEquipment.latest_inspection && assetEquipment.latest_inspection.user_ref) {
                        assetEquipment.latest_inspection.user_ref = await sails.models.user_reader.findOne({
                            where: {id: assetEquipment.latest_inspection.user_ref},
                            select: ['id', 'first_name', 'last_name']
                        });
                    }
                    projectAssetEquipments[key] = assetEquipment;
                }
                //get open fault count for each equipment and attach with respective equipment
                let faults = await getEquipmentFaults(equipmentIds);
                projectAssetEquipments = projectAssetEquipments.map(equipment => {
                    let faultData = faults.find(fault => fault.equipment_ref == equipment.id);
                    equipment.fault_count = (faultData && faultData.fault_count) ? +faultData.fault_count : 0;
                    return equipment;
                });
            }

            if (includeArchived === 'true') {
                unarchived_asset_equipment = (projectAssetEquipments || []).filter(av => !av.is_archived);
                archived_asset_equipments = (projectAssetEquipments || []).filter(av => av.is_archived);
            } else {
                unarchived_asset_equipment = projectAssetEquipments;
            }
        }

        let userEmployer = {};
        let userEmployerId = await getUserInductionEmployer(req.user, projectId);
        if(userEmployerId){
            userEmployer = await sails.models.createemployer_reader.findOne({
                where: {id: userEmployerId},
                select: ['id', 'name', 'country_code']
            });
        }
        sails.log.info(`equipment: projectAssetVehicles count ${projectAssetEquipments.length}`);

        let total_asset = await sails.models.projectassetequipment_reader.count({project_ref: projectId, approval_pending: { '!=' : 3 }});
        //projectAssetEquipments is being used in mobile app.
        return successResponse(res, {project_asset_equipments: projectAssetEquipments, unarchived_asset_equipment, archived_asset_equipments, employerList: [], userEmployer, total_asset});
    },

    getProjectAssetEquipmentsV2: async (req, res) => {
        let projectId = +req.param('projectId');
        let expand_inspections = req.param('expand_inspections', 'false').toString().trim() === 'true';
        let isArchived = req.param('is_archived', 'false').toString().trim() === 'true';
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let category = req.param('type') ? req.param('type').split(","):[];
        let searchTerm = decodeURIParam((req.param('q', '')).toString().trim());
        let owner = req.param('owner') ? req.param('owner').split(",").map(a=>+a): [];
        let sortKey = req.param('sortKey', 'id');
        let sortDir = req.param('sortDir', 'desc');
        let status = req.param('status') ? req.param('status').split(',').map(a=>+a): [];

        sails.log.info(`get asset equipment list for projectId: ${projectId} with is_archived: ${isArchived}`);
        let defaultResponse = {
            q: searchTerm,
            pageSize,
            pageNumber,
            sortKey,
            sortDir,
            category,
            status
        };

        //check if user has only site management access on project
        let ownerFilter = await getOwnerFilter(req.user, projectId, true);
        sails.log.info(`vehicle: ownerFilter ${ownerFilter}`);
        let {
            total: totalCount,
            records: assetEquipments
        } = await getProjectEquipmentAssetsList(projectId, pageSize, (pageSize * pageNumber), sortKey,isArchived, sortDir,{
            owner, searchTerm, category,
            ownerFilter, status,
        });
        let totalOnsiteAssets = await sails.models.projectassetequipment_reader.count({project_ref: projectId, approval_pending: 2, is_archived: isArchived});
        const {records, total_asset, userEmployer} = await getAssetEquipmentMappedWithSpecificChecklist(req.user, projectId, assetEquipments, expand_inspections, isArchived);
        return successResponse(res, {...defaultResponse, totalCount, records, userEmployer, total_asset, totalOnsiteAssets});
    },

    addAssetEquipmentInspection: async (req, res) => {
        let payload = _pick((req.body || {}), [
            'general_checklist',
            'equipment_specific_checklist',
            'specific_checklist',
            'fault_details',
            'sign',
            'attachment_file_ref',
            'checklist_version',
        ]);

        /**
         * @todo - Remove this once all app users are migrated to mobile-iOS 4.0.13(1)
         * Rename all the code that uses equipment_specific_checklist to specific_checklist
         * Rename DB column equipment_specific_checklist to specific_checklist
         */
        //If specific checklist is present then assign it to equipment_specific_checklist
        if(payload && payload.specific_checklist && payload.specific_checklist.length) {
            payload.equipment_specific_checklist = payload.specific_checklist;
        }

        let projectId = +req.param('projectId', 0);
        if(!projectId){
            sails.log.info('Missing project id')
            return errorResponse(res, "Invalid request");
        }

        let {validationError} = addEquipmentInspection(payload, 'equipment');
        if(validationError){
            return errorResponse(res, 'Invalid Request.', {validationError});
        }

        payload.equipment_ref = req.param('equipmentId');
        payload.user_ref = req.user.id;
        payload.project_ref = projectId;
        let revision = await getLatestUserRevision(req.user.id);
        payload.user_revision_ref = revision.id;

        //append fault id to each fault
        if (payload.fault_details && payload.fault_details.length) {
            //Fetch previous inspection contains faults to prepare fault_id
            payload.fault_details = await associateFaultIdToFault(payload.fault_details, payload.equipment_ref, ASSET_TYPES.AssetEquipment);
        }

        //Prevent duplicate inspection for the day
        let todayInspections = await sails.models.assetequipmentinspection_reader.find({
            where: {
                equipment_ref: +req.param('equipmentId'),
                createdAt: {'>=': moment().startOf('day').valueOf(), '<=': moment().endOf('day').valueOf()},
            },
            select: ['id']
        });

        if (todayInspections && todayInspections.length) {
            sails.log.info("Today's inspection report for the equipment has been already submitted.");
            return errorResponse(res, "Today's inspection report for the equipment has been already submitted.");
        }

        sails.log.info('Adding asset equipment inspection.');
        let assetEquipmentInspection = await sails.models.assetequipmentinspection.create(payload);

        //Send mail to all equipment managers if faults present.
        if (assetEquipmentInspection.fault_details && assetEquipmentInspection.fault_details.length) {
            let assetEquipment = await sails.models.projectassetequipment_reader.findOne({
                where: {
                    id: assetEquipmentInspection.equipment_ref
                },
                select: ['serial_number', 'project_ref', 'equipment_id', 'equipment_type', 'tagged_owner']
            });

            assetEquipment.tagged_owner = await expandCompanies([assetEquipment]);

            await sendMailToAssetManagers({
                alertFor: 'fault_present',
                assetInfo: assetEquipment,
                assetInspection: assetEquipmentInspection,
                assetType: ASSET_TYPES.AssetEquipment,
                user: req.user
            });
        }
        return successResponse(res,{asset_equipment_inspection: (assetEquipmentInspection && assetEquipmentInspection.id) ? assetEquipmentInspection : null});
    },

    archiveUnarchiveEquipmentAsset: async (req, res) => {
        let id = +req.param('id');
        let payload = { 'is_archived': req.body.is_archived, 'activity_logs': req.body.activity_logs};
        sails.log.info('archive equipment asset with', payload);
        await updateRecord(payload, ASSET_TYPES.AssetEquipment, id, req, res);
    },

    downloadRegisterXLSX: async (req, res) => {
        let projectId = +req.param('projectId');
        let assetType = req.param('assetType');
        // let assetIds = req.body.asset_ids|| [];
        let assetIds = [];
        sails.log.info(`Download xls of asset-register with filter:`);
        sails.log.info(`Download xls of asset-register with filter assetIds:`, assetIds);
        let projectInfo = await sails.models.project_reader.findOne({
            select: ['name', 'project_number', 'custom_field'],
            where: {
                id: projectId
            }
        });
        sails.log.info(`Found project with name:`, projectInfo.name);
        //check if user has only site management access on project
        let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, projectId);
        let uacRecord = (req.user.raw_uac).find(uac => (uac.resource === `project.${projectId}`));
        let isNormalUser = (uacRecord == undefined);
        let assetInspections = [];
        let startingNoOfEscaped = 1;

        let ownerFilter = ''
        if (hasOnlySiteManagement || isNormalUser) {
            let userEmployer = await getUserInductionEmployer(req.user, projectId);
            ownerFilter = `AND ${userEmployer} = ANY(tagged_owner)`
        }
        if (assetType == 'vehicle') {
            sails.log.info(`Processing asset vehicle.`);
            assetIds = await sails.models.projectassetvehicles_reader.find({
                select:['id'],
                where:{
                    project_ref:projectId,
                    approval_pending: { '!=': 3 }
                }
            })
            assetIds = assetIds.map(asset => asset.id);
            let id_IN_query = assetIds.length >= 1 ? `AND id IN (${(assetIds || []).map(() => {
                startingNoOfEscaped++;
                return `$${startingNoOfEscaped}`;
            }).join(',')})` : '';
            startingNoOfEscaped++;

            //let TYPE_OF_VEHICLE = await getTypeOfAssetVehicles();
            let rawResult = await sails.sendNativeQuery(`SELECT id, vehicle_id, is_archived, type_of_vehicle, serial_number, tagged_owner, examination_cert_expiry_date, service_expiry_date, mot_expiry_date, arrived_at, activity_logs
                    FROM project_asset_vehicles
                    WHERE project_ref = $1
                    ${id_IN_query}
                    ${ownerFilter}
                    ORDER BY id ASC`,
                    [projectId, ...(assetIds)]
                );
            let assetRecords = (typeOf(rawResult.rows, 'array') && rawResult.rows.length)  ? rawResult.rows : [];
            assetInspections = await getAssetInspections(ASSET_TYPES.AssetVehicle, assetIds, ['specific_checklist', 'vehicle_specific_checklist', 'general_checklist']);
            let companiesInfo = await expandCompanies(assetRecords);
            assetRecords = (assetRecords || []).map(record => {
                //let vehicleType = (TYPE_OF_VEHICLE || []).find(typeArr => typeArr.key === record.type_of_vehicle);
                //record.type_of_vehicle = (vehicleType && vehicleType.value) ? vehicleType.value : '';
                record.inspections = (assetInspections || []).filter(inspection => record.id == inspection.vehicle_ref);
                record.tagged_owner = (companiesInfo || []).filter(company => (record.tagged_owner).includes(company.id));
                return record;
            });
            assetRecords = await mapAlternatePhrasing(assetRecords, projectId, ASSET_TYPES.AssetVehicle, 'type_of_vehicle');
            await downloadRegister(res, assetRecords, assetType, projectInfo);
        } else if (assetType == 'equipment') {
            sails.log.info(`Processing asset equipment.`);
            assetIds = await sails.models.projectassetequipment_reader.find({
                select:['id'],
                where:{
                    project_ref:projectId,
                    approval_pending: { '!=': 3 }
                }
            })
            assetIds = assetIds.map(asset => asset.id);
            let id_IN_query = assetIds.length >= 1 ? `AND id IN (${(assetIds || []).map(() => {
                startingNoOfEscaped++;
                return `$${startingNoOfEscaped}`;
            }).join(',')})` : '';
            startingNoOfEscaped++;
            let rawResult = await sails.sendNativeQuery(`SELECT id, equipment_id, is_archived, equipment_type, item, serial_number, tagged_owner, examination_cert_expiry_date, pat_test_expiry_date, arrived_at, activity_logs
                                                     FROM project_asset_equipment
                                                     WHERE project_ref = $1
                                                       ${id_IN_query}
                                                       ${ownerFilter}
                                                     ORDER BY id ASC`,
                [projectId, ...(assetIds)]
            );
            let assetRecords = (typeOf(rawResult.rows, 'array') && rawResult.rows.length)  ? rawResult.rows : [];
            //let TYPE_OF_EQUIPMENT = await getTypeOfAssetEquipment();
            sails.log.info(`Found ${assetRecords.length} asset equipment.`);
            assetInspections = await getAssetInspections(ASSET_TYPES.AssetEquipment, assetIds);
            let companiesInfo = await expandCompanies(assetRecords);
            assetRecords = (assetRecords || []).map(record => {
                //let equipmentType = (TYPE_OF_EQUIPMENT || []).find(typeArr => typeArr.key === record.equipment_type);
                //record.equipment_type = (equipmentType && equipmentType.value) ? equipmentType.value : '';
                record.inspections = (assetInspections || []).filter(inspection => record.id == inspection.equipment_ref);
                record.tagged_owner = (companiesInfo || []).filter(company => (record.tagged_owner).includes(company.id));
                return record;
            });
            assetRecords = await mapAlternatePhrasing(assetRecords, projectId, ASSET_TYPES.AssetEquipment, 'equipment_type');
            sails.log.info(`Number of asset records ...BEOFRE: ${assetRecords.length}.`);
            await downloadRegister(res, assetRecords, assetType, projectInfo);
        } else if (assetType == ASSET_TYPES.AssetTemporaryWork) {
            sails.log.info(`Processing asset temporary works.`);
            assetIds = await sails.models.projectassettemporarywork_reader.find({
                select:['id'],
                where:{
                    project_ref:projectId,
                    approval_pending: { '!=': 3 }
                }
            })
            assetIds = assetIds.map(asset => asset.id);
            let id_IN_query = assetIds.length >= 1 ? `AND id IN (${(assetIds || []).map(() => {
                startingNoOfEscaped++;
                return `$${startingNoOfEscaped}`;
            }).join(',')})` : '';
            let rawResult = await sails.sendNativeQuery(`SELECT *
                                                     FROM project_asset_temporary_work
                                                     WHERE project_ref = $1
                                                       ${id_IN_query}
                                                       ${ownerFilter}
                                                     ORDER BY id ASC`,
                [projectId, ...(assetIds)]
            );
            let assetRecords = (typeOf(rawResult.rows, 'array') && rawResult.rows.length)  ? rawResult.rows : [];
            //let TYPE_OF_TEMPORARY_WORKS = await getTypeOfAssetTemporaryWork();
            sails.log.info(`Found ${assetRecords.length} asset temporary works.`);
            assetInspections = await getAssetInspections(ASSET_TYPES.AssetTemporaryWork, assetIds);
            let companiesInfo = await expandCompanies(assetRecords);
            assetRecords = (assetRecords || []).map(record => {
                //let workType = (TYPE_OF_TEMPORARY_WORKS || []).find(typeArr => typeArr.key === record.type_of_works);
                //record.work_type = (workType && workType.value) ? workType.value : '';
                record.inspections = (assetInspections || []).filter(inspection => record.id == inspection.temporary_work_ref);
                record.tagged_owner = (companiesInfo || []).filter(company => (record.tagged_owner).includes(company.id));
                return record;
            });
            assetRecords = await mapAlternatePhrasing(assetRecords, projectId, ASSET_TYPES.AssetTemporaryWork, 'type_of_works');
            sails.log.info(`Number of asset records ...BEOFRE: ${assetRecords.length}.`);
            await downloadRegister(res, assetRecords, assetType, projectInfo);
        }
    },

    getProjectAssetTemporaryWorks:  async (req, res) => {
        let projectId = +req.param('projectId');
        let expand_inspections = (req.query.expand_inspections || '').toString().trim() === 'true';
        let includeArchived = req.query['include_archived'];
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let category = req.param('type') ? req.param('type').split(","):[];
        let searchTerm = req.param('search');
        let owner = req.param('owner') ? req.param('owner').split(",").map(a=>+a): [];
        let sortKey = req.param('sortKey', 'id');
        let sortDir = req.param('sortDir', 'desc');
        let status = req.param('status') ? req.param('status').split(',').map(a=>+a): [];

        let defaultResponse = {
            q: searchTerm,
            pageSize,
            pageNumber,
            sortKey,
            sortDir,
            category,
            status
        };

        let ownerFilter = await getOwnerFilter(req.user, projectId, true);
        sails.log.info("Temporary Works ownerFilter: ", ownerFilter);
        let {
            total: totalCount,
            records: projectAssetTempWorks
        } = await getProjectTemporaryAssets(projectId, pageSize, (pageSize * pageNumber), sortKey, sortDir,{
            owner, searchTerm, category, includeArchived,
            ownerFilter, status,
        });
        let companiesInfo = await expandCompanies(projectAssetTempWorks);
        projectAssetTempWorks = await mapAlternatePhrasing(projectAssetTempWorks, projectId, ASSET_TYPES.AssetTemporaryWork, 'type_of_works');
        projectAssetTempWorks = await populateProjectRefs(projectAssetTempWorks, 'project_ref', []);
        let typeOfTempWorks = await getTypeOfAssetTemporaryWork();
        const mappedTypes =  getAssetTypeMappedWithSpecificChecklist(typeOfTempWorks);
        projectAssetTempWorks = projectAssetTempWorks.map(record => ({
        ...record,
        has_specific_checklist: mappedTypes[record.type_of_works] || false,
        inspection_key: record.type_of_works
        }));
        let archived_asset_temp_works = [];
        let unarchived_asset_temp_works = [];
        if (projectAssetTempWorks && projectAssetTempWorks.length) {
            let fileRefs = [];
            projectAssetTempWorks.map(tempWork => {
                tempWork.tagged_owner = (companiesInfo || []).filter(company => tempWork.tagged_owner.includes(company.id));
                fileRefs.push(...tempWork.photos);
                tempWork.approval_pending_message = buildAssetStatusMessage(tempWork.approval_pending);
                return tempWork;
            });
            fileRefs = fileRefs.filter(function (ref) {
                return ref != null;
            });
            if (fileRefs.length) {
                let files = await sails.models.userfile_reader.find({
                    where: {id: fileRefs},
                    select: ['createdAt','file_mime', 'id', 'name', 'file_url', 'sm_url', 'md_url', 'user_id', 'img_translation']
                });
                projectAssetTempWorks = projectAssetTempWorks.map(asset => {
                    asset.photos = (files || []).filter(file => asset.photos.includes(file.id));
                    return asset;
                });
            }
            let tempWorkIds = projectAssetTempWorks.map(item => item.id);
            if (expand_inspections) {
                let tempWorkInspections =  await sails.models.assettemporaryworkinspection_reader.find({temporary_work_ref: tempWorkIds});
                tempWorkInspections = await populateUserRefs(tempWorkInspections, 'user_ref', []);
                if(tempWorkInspections && tempWorkInspections.length) {
                    projectAssetTempWorks = projectAssetTempWorks.filter(item => {
                        let work_inspection = tempWorkInspections.filter(u => u.temporary_work_ref === item.id);
                        let dates = [];
                        tempWorkInspections = tempWorkInspections.reduce((inspections, vi) => {
                            if (!dates.includes(formatTimeInTimezone(vi.createdAt, 'DD-MM-YYYY', fall_back_timezone))) {
                                dates.push(formatTimeInTimezone(vi.createdAt, 'DD-MM-YYYY', fall_back_timezone));
                                inspections.push(vi);
                            }
                            return inspections;
                        }, []);
                        item.work_inspections = work_inspection;
                        return item;
                    });
                }
            } else {
                for (let key in projectAssetTempWorks) {
                    let assetTempWork = projectAssetTempWorks[key];
                    let latest_inspection = await sails.models.assettemporaryworkinspection_reader.find({
                        where: {
                            temporary_work_ref: assetTempWork.id
                        },
                        select: ['createdAt', 'updatedAt', 'id', 'user_ref'],
                        limit:1
                    }).sort([
                        { createdAt: 'DESC' },
                    ]);
                    assetTempWork.latest_inspection = (latest_inspection && latest_inspection.length) ? latest_inspection[0] : {};
                    if(assetTempWork.latest_inspection && assetTempWork.latest_inspection.user_ref) {
                        assetTempWork.latest_inspection.user_ref = await sails.models.user_reader.findOne({
                            where: {id: assetTempWork.latest_inspection.user_ref},
                            select: ['id', 'first_name', 'last_name']
                        });
                    }
                    projectAssetTempWorks[key] = assetTempWork;
                }
                let faults = await getTemporaryWorkFaults(tempWorkIds);
                projectAssetTempWorks = projectAssetTempWorks.map(item => {
                    let faultData = faults.find(fault => fault.temporary_work_ref == item.id);
                    item.fault_count = (faultData && faultData.fault_count) ? +faultData.fault_count : 0;
                    return item;
                });
            }

            if (includeArchived === 'true') {
                unarchived_asset_temp_works = (projectAssetTempWorks || []).filter(av => !av.is_archived);
                archived_asset_temp_works = (projectAssetTempWorks || []).filter(av => av.is_archived);
            } else {
                unarchived_asset_temp_works = projectAssetTempWorks;
            }
        }

        let userEmployer = {};
        let userEmployerId = await getUserInductionEmployer(req.user, projectId);
        if(userEmployerId){
            userEmployer = await sails.models.createemployer_reader.findOne({
                where: {id: userEmployerId},
                select: ['id', 'name', 'country_code']
            });
        }
        return successResponse(res, {...defaultResponse, totalCount, project_asset_temp_works: projectAssetTempWorks, unarchived_asset_temp_works, archived_asset_temp_works, employerList: [], userEmployer});
    },

    updateTemporayrWorkAsset: async (req, res) => {
        let id = +req.param('id');
        let payload = _pick((req.body || {}), [
            'item_id',
            'type_of_works',
            'item',
            'photos',
            'serial_number',
            'is_archived',
            'approval_pending',
            'activity_logs',
            'examination_cert_number',
            'examination_cert_expiry_date',
            'examination_certificates',
            'arrived_at',
            'tagged_owner',
            'declined_comment',
            'risk_category',
            'custom_fields'
        ]);

        //payload.tagged_owner = _.uniq([...(payload.tagged_owner || [])]);
        payload = cleanFields(payload);
        sails.log.info('updating asset temporary work with', payload);
        await updateRecord(payload, ASSET_TYPES.AssetTemporaryWork, id, req, res);
    },

    addTemporaryWorkAsset: async (req, res) => {
        let payload = _pick((req.body || {}), [
            'item_id',
            'type_of_works',
            'item',
            'photos',
            'serial_number',
            'approval_pending',
            'activity_logs',
            'examination_cert_number',
            'examination_cert_expiry_date',
            'examination_certificates',
            'arrived_at',
            'tagged_owner',
            'risk_category',
            'custom_fields'
        ]);
        payload = cleanFields(payload);
        payload.user_ref = req.user.id;
        let revision = await getLatestUserRevision(req.user.id);
        payload.user_revision_ref = revision.id;
        payload.project_ref = +req.param('projectId');
        payload.tagged_owner = _.uniq([...(payload.tagged_owner || [])]);
        let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, payload.project_ref);
        sails.log.info("adding temporary work hasOnlySiteManagement: ", hasOnlySiteManagement);
        if (hasOnlySiteManagement) {
            payload.approval_pending = 1;
        }
        sails.log.info('adding asset temporary work with', payload);
        let temporaryWorkAsset = await sails.models.projectassettemporarywork.create(payload);
        if(temporaryWorkAsset && temporaryWorkAsset.id) {
            if (temporaryWorkAsset.approval_pending === 1) {
                sails.log.info("Process to send mail to nominated managers.");
                await sendMailToAssetManagers({alertFor: 'new_asset', assetInfo: temporaryWorkAsset, assetType: ASSET_TYPES.AssetTemporaryWork, user: req.user, status: 'approval_pending'});
            }
            sails.log.info('asset temporary work added successfully.');
            return successResponse(res, {temporaryWorkAsset: temporaryWorkAsset});
        }
        sails.log.info('Failed to add asset temporary work.');
        return errorResponse(res, sails.__('Failed to add temporary work asset.'));
    },

    addAssetTemporaryWorkInspection: async (req, res) => {
        let payload = _pick((req.body || {}), [
            'general_checklist',
            'temporary_work_specific_checklist',
            'specific_checklist',
            'fault_details',
            'sign',
            'attachment_file_ref',
            'checklist_version',
        ]);

        /**
         * @todo - Remove this once all app users are migrated to mobile-iOS 4.0.13(1).
         * Rename all the code that uses temporary_work_specific_checklist to specific_checklist
         * Rename DB column temporary_work_specific_checklist to specific_checklist
         */
        //If specific checklist is present then assign it to temporary_work_specific_checklist
        if(payload && payload.specific_checklist && payload.specific_checklist.length) {
            payload.temporary_work_specific_checklist = payload.specific_checklist;
        }

        let projectId = +req.param('projectId', 0);
        if(!projectId){
            sails.log.info('Missing project id')
            return errorResponse(res, "Invalid request");
        }

        let {validationError} = addTemporaryWorkInspection(payload);
        if(validationError){
            return errorResponse(res, 'Invalid Request.', {validationError});
        }

        payload.temporary_work_ref = req.param('temporaryWorkId');
        payload.user_ref = req.user.id;
        payload.project_ref = projectId;
        let revision = await getLatestUserRevision(req.user.id);
        payload.user_revision_ref = revision.id;
        //append fault id to each fault
        if (payload.fault_details && payload.fault_details.length) {
            //Fetch previous inspection contains faults to prepare fault_id
            payload.fault_details = await associateFaultIdToFault(payload.fault_details, payload.temporary_work_ref, ASSET_TYPES.AssetTemporaryWork);
        }
        //Prevent duplicate inspection for the day
        let todayInspections = await sails.models.assettemporaryworkinspection_reader.find({
            where: {
                temporary_work_ref: +req.param('temporaryWorkId'),
                createdAt: {'>=': moment().startOf('day').valueOf(), '<=': moment().endOf('day').valueOf()},
            },
            select: ['id']
        });
        if (todayInspections && todayInspections.length) {
            sails.log.info("Today's inspection report for the temporary work has been already submitted.");
            return errorResponse(res, "Today's inspection report for the temporary work has been already submitted.");
        }

        sails.log.info('Adding asset temporary work inspection.');
        let assetTempWorkInspection = await sails.models.assettemporaryworkinspection.create(payload);
        //Send mail to all temp work managers if faults present.
        if (assetTempWorkInspection.fault_details && assetTempWorkInspection.fault_details.length) {
            let assetTempWork = await sails.models.projectassettemporarywork_reader.findOne({
                where: {
                    id: assetTempWorkInspection.temporary_work_ref
                },
                select: ['serial_number', 'project_ref', 'item_id', 'type_of_works', 'tagged_owner']
            });

            assetTempWork.tagged_owner = await expandCompanies([assetTempWork]);

            await sendMailToAssetManagers({
                alertFor: 'fault_present',
                assetInfo: assetTempWork,
                assetInspection: assetTempWorkInspection,
                assetType: ASSET_TYPES.AssetTemporaryWork,
                user: req.user
            });
        }
        return successResponse(res,{asset_temp_work_inspection: (assetTempWorkInspection && assetTempWorkInspection.id) ? assetTempWorkInspection : null});
    },

    getAsset: async(req, res) => {
        let id = +req.param('id');
        let assetType = req.param('assetType');
        let expand_inspections = (req.query.expand_inspections || '').toString().trim() === 'true';
        sails.log.info('Fetch asset id:', id, ' type ', assetType);
        let assetData = ASSET_TYPE_DATA[assetType];
        let dbTable = assetData['databaseTableReader'];
        let asset = await sails.models[dbTable].findOne({id: id}).populate('project_ref');
        asset = await expandAsset(asset, expand_inspections, assetData);
        let responeObj = {};
        responeObj[assetType.replace('-', '_')] = asset;
        return successResponse(res, responeObj);
    },

    getAssetInspections: async (req, res) => {
        let assetId = +req.param('assetId');
        let assetType = req.param('assetType');
        let assetData = ASSET_TYPE_DATA[assetType];
        let assetInspectionTable = assetData['inpsectionDBTableReader'];
        let inspectionKey = assetData['inspectionTypeKey'];
        let fromDateParam = (req.query.from_date || '').toString().trim();
        let toDateParam = (req.query.to_date || '').toString().trim();
        let fromDate = moment(fromDateParam, 'DD-MM-YYYY').startOf('day').valueOf();
        let toDate = moment(toDateParam, 'DD-MM-YYYY').endOf('day').valueOf();
        sails.log.info('Fetch inspections for asset, id:', assetId, ' type ', assetType);
        let where = {};
        where[inspectionKey] = assetId;
        if (fromDate && toDate) {
            where.createdAt = {'>=': fromDate, '<=': toDate};
        }
        let asset_inspections =  await sails.models[assetInspectionTable].find({ where: where }).populate(inspectionKey);
        asset_inspections = await populateUserRefs(asset_inspections, 'user_ref', []);

        let responeObj = {};
        responeObj[assetType.replace('-', '_') + '_inspections'] = asset_inspections;
        return successResponse(res, responeObj);
    },

    updateAssetInspection: async(req, res) => {
        let inspectionId = req.param('id');
        let assetId = +req.param('assetId');
        let assetType = req.param('assetType');
        let assetData = ASSET_TYPE_DATA[assetType];
        let assetInspectionTableReader = assetData['inpsectionDBTableReader'];
        let assetInspectionTable = assetData['inpsectionDBTable'];
        let inspectionKey = assetData['inspectionTypeKey'];
        sails.log.info("Update Asset Inspection inspectionId: ", inspectionId, " assetType ", assetType, " assetId ", assetId, " assetInspectionTableReader " , assetInspectionTableReader, " assetInspectionTable ", assetInspectionTable);
        let payload = _pick((req.body || {}), [
            'fault',
        ]);
        let { validationError } = closeOutInspectionFault(payload);
        if (validationError) {
            return errorResponse(res, 'Invalid Request.', {validationError});
        }
        let where = {id: inspectionId};
        where[inspectionKey] = assetId;
        let asset_inspection = await sails.models[assetInspectionTableReader].findOne({
            where: where,
            select: ['fault_details']
        });
        let faultIndex = (asset_inspection.fault_details || []).findIndex(fault => fault.fault_id == payload.fault.fault_id);
        if (faultIndex > -1) {
            asset_inspection.fault_details[faultIndex] = payload.fault;
            let req = {
                "fault_details": asset_inspection.fault_details
            };
            asset_inspection = await sails.models[assetInspectionTable].updateOne({id: inspectionId}).set(req);
        }
        sails.log.info('Asset inspection has been updated');
        let responeObj = {};
        responeObj[assetType.replace('-', '_') + '_inspection'] = asset_inspection;
        return successResponse(res, responeObj);
    },

    downloadAssetWeeklyInspections: async(req, res) => {
        let assetId = +req.param('assetId');
        let assetType = req.param('assetType');
        if(assetType == 'equipment') {
            assetType = 'asset-equipment';
        }
        let format = req.body.format;
        let fileName = (req.body.file_name || '').toString().trim();
        let fromDayParam = (req.body.from_day || '').toString().trim();
        // remove fromDateParam(from_date) when mobile app gets updated to use the api request date format
        let fromDateParam = (req.body.from_date || '').toString().trim();
        let fromDay = fromDayParam ? moment(fromDayParam, dbDateFormat_YYYY_MM_DD).startOf('day').valueOf(): moment(fromDateParam, 'DD-MM-YYYY').startOf('day').valueOf();
        let toDay = moment(fromDay).add(6, 'days').endOf('day').valueOf();
        let assetData = ASSET_TYPE_DATA[assetType];
        let assetCategory = assetData.title;
        let assetInspectionTable = assetData['inpsectionDBTableReader'];
        let inspectionTypeKey = assetData['inspectionTypeKey'];
        let assetTable = assetData['databaseTableReader'];
        sails.log.info(`download weekly asset request for Asset type ${assetType}, id:`, assetId);
        let assetRecord =  await sails.models[assetTable].findOne({id: assetId})
            .populate('project_ref');
        // let project = await sails.models.project_reader.findOne({where:{id: projectId}, select: ['id', 'parent_company']});
        let companyId = assetRecord.project_ref.parent_company;
        sails.log.info(`Fetch Asset ${assetType} inspections for Asset ${assetType}, id:`, assetId, `Between days: From Day ${fromDay} and To Day ${toDay}.`);
        let where = {
            createdAt: {'>=': fromDay, '<=': toDay},
        };
        where[inspectionTypeKey] = assetId;
        let assetInspections =  await sails.models[assetInspectionTable].find({
            where,
        }).sort([
            { id: 'DESC' },
        ]);
        let checklistVersions = new Set();
        assetInspections.filter(i=> checklistVersions.add(i.checklist_version));
        let checklist_version = [...checklistVersions].join(', ');
        if (assetRecord && assetRecord.id) {
            let TYPE_OF_ASSET = [];
            let metaChecklistCategoriesData = [];
            let metaChecklistCategories = [];
            let checkListItems = {};
            let form_template = `pages/asset-equipment-inspections-form-page`;
            let tool = `asset-equipment`;
            if(assetType === ASSET_TYPES.AssetEquipment) {
                fileName = fileName ? fileName: 'Equipment-Weekly-Inspections-Report-' + moment().format('DD-MM-YYYY');
                TYPE_OF_ASSET = await getTypeOfAssetEquipment();
                let fileRefs = (assetRecord.examination_certificates || []).filter(function (ref) {
                    return ref != null;
                });
                if (fileRefs.length) {
                    let files = await sails.models.userfile.find({
                        where: {id: fileRefs},
                        select: ['name', 'file_url']
                    });
                    assetRecord.examination_certificates = files;
                } else {
                    assetRecord.examination_certificates = [];
                }
                let equipmentType = getEquipmentKey(assetRecord);
                for(let version of checklistVersions) {
                    let metaEquipmentSpecificChecklist = [];
                    if(version === 1) {
                        let metaInspectionChecklist = await sails.models.inndexsetting.findOne({ name: "equipment_asset_inspection_checklist" });
                        metaEquipmentSpecificChecklist = metaInspectionChecklist.value[equipmentType+'_checklist'];
                        for (let key in metaEquipmentSpecificChecklist) {
                            // fill meta checklist items with checklist version to be used in getAnswer fn.
                            metaEquipmentSpecificChecklist[key].checklist_version = version;
                        }
                    } else {
                        let filter = {key: equipmentType, company_ref: companyId, checklist_version: version};
                        let configs = await sails.models.assetinspectionchecklists_reader.findOne({ where: filter, select:['inspection_checklist_type', 'inspection_checklist']});
                        metaEquipmentSpecificChecklist = (configs && configs.inspection_checklist_type === 'detailed') ? configs.inspection_checklist : [];
                        metaEquipmentSpecificChecklist = metaEquipmentSpecificChecklist.map(m=> {
                            m.category='Inspection';
                            m.checklist_version = version;
                            return m;
                        });
                    }
                    // let metaChecklist = (metaEquipmentSpecificChecklist || []).reduce((obj, que) => {
                    //     obj[que.question_id] = que;
                    //     return obj;
                    // }, {});
                    let sequenceNum = 0;
                    let sequenceNumArr = {};
                    for (let item of (metaEquipmentSpecificChecklist || [])) {
                        if (item.category) {
                            if (!metaChecklistCategoriesData[item.category]) {
                                metaChecklistCategoriesData[item.category] = [];
                                sequenceNumArr[item.category] = sequenceNum += 1;
                            }
                            // metaChecklist[item.question_id].display_number = `${sequenceNumArr[item.category]}.${metaChecklistCategoriesData[item.category].length+1}`;
                            metaChecklistCategoriesData[item.category].push(item);
                            if (!metaChecklistCategories.includes(item.category)) {
                                metaChecklistCategories.push(item.category);
                            }
                        }
                    }
                }
            } else if(assetType === ASSET_TYPES.AssetVehicle) {
                form_template = `pages/asset-vehicle-inspections-form-page`;
                tool = `asset-vehicle`;
                fileName = fileName ? fileName: 'Vechile-Weekly-Inspections-Report-' + moment().format('DD-MM-YYYY');
                TYPE_OF_ASSET = await getTypeOfAssetVehicles();
                let fileRefs = [...(assetRecord.examination_certificates), ...(assetRecord.service_certificates), ...(assetRecord.mot_certificates)].filter(function (ref) {
                    return ref != null;
                });
                if (fileRefs.length) {
                    let files = await sails.models.userfile.find({
                        where: {id: fileRefs},
                        select: ['name', 'file_url']
                    });
                    assetRecord.examination_certificates = (files || []).filter(file => assetRecord.examination_certificates.includes(file.id));
                    assetRecord.service_certificates = (files || []).filter(file => assetRecord.service_certificates.includes(file.id));
                    assetRecord.mot_certificates = (files || []).filter(file => assetRecord.mot_certificates.includes(file.id));
                }
                let vehicleType = assetRecord.type_of_vehicle;
                for(let version of checklistVersions) {
                    let metaVehicleSpecificChecklist = [];
                    if(version === 1) {
                        let metaInspectionChecklist = await sails.models.inndexsetting.findOne({ name: "vehicle_asset_inspection_checklist" });
                        metaVehicleSpecificChecklist = metaInspectionChecklist.value[vehicleType+'_checklist'];
                        for (let key in metaVehicleSpecificChecklist) {
                            // fill meta checklist items with checklist version to be used in getAnswer fn.
                            metaVehicleSpecificChecklist[key].checklist_version = version;
                            checkListItems[key] = metaVehicleSpecificChecklist[key].title;
                        }
                    } else {
                        let filter = {key: vehicleType, company_ref: companyId, checklist_version: version};
                        let configs = await sails.models.assetinspectionchecklists_reader.findOne({ where: filter, select:['inspection_checklist_type', 'inspection_checklist']});
                        metaVehicleSpecificChecklist = (configs && configs.inspection_checklist_type === 'detailed') ? configs.inspection_checklist : [];
                        metaVehicleSpecificChecklist = metaVehicleSpecificChecklist.map(m=> {
                            m.category='Inspection';
                            m.checklist_version = version;
		                    return m;
                        });
                        // let metaChecklist = (metaVehicleSpecificChecklist || []).reduce((obj, que) => {
                        //     obj[que.question_id] = que;
                        //     return obj;
                        // }, {});
                        let sequenceNum = 0;
                        let sequenceNumArr = {};
                        for (let item of (metaVehicleSpecificChecklist || [])) {
                            if (item.category) {
                                if (!metaChecklistCategoriesData[item.category]) {
                                    metaChecklistCategoriesData[item.category] = [];
                                    sequenceNumArr[item.category] = sequenceNum += 1;
                                }
                            //    metaChecklist[item.question_id].display_number = `${sequenceNumArr[item.category]}.${metaChecklistCategoriesData[item.category].length+1}`;
                                metaChecklistCategoriesData[item.category].push(item);
                                if (!metaChecklistCategories.includes(item.category)) {
                                    metaChecklistCategories.push(item.category);
                                }
                            }
                        }
                    }
                }
            } else if(assetType === ASSET_TYPES.AssetTemporaryWork) {
                TYPE_OF_ASSET = await getTypeOfAssetTemporaryWork();
                form_template = `pages/asset-temporary-work-inspections-form-page`;
                tool = `asset-temporary-work`;
                fileName = fileName ? fileName: 'Temporary-Work-Weekly-Inspections-Report-' + moment().format('DD-MM-YYYY');

                let fileRefs = (assetRecord.examination_certificates || []).filter(function (ref) {
                    return ref != null;
                });
                if (fileRefs.length) {
                    let files = await sails.models.userfile.find({
                        where: {id: fileRefs},
                        select: ['name', 'file_url']
                    });
                    assetRecord.examination_certificates = (files || []).filter(file => assetRecord.examination_certificates.includes(file.id));
                }
                for(let version of checklistVersions) {
                    let metaTempWorkSpecificChecklist = [];
                    if(version === 1) {
                        let metaInspectionChecklist = await sails.models.inndexsetting.findOne({ name: "temporary_work_asset_inspection_checklist" });
                        metaTempWorkSpecificChecklist = metaInspectionChecklist.value[assetRecord['type_of_works']+'_checklist'];
                        for (let key in metaTempWorkSpecificChecklist) {
                            // fill meta checklist items with checklist version to be used in getAnswer fn.
                            metaTempWorkSpecificChecklist[key].checklist_version = version;
                        }
                    } else {
                        let filter = {key: assetRecord.type_of_works, company_ref: companyId, checklist_version: version};
                        let configs = await sails.models.assetinspectionchecklists_reader.findOne({ where: filter, select:['inspection_checklist_type', 'inspection_checklist']});
                        metaTempWorkSpecificChecklist = (configs && configs.inspection_checklist_type === 'detailed') ? configs.inspection_checklist : [];
                        metaTempWorkSpecificChecklist = metaTempWorkSpecificChecklist.map(m=> {
                            m.category='Inspection';
                            m.checklist_version = version;
                            return m;
                        });
                    }
                    // let metaChecklist = (metaTempWorkSpecificChecklist || []).reduce((obj, que) => {
                    //     obj[que.question_id] = que;
                    //     return obj;
                    // }, {});
                    let sequenceNum = 0;
                    let sequenceNumArr = {};
                    for (let item of (metaTempWorkSpecificChecklist || [])) {
                        if (item.category) {
                            if (!metaChecklistCategoriesData[item.category]) {
                                metaChecklistCategoriesData[item.category] = [];
                                sequenceNumArr[item.category] = sequenceNum += 1;
                            }
                            // metaChecklist[item.question_id].display_number = `${sequenceNumArr[item.category]}.${metaChecklistCategoriesData[item.category].length+1}`;
                            metaChecklistCategoriesData[item.category].push(item);
                            if (!metaChecklistCategories.includes(item.category)) {
                                metaChecklistCategories.push(item.category);
                            }
                        }
                    }
                }
            }
            let fileRefs = [];
            assetInspections.filter(a => fileRefs.push(...a.attachment_file_ref));
            let hasSupportingAttachments = false;
            if (fileRefs.length) {
                hasSupportingAttachments = true;
                let files = await sails.models.userfile_reader.find({
                    id: _uniq(fileRefs)
                });
                assetInspections = assetInspections.map(assetInspection => {
                    assetInspection.attachment_file_ref = (files || []).filter(file => assetInspection.attachment_file_ref.includes(file.id));
                    return assetInspection;
                });
            }

            assetInspections = await populateUserRefs(assetInspections, 'user_ref', []);
            let projectInfo = assetRecord.project_ref;
            let timezone = (projectInfo && projectInfo.custom_field && projectInfo.custom_field.timezone) || fall_back_timezone;
            sails.log.info("Project Timezone: ", timezone);
            let { inspections, days, hasFault, numberOfFaults, project_logo_file, companyName, generalChecklistCount} = await prepareWeeklyInspections({
                assetRecord: assetRecord,
                assetInspections: assetInspections,
                timezone
            });
            assetInspections = inspections;
            let daysWithDate = [
                moment(fromDay).format("D/MMM"),
                moment(fromDay).add(1, 'days').format("D/MMM"),
                moment(fromDay).add(2, 'days').format("D/MMM"),
                moment(fromDay).add(3, 'days').format("D/MMM"),
                moment(fromDay).add(4, 'days').format("D/MMM"),
                moment(fromDay).add(5, 'days').format("D/MMM"),
                moment(fromDay).add(6, 'days').format("D/MMM")
            ];
            let title = `${ucFirst(assetCategory)} Checklist`;
            let project_line = `${ projectInfo.name}`;
            let date_line = `Week Commencing: ${formatTimeInTimezone(fromDay, 'DD-MM-YYYY', timezone)}`;

            let html = await sails.renderView(form_template, {
                title,
                project: assetRecord.project_ref,
                assetCategory,
                assetRecord,
                assetInspections,
                generalChecklistCount,
                metaChecklistCategories,
                metaChecklistCategoriesData,
                hasSupportingAttachments,
                checkListItems,
                checklist_version,
                weekCommencingMs: fromDay,
                moment,
                daysWithDate,
                days,
                hasFault,
                dateFormat: 'DD/MM/YYYY',
                fullDateFormat: dateFormat_DD_MM_YYYY_HH_mm_ss,
                project_logo_file,
                layout: false,
                unix(n, format) {
                    return moment.unix(n).format(format);
                },
                pad(n) {
                    return (n < 10) ? ("0" + n) : n;
                },
                getUserFullName(user) {
                    return (user) ? getUserFullName(user) : '';
                },
                getAssetTypeTitle(assetTypeKey) {
                    let type = (TYPE_OF_ASSET || []).find(typeArr => typeArr.key === assetTypeKey);
                    return (type) ? type.value : '';
                },
                ucFirst(string) {
                    return ucFirst(string);
                },
                momentTz(n, format) {
                    return timezone ? momentTz(+n).tz(timezone).format(format) : (+n) ? moment(+n).format(format) : moment().format(format);
                },
                strToUpper(string) {
                    return (string || '').toUpperCase();
                },
                getAnswer(dayData, questionId, tempWorks= false, vechileData=false, version = '') {
                    let result = '';
                    if (dayData && (version && dayData.checklist_version == version) && dayData.equipment_specific_checklist && Array.isArray(dayData.equipment_specific_checklist) && questionId) {
                        result = (dayData.equipment_specific_checklist || []).find(item => item.question_id == questionId);
                    }
                    if(tempWorks && dayData && (version && dayData.checklist_version == version) && dayData.temporary_work_specific_checklist && Array.isArray(dayData.temporary_work_specific_checklist) && questionId) {
                        result = (dayData.temporary_work_specific_checklist || []).find(item => item.question_id == questionId);
                    }
                    if(vechileData && dayData && (version && dayData.checklist_version == version) && dayData.specific_checklist && Array.isArray(dayData.specific_checklist) && questionId) {
                        result = (dayData.specific_checklist || []).find(item => item.question_id == questionId);
                    }

                    return (result && result.question_id) ? result.answer : '';
                }
            });
            if (format === 'pdf') {
                return await downloadPdfViaGenerator({
                    req,
                    res,
                    html,
                    tool,
                    file_name: fileName,
                    heading_line: title,
                    project_line,
                    date_line,
                    logo_file: project_logo_file,
                    has_cover: true,
                    has_one_page: true
                });
            }
            return res.send(html);
        }
    },

    getAssetFromCodeString: async(req, res) => {
        let projectId = +req.param('projectId');
        let codeString = req.body.encoded_string;
        let codeArray = codeString.split('-');
        let searchById = (req.query.search_by_itemId || '').toString().trim() === 'true';
        let expand_inspections = (req.query.expand_inspections || '').toString().trim() === 'true';
        sails.log.info('Fetch asset details from code string: ', codeString, ' project: ', projectId, ' expand_expensions: ', expand_inspections, ' searchById: ', searchById);
        let assets = [];
        let respObj = {};
        let assetData;
        let assetsFound = false;
        let userEmployer = await getOwnerFilter(req.user, projectId, false);
        if(searchById) {
            let assetTypes = [ASSET_TYPES.AssetEquipment, ASSET_TYPES.AssetVehicle,  ASSET_TYPES.AssetTemporaryWork];
            for(let i=0; i<assetTypes.length; i++) {
                assetData = ASSET_TYPE_DATA[assetTypes[i]];
                let where =  {project_ref: projectId};
                where[assetData['itemKey']] = codeString;
                assets = await findAndPopulateAssets(assetData, where, userEmployer, expand_inspections, assetTypes[i]);
                if(assets.length) {
                    assetsFound = true;
                    respObj[assetTypes[i].replace('-', '_')] = assets;
                }
            }
            if(assetsFound) {
                return successResponse(res, respObj);
            }
        } else if(codeArray.length && codeArray.length === 3  && projectId === +codeArray[0]) {
            let assetInfo = codeArray[1];
            let id = codeArray[2];
            let assetType = codedAssetTypes[assetInfo];
            assetData = ASSET_TYPE_DATA[assetType];
            if(assetData) {
                let filter = {id: id, project_ref: projectId};
                assets = await findAndPopulateAssets(assetData, filter, userEmployer, expand_inspections, assetType);
                if(assets.length) {
                    respObj[assetType.replace('-', '_')] = assets;
                    return successResponse(res, respObj);
                }
            }
        }
        return errorResponse(res, undefined, {message: `Asset with identifier ${codeString} not found`});
    },

    getProjectAssetFaults: async (req, res) => {
        let id = +req.param('id');
        const assetType = req.param('assetType');

        sails.log.info(`[getProjectAssetFaults] Fetch open faults for id: ${id}, assetType: ${assetType}`);

        let faults = [];

        if (assetType === ASSET_TYPES.AssetEquipment) {
            faults = await getEquipmentFaults([id]);
        } else if (assetType === ASSET_TYPES.AssetTemporaryWork) {
            faults = await getTemporaryWorkFaults([id]);
        } else {
            faults = await getVehiclesFaults([id]); // For assetType = asset-vehicle
        }


        sails.log.info(`[getProjectAssetFaults] For id: ${id} found faults: ${faults.length}, assetType: ${assetType}`);

        if (faults && faults.length > 0) {
            faults = faults[0];
        } else {
            faults = {}
        }

        return successResponse(res, {faults});
    }

};
