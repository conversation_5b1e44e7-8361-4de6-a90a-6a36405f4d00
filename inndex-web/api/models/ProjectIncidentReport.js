const {
    DataProcessingService: { buildRecordRef, buildIncidentReportStatus },
} = require('./../services');
const dayjs = require('dayjs');

const recordRefGenerator = async (values, cb) => {
    let where = {project_ref: values.project_ref, finalised: true};
    if(!values.project_ref) {
        where = {company_ref: values.company_ref, finalised: true}
    }

    // get next value for record id of this project
    // else use baseValue + 1
    const baseValue = 0;
    let [incidentReport] = await sails.models.projectincidentreport.find({
        where: where,
        select: ['id', 'record_id'],
        limit: 1,
        sort: ['id DESC', 'record_id DESC']
    });
    let next_record_id =  parseInt((incidentReport && incidentReport.record_id) ? incidentReport.record_id : baseValue) + 1;
    sails.log.info('next_record_id will be', incidentReport, next_record_id);
    values.record_id = next_record_id;
    // Assigning finalized_at timestamp.
    values.finalised_at = dayjs().valueOf();
    cb();
};

module.exports = {
    tableName: 'project_incident_report',

    attributes: {
        record_id:{
            type: 'string',
            allowNull: true,
            columnType: 'bigint'
        },
        project_ref: {
            model: 'project'
        },
        user_ref: {
            model: 'user'
        },
        user_revision_ref: {
            model: 'userrevision'
        },
        finalised:{
            type: 'boolean',
            defaultsTo: true,
            allowNull: true,
        },
        location: {
            type: 'string',
            allowNull: true
        },
        incident_details: {
            type: 'string',
            allowNull: true
        },
        incident_date: {
            type: 'string',
            allowNull: true,
            columnType: 'bigint'
        },
        injury_details: {
            type: 'json',
            defaultsTo: [],
        },
        is_onsite_treatment:{
            type: 'boolean',
            defaultsTo: false,
            allowNull: true,
        },
        site_treatment: {
            type: 'json',
            defaultsTo: {},
        },
        attachment_file_ids: {
            type: 'json',
            defaultsTo: []
        },
        any_witnesses:{
            type: 'boolean',
            defaultsTo: false,
            allowNull: true,
        },
        witnesses: {
            type: 'json',
            defaultsTo: [],
        },
        company_ref: {
            model: 'createemployer'
        },
        project_company: {
            model: 'createemployer'
        },
        incident_type: {
            type: 'string',
            allowNull: true
        },
        incident_category: {
            type: 'string',
            allowNull: true
        },
        abuse_type: {
            type: 'string',
            allowNull: true
        },
        act_type: {
            type: 'string',
            allowNull: true
        }, 
        actual_outcome: {
            type: 'string',
            allowNull: true
        },
        is_chartered:{
            type: 'boolean',
            defaultsTo: false,
            allowNull: true,
        },
        potential_severity: {
            type: 'string',
            allowNull: true
        },
        actual_severity: {
            type: 'string',
            allowNull: true
        },
        person_affected: {
            type: 'json',
            defaultsTo: [],
        },
        vehicle_details: {
            type: 'json',
            defaultsTo: {},
        },
        is_thirdparty_vehicle: {
            type: 'boolean',
            defaultsTo: false,
            allowNull: true,
        },
        thirdparty_vehicle: {
            type: 'json',
            defaultsTo: [],
        },
        loc_env_details: {
            type: 'json',
            defaultsTo: {},
        },
        driver_details: {
            type: 'json',
            defaultsTo: {},
        },
        action_details: {
            type: 'string',
            allowNull: true
        },
        status: {
            type: 'number',
            allowNull: true,
            defaultsTo: 1,
        },
        closed_out_date: {
            type: 'string',
            allowNull: true,
            columnType: 'bigint'
        },
        closeout_comment: {
            type: 'string',
            allowNull: true
        },
        closeout_user_ref: {
            model: 'user'
        },
        closeout_sign: {
            type: 'string',
            allowNull: true
        },

        incident_events: {
            type: 'json',
            defaultsTo: [],
        },
        incident_harm: {
            type: 'json',
            defaultsTo: [],
        },
        investigation_findings: {
            type: 'json',
            defaultsTo: [],
        },
        similar_incidents: {
            type: 'json',
            defaultsTo: [],
        },
        immediate_causes: {
            type: 'json',
            defaultsTo: [],
        },
        underlying_causes: {
            type: 'json',
            defaultsTo: [],
        },
        root_causes: {
            type: 'json',
            defaultsTo: [],
        },
        incident_actions: {
            type: 'json',
            defaultsTo: [],
        },
        incident_conclusions: {
            type: 'json',
            defaultsTo: [],
        },
        incident_recommendations: {
            type: 'json',
            defaultsTo: [],
        },
        review_photo_ids: {
            type: 'json',
            defaultsTo: [],
        },
        relevant_personnel_user_refs: {
            type: 'json',
            defaultsTo: [],
        },
        finalised_at: {
            type: 'string',
            allowNull: true,
            columnType: 'bigint'
        },
        is_closed:{
            type: 'boolean',
            defaultsTo: false,
            allowNull: true,
        },
        expected_root_cause:{
            type: 'string',
            allowNull: true,
        },
        lighting_condition: {
            type: 'string',
            allowNull: true
        },
        injury_caused_by: {
            type: 'string',
            allowNull: true
        },
        injury_caused_by_additional: {
            type: 'string',
            allowNull: true
        },
        weather_conditions: {
            type: 'string',
            allowNull: true
        },
    },

    //attributes methods
    customToJSON: function () {
        if (this.status == null) {
            this.status = 1;
        }
        this.status_message = buildIncidentReportStatus(this.status);
        this.record_ref = buildRecordRef(this);
        return _.omit(this, []);
    },

    beforeCreate: function (values, cb) {
        if(values.finalised) {
            recordRefGenerator(values, cb);
        } else {
            cb();
        }
    },

    beforeUpdate: function (values, cb) {
        cb();
    },

    afterUpdate: function(values, cb){
        values.status_message = buildIncidentReportStatus(values.status);
        values.record_ref = buildRecordRef(values);
        cb();
    },
};
