-- incident_report_meta_data_en_gb
DELETE FROM inndex_setting WHERE name = 'incident_report_meta_data_en_gb'; 

insert into inndex_setting (name, "value", "createdAt", "updatedAt") 
values ('incident_report_meta_data_en_gb', '{"incident_type":["Damage or Loss","Environmental","Health","Injury","Near Miss","Road Traffic","Service Strike","Unsafe Act/Occurrence","Violence or Abuse"],"Injury":{"incident_classification":["High Potential Learning Event (HPLE)","Classification Pending","Non-Treatment Injury (NTI)","First Aid Case (FAC)","Medical Treatment Case (MTC)","Modified Work Duty (MWD)","<=7 Day Lost Time Injury (LTI)",">7 Day Lost Time Injury (LTI)","RIDDOR Specified Injury","Fatal"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","<PERSON>","Stormy","Snowy/Icy","Indoors - N/A"],"incident_category":["Contact with moving machinery","Hit by moving vehicle","Hit by moving, flying, falling object","Hit something fixed or stationary","Injured while handling, lifting or carrying","Trapped by something collapsing","Slipped, tripped or fell","Fell from height","Exposed to fire","Exposed to explosion","Exposed to / contact with harmful substance","Exposed to / contact with electricity / electrical discharge","Drowned or asphyxiated","Injured by animal","Physically assaulted by person"],"gender":["Man","Woman","Non Binary","Other","Prefer not to say"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"injury_type":["Amputation","Arc Eye","Bruising","Burn / Scald","Chemical Burn","Crush","Cut / Incision","Dermatitis","Electrocution","Foreign Body in Eye","Fracture","Graze","Hand Arm Vibration","Laceration","Loss of Sight (Permanent)","Loss of Sight (Temporary)","Noise","Penetration / Puncture","Strain / Sprain"],"parts_of_body_effected":["Ankle","Arm - Lower","Arm - Upper","Back","Chest","Collar Bone","Ear","Elbow","Eye","Face","Finger","Foot","Groin","Hand","Head","Knee","Leg - Lower","Leg - Upper","Neck","Nose","Pelvis","Posterior","Shoulder","Stomach","Thumb","Toe","Wrist"],"injured_side":["Both","Left","Right","N/A"],"priority":["High","Medium","Low"]},"Health":{"environmental_incident":["Airborne emissions","Protected species","Waste","Breach of permit/license","Fly tip/litter","Land contamination","Oil/Chemical spill","Pests/Vermin (rats, mice etc.)","Water born emissions"],"type_of_health_incident":["Existing Medical Condition","Exposure to Asbestos","Expsoure to Biological Agent","Exposure to Dust","Exposure to Hazardous Substance","Exposure to Noise","Exposure to Radiation","Exposure to Vibration"],"gender":["Man","Woman","Non Binary","Other","Prefer not to say"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"]},"Unsafe Act/Occurrence":{"hazard_types":["Access & Egress","Buried/Above Ground/Overhead services","Confined Spaces","COSHH","Covid-19","Crime/Security","Ecology (Plants & Animals)","Electrical Safety","Excavation Safety","Fire Safety","Hot Works, Welding and Cutting Activities","Lifting Operations","Manual Handling","Moving Plant & Machinery","Nuisance (Noise/Lighting)","PPE","Personal Health","Process/Documentation","Protection/Possession Arrangements","Quality","Rail Vehicles","Railway Operations","Road Vehicles","Safe Systems Of Work (SSOW)","Site Welfare & Housekeeping","Slips, Trips & Falls","Spillage or Pollution (Oils/Dust ect)","Tools & Equipment","Traffic/Pedestrian Management","Unsafe Behaviour","Waste","Working at Height","Working on/over/near water"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"types":["Unsafe Act","Unsafe Condition"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"outcome":["No Damage","No Injury","Damage"]},"Near Miss":{"hazard_types":["Access & Egress","Buried/Above Ground/Overhead services","Confined Spaces","COSHH","Covid-19","Crime/Security","Ecology (Plants & Animals)","Electrical Safety","Excavation Safety","Fire Safety","Hot Works, Welding and Cutting Activities","Lifting Operations","Manual Handling","Moving Plant & Machinery","Nuisance (Noise/Lighting)","PPE","Personal Health","Process/Documentation","Protection/Possession Arrangements","Quality","Rail Vehicles","Railway Operations","Road Vehicles","Safe Systems Of Work (SSOW)","Site Welfare & Housekeeping","Slips, Trips & Falls","Spillage or Pollution (Oils/Dust ect)","Tools & Equipment","Traffic/Pedestrian Management","Unsafe Behaviour","Waste","Working at Height","Working on/over/near water"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"outcome":["No Damage","No Injury","Damage"]},"Road Traffic":{"environmental_incident":["Airborne emissions","Protected species","Waste","Breach of permit/license","Fly tip/litter","Land contamination","Oil/Chemical spill","Pests/Vermin (rats, mice etc.)","Water born emissions"],"gender":["Man","Woman","Non Binary","Other","Prefer not to say"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"location_type":["Carpark","Delivery/Collection Area","Own Site","Unknown","Road","Services","Site Compound","Yard"],"road_type":["Motorway","Dual Carriageway","Own Site","Minor Road","Road","Other","Unknown"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"weather_visibility":["Good","Satisfactory","Poor","Other","Unknown"],"vehicle_type":["Dozer","Dumper","Excavator","Forklift","Lorry","Motorcycle","Multiple Occupancy Vehicle","Private Car","Roller","Scooter","Tractor Unit","Van","Other"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"third_party_category":["Person","Property","Vehicle"],"vehicle_status":["Owned","Hired","Leased"]},"Damage or Loss":{"lighting_conditions":["Daylight","Dark","Artificial Light"],"type_of_damage_loss":["Building (Third Party Owned)","Building (Company Owned)","Fixed Plant (Third Party Owned)","Fixed Plant (Company Owned)","Mobile Plant (Third Party Owned)","Mobile Plant (Company Owned)","Vehicle (Third Party Owned)","Vehicle (Company Owned)"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"]},"Environmental":{"environmental_incident":["Airborne emissions","Protected species","Waste","Breach of permit/license","Fly tip/litter","Land contamination","Oil/Chemical spill","Pests/Vermin (rats, mice etc.)","Water Pollution"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"expected_root_cause":["Operator error","Permit not issued","Inadequate Risk Assessment","Inadequate supervision","Inadequate training/instruction","Lack of information","Lack of monitoring/inspection","Not in any way work related","N/Poor communication/consultation","Poor design of premises","Poor emergency arrangements","Poor job design","Poor motivation","Poor or wrong equipment","Poor personnel selection","Poor safe working procedures","Poorly allocated responsibilities","Poorly maintained equipment","Poorly maintained premises","Reckless behaviour","Safe working procedure not followed"]},"Violence or Abuse":{"gender":["Man","Woman","Non Binary","Other","Prefer not to say"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"abuse_type":["Physical Abuse","Verbal Abuse"]},"Service Strike":{"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"utility_strike":["Electric","Gas (Domestic)","Gas (Mains)","Media","Street lighting cable","Telecoms","Water"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"chartered":["Chartered","Unchartered"],"lighting_conditions":["Daylight","Dark","Artificial Light"]},"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"injury_type":["Amputation","Arc Eye","Bruising","Burn / Scald","Chemical Burn","Crush","Cut / Incision","Dermatitis","Electrocution","Foreign Body in Eye","Fracture","Graze","Hand Arm Vibration","Laceration","Loss of Sight (Permanent)","Loss of Sight (Temporary)","Noise","Penetration / Puncture","Strain / Sprain"],"parts_of_body_effected":["Ankle","Arm - Lower","Arm - Upper","Back","Chest","Collar Bone","Ear","Elbow","Eye","Face","Finger","Foot","Groin","Hand","Head","Knee","Leg - Lower","Leg - Upper","Neck","Nose","Pelvis","Posterior","Shoulder","Stomach","Thumb","Toe","Wrist"],"location_type":["Carpark","Delivery/Collection Area","Own Site","Unknown","Road","Services","Site Compound","Yard"],"vehicle_type":["Dozer","Dumper","Excavator","Forklift","Lorry","Motorcycle","Multiple Occupancy Vehicle","Private Car","Roller","Scooter","Tractor Unit","Van","Other"],"road_type":["Motorway","Dual Carriageway","Own Site","Minor Road","Road","Other","Unknown"]}'::json, 1750687415546, 1750687415546);

-- incident_report_meta_data_en_us
DELETE FROM inndex_setting WHERE name = 'incident_report_meta_data_en_us'; 

insert into inndex_setting (name, "value", "createdAt", "updatedAt")
values ('incident_report_meta_data_en_us', '{"incident_type":["Damage or Loss","Environmental","Health","Injury","Near Miss","Road Traffic","Service Strike","Unsafe Act/Occurrence","Violence or Abuse"],"Injury":{"incident_classification":["High Potential Learning Event (HPLE)","Classification Pending","Non-Treatment Injury (NTI)","First Aid Case (FAC)","Medical Treatment Case (MTC)","Modified Work Duty (MWD)","<=7 Day Lost Time Injury (LTI)",">7 Day Lost Time Injury (LTI)","RIDDOR Specified Injury","Fatal"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"incident_category":["Contact with moving machinery","Hit by moving vehicle","Hit by moving, flying, falling object","Hit something fixed or stationary","Injured while handling, lifting or carrying","Trapped by something collapsing","Slipped, tripped or fell","Fell from height","Exposed to fire","Exposed to explosion","Exposed to / contact with harmful substance","Exposed to / contact with electricity / electrical discharge","Drowned or asphyxiated","Injured by animal","Physically assaulted by person"],"gender":["Man","Woman","Non Binary","Other","Prefer not to say"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"injury_type":["Amputation","Arc Eye","Bruising","Burn / Scald","Chemical Burn","Crush","Cut / Incision","Dermatitis","Electrocution","Foreign Body in Eye","Fracture","Graze","Hand Arm Vibration","Laceration","Loss of Sight (Permanent)","Loss of Sight (Temporary)","Noise","Penetration / Puncture","Strain / Sprain"],"parts_of_body_effected":["Ankle","Arm - Lower","Arm - Upper","Back","Chest","Collar Bone","Ear","Elbow","Eye","Face","Finger","Foot","Groin","Hand","Head","Knee","Leg - Lower","Leg - Upper","Neck","Nose","Pelvis","Posterior","Shoulder","Stomach","Thumb","Toe","Wrist"],"injured_side":["Both","Left","Right","N/A"],"priority":["High","Medium","Low"]},"Health":{"environmental_incident":["Airborne emissions","Protected species","Waste","Breach of permit/license","Fly tip/litter","Land contamination","Oil/Chemical spill","Pests/Vermin (rats, mice etc.)","Water born emissions"],"type_of_health_incident":["Existing Medical Condition","Exposure to Asbestos","Expsoure to Biological Agent","Exposure to Dust","Exposure to Hazardous Substance","Exposure to Noise","Exposure to Radiation","Exposure to Vibration"],"gender":["Man","Woman","Non Binary","Other","Prefer not to say"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"]},"Unsafe Act/Occurrence":{"hazard_types":["Access & Egress","Buried/Above Ground/Overhead services","Confined Spaces","COSHH","Covid-19","Crime/Security","Ecology (Plants & Animals)","Electrical Safety","Excavation Safety","Fire Safety","Hot Works, Welding and Cutting Activities","Lifting Operations","Manual Handling","Moving Plant & Machinery","Nuisance (Noise/Lighting)","PPE","Personal Health","Process/Documentation","Protection/Possession Arrangements","Quality","Rail Vehicles","Railway Operations","Road Vehicles","Safe Systems Of Work (SSOW)","Site Welfare & Housekeeping","Slips, Trips & Falls","Spillage or Pollution (Oils/Dust ect)","Tools & Equipment","Traffic/Pedestrian Management","Unsafe Behaviour","Waste","Working at Height","Working on/over/near water"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"types":["Unsafe Act","Unsafe Condition"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"outcome":["No Damage","No Injury","Damage"]},"Near Miss":{"hazard_types":["Access & Egress","Buried/Above Ground/Overhead services","Confined Spaces","COSHH","Covid-19","Crime/Security","Ecology (Plants & Animals)","Electrical Safety","Excavation Safety","Fire Safety","Hot Works, Welding and Cutting Activities","Lifting Operations","Manual Handling","Moving Plant & Machinery","Nuisance (Noise/Lighting)","PPE","Personal Health","Process/Documentation","Protection/Possession Arrangements","Quality","Rail Vehicles","Railway Operations","Road Vehicles","Safe Systems Of Work (SSOW)","Site Welfare & Housekeeping","Slips, Trips & Falls","Spillage or Pollution (Oils/Dust ect)","Tools & Equipment","Traffic/Pedestrian Management","Unsafe Behaviour","Waste","Working at Height","Working on/over/near water"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"outcome":["No Damage","No Injury","Damage"]},"Road Traffic":{"environmental_incident":["Airborne emissions","Protected species","Waste","Breach of permit/license","Fly tip/litter","Land contamination","Oil/Chemical spill","Pests/Vermin (rats, mice etc.)","Water born emissions"],"gender":["Man","Woman","Non Binary","Other","Prefer not to say"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"location_type":["Carpark","Delivery/Collection Area","Own Site","Unknown","Road","Services","Site Compound","Yard"],"road_type":["Motorway","Dual Carriageway","Own Site","Minor Road","Road","Other","Unknown"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"weather_visibility":["Good","Satisfactory","Poor","Other","Unknown"],"vehicle_type":["Dozer","Dumper","Excavator","Forklift","Lorry","Motorcycle","Multiple Occupancy Vehicle","Private Car","Roller","Scooter","Tractor Unit","Van","Other"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"third_party_category":["Person","Property","Vehicle"],"vehicle_status":["Owned","Hired","Leased"]},"Damage or Loss":{"lighting_conditions":["Daylight","Dark","Artificial Light"],"type_of_damage_loss":["Building (Third Party Owned)","Building (Company Owned)","Fixed Plant (Third Party Owned)","Fixed Plant (Company Owned)","Mobile Plant (Third Party Owned)","Mobile Plant (Company Owned)","Vehicle (Third Party Owned)","Vehicle (Company Owned)"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"]},"Environmental":{"environmental_incident":["Airborne emissions","Protected species","Waste","Breach of permit/license","Fly tip/litter","Land contamination","Oil/Chemical spill","Pests/Vermin (rats, mice etc.)","Water Pollution"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"expected_root_cause":["Operator error","Permit not issued","Inadequate Risk Assessment","Inadequate supervision","Inadequate training/instruction","Lack of information","Lack of monitoring/inspection","Not in any way work related","N/Poor communication/consultation","Poor design of premises","Poor emergency arrangements","Poor job design","Poor motivation","Poor or wrong equipment","Poor personnel selection","Poor safe working procedures","Poorly allocated responsibilities","Poorly maintained equipment","Poorly maintained premises","Reckless behaviour","Safe working procedure not followed"]},"Violence or Abuse":{"gender":["Man","Woman","Non Binary","Other","Prefer not to say"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"abuse_type":["Physical Abuse","Verbal Abuse"]},"Service Strike":{"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"utility_strike":["Electric","Gas (Domestic)","Gas (Mains)","Media","Street lighting cable","Telecoms","Water"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"chartered":["Chartered","Unchartered"],"lighting_conditions":["Daylight","Dark","Artificial Light"]},"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"injury_type":["Amputation","Arc Eye","Bruising","Burn / Scald","Chemical Burn","Crush","Cut / Incision","Dermatitis","Electrocution","Foreign Body in Eye","Fracture","Graze","Hand Arm Vibration","Laceration","Loss of Sight (Permanent)","Loss of Sight (Temporary)","Noise","Penetration / Puncture","Strain / Sprain"],"parts_of_body_effected":["Ankle","Arm - Lower","Arm - Upper","Back","Chest","Collar Bone","Ear","Elbow","Eye","Face","Finger","Foot","Groin","Hand","Head","Knee","Leg - Lower","Leg - Upper","Neck","Nose","Pelvis","Posterior","Shoulder","Stomach","Thumb","Toe","Wrist"],"location_type":["Carpark","Delivery/Collection Area","Own Site","Unknown","Road","Services","Site Compound","Yard"],"vehicle_type":["Dozer","Dumper","Excavator","Forklift","Lorry","Motorcycle","Multiple Occupancy Vehicle","Private Car","Roller","Scooter","Tractor Unit","Van","Other"],"road_type":["Motorway","Dual Carriageway","Own Site","Minor Road","Road","Other","Unknown"]}'::json, 1750687415546, 1750687415546);

-- incident_report_meta_data_de_de
DELETE FROM inndex_setting WHERE name = 'incident_report_meta_data_de_de'; 

insert into inndex_setting (name, "value", "createdAt", "updatedAt")
values ('incident_report_meta_data_de_de', '{"incident_type":["Damage or Loss","Environmental","Health","Injury","Near Miss","Road Traffic","Service Strike","Unsafe Act/Occurrence","Violence or Abuse"],"Injury":{"incident_classification":["High Potential Learning Event (HPLE)","Classification Pending","Non-Treatment Injury (NTI)","First Aid Case (FAC)","Medical Treatment Case (MTC)","Modified Work Duty (MWD)","<=7 Day Lost Time Injury (LTI)",">7 Day Lost Time Injury (LTI)","RIDDOR Specified Injury","Fatal"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"incident_category":["Contact with moving machinery","Hit by moving vehicle","Hit by moving, flying, falling object","Hit something fixed or stationary","Injured while handling, lifting or carrying","Trapped by something collapsing","Slipped, tripped or fell","Fell from height","Exposed to fire","Exposed to explosion","Exposed to / contact with harmful substance","Exposed to / contact with electricity / electrical discharge","Drowned or asphyxiated","Injured by animal","Physically assaulted by person"],"gender":["Man","Woman","Non Binary","Other","Prefer not to say"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"injury_type":["Amputation","Arc Eye","Bruising","Burn / Scald","Chemical Burn","Crush","Cut / Incision","Dermatitis","Electrocution","Foreign Body in Eye","Fracture","Graze","Hand Arm Vibration","Laceration","Loss of Sight (Permanent)","Loss of Sight (Temporary)","Noise","Penetration / Puncture","Strain / Sprain"],"parts_of_body_effected":["Ankle","Arm - Lower","Arm - Upper","Back","Chest","Collar Bone","Ear","Elbow","Eye","Face","Finger","Foot","Groin","Hand","Head","Knee","Leg - Lower","Leg - Upper","Neck","Nose","Pelvis","Posterior","Shoulder","Stomach","Thumb","Toe","Wrist"],"injured_side":["Both","Left","Right","N/A"],"priority":["High","Medium","Low"]},"Health":{"environmental_incident":["Airborne emissions","Protected species","Waste","Breach of permit/license","Fly tip/litter","Land contamination","Oil/Chemical spill","Pests/Vermin (rats, mice etc.)","Water born emissions"],"type_of_health_incident":["Existing Medical Condition","Exposure to Asbestos","Expsoure to Biological Agent","Exposure to Dust","Exposure to Hazardous Substance","Exposure to Noise","Exposure to Radiation","Exposure to Vibration"],"gender":["Man","Woman","Non Binary","Other","Prefer not to say"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"]},"Unsafe Act/Occurrence":{"hazard_types":["Access & Egress","Buried/Above Ground/Overhead services","Confined Spaces","COSHH","Covid-19","Crime/Security","Ecology (Plants & Animals)","Electrical Safety","Excavation Safety","Fire Safety","Hot Works, Welding and Cutting Activities","Lifting Operations","Manual Handling","Moving Plant & Machinery","Nuisance (Noise/Lighting)","PPE","Personal Health","Process/Documentation","Protection/Possession Arrangements","Quality","Rail Vehicles","Railway Operations","Road Vehicles","Safe Systems Of Work (SSOW)","Site Welfare & Housekeeping","Slips, Trips & Falls","Spillage or Pollution (Oils/Dust ect)","Tools & Equipment","Traffic/Pedestrian Management","Unsafe Behaviour","Waste","Working at Height","Working on/over/near water"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"types":["Unsafe Act","Unsafe Condition"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"outcome":["No Damage","No Injury","Damage"]},"Near Miss":{"hazard_types":["Access & Egress","Buried/Above Ground/Overhead services","Confined Spaces","COSHH","Covid-19","Crime/Security","Ecology (Plants & Animals)","Electrical Safety","Excavation Safety","Fire Safety","Hot Works, Welding and Cutting Activities","Lifting Operations","Manual Handling","Moving Plant & Machinery","Nuisance (Noise/Lighting)","PPE","Personal Health","Process/Documentation","Protection/Possession Arrangements","Quality","Rail Vehicles","Railway Operations","Road Vehicles","Safe Systems Of Work (SSOW)","Site Welfare & Housekeeping","Slips, Trips & Falls","Spillage or Pollution (Oils/Dust ect)","Tools & Equipment","Traffic/Pedestrian Management","Unsafe Behaviour","Waste","Working at Height","Working on/over/near water"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"outcome":["No Damage","No Injury","Damage"]},"Road Traffic":{"environmental_incident":["Airborne emissions","Protected species","Waste","Breach of permit/license","Fly tip/litter","Land contamination","Oil/Chemical spill","Pests/Vermin (rats, mice etc.)","Water born emissions"],"gender":["Man","Woman","Non Binary","Other","Prefer not to say"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"location_type":["Carpark","Delivery/Collection Area","Own Site","Unknown","Road","Services","Site Compound","Yard"],"road_type":["Motorway","Dual Carriageway","Own Site","Minor Road","Road","Other","Unknown"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"weather_visibility":["Good","Satisfactory","Poor","Other","Unknown"],"vehicle_type":["Dozer","Dumper","Excavator","Forklift","Lorry","Motorcycle","Multiple Occupancy Vehicle","Private Car","Roller","Scooter","Tractor Unit","Van","Other"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"third_party_category":["Person","Property","Vehicle"],"vehicle_status":["Owned","Hired","Leased"]},"Damage or Loss":{"lighting_conditions":["Daylight","Dark","Artificial Light"],"type_of_damage_loss":["Building (Third Party Owned)","Building (Company Owned)","Fixed Plant (Third Party Owned)","Fixed Plant (Company Owned)","Mobile Plant (Third Party Owned)","Mobile Plant (Company Owned)","Vehicle (Third Party Owned)","Vehicle (Company Owned)"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"]},"Environmental":{"environmental_incident":["Airborne emissions","Protected species","Waste","Breach of permit/license","Fly tip/litter","Land contamination","Oil/Chemical spill","Pests/Vermin (rats, mice etc.)","Water Pollution"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"expected_root_cause":["Operator error","Permit not issued","Inadequate Risk Assessment","Inadequate supervision","Inadequate training/instruction","Lack of information","Lack of monitoring/inspection","Not in any way work related","N/Poor communication/consultation","Poor design of premises","Poor emergency arrangements","Poor job design","Poor motivation","Poor or wrong equipment","Poor personnel selection","Poor safe working procedures","Poorly allocated responsibilities","Poorly maintained equipment","Poorly maintained premises","Reckless behaviour","Safe working procedure not followed"]},"Violence or Abuse":{"gender":["Man","Woman","Non Binary","Other","Prefer not to say"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"lighting_conditions":["Daylight","Dark","Artificial Light"],"abuse_type":["Physical Abuse","Verbal Abuse"]},"Service Strike":{"weather_conditions":["Cloudy","Clear","Foggy","Rainy","Sunny","Stormy","Snowy/Icy","Indoors - N/A"],"utility_strike":["Electric","Gas (Domestic)","Gas (Mains)","Media","Street lighting cable","Telecoms","Water"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"chartered":["Chartered","Unchartered"],"lighting_conditions":["Daylight","Dark","Artificial Light"]},"person_type":["Employee","Contractor","Member of the public","Delivery Driver","Visitor","Work Experience"],"severity":["Critical (Major)","High Potential","Serious (High)","Moderate","Minor"],"injury_type":["Amputation","Arc Eye","Bruising","Burn / Scald","Chemical Burn","Crush","Cut / Incision","Dermatitis","Electrocution","Foreign Body in Eye","Fracture","Graze","Hand Arm Vibration","Laceration","Loss of Sight (Permanent)","Loss of Sight (Temporary)","Noise","Penetration / Puncture","Strain / Sprain"],"parts_of_body_effected":["Ankle","Arm - Lower","Arm - Upper","Back","Chest","Collar Bone","Ear","Elbow","Eye","Face","Finger","Foot","Groin","Hand","Head","Knee","Leg - Lower","Leg - Upper","Neck","Nose","Pelvis","Posterior","Shoulder","Stomach","Thumb","Toe","Wrist"],"location_type":["Carpark","Delivery/Collection Area","Own Site","Unknown","Road","Services","Site Compound","Yard"],"vehicle_type":["Dozer","Dumper","Excavator","Forklift","Lorry","Motorcycle","Multiple Occupancy Vehicle","Private Car","Roller","Scooter","Tractor Unit","Van","Other"],"road_type":["Motorway","Dual Carriageway","Own Site","Minor Road","Road","Other","Unknown"]}'::json, 1750687415546, 1750687415546);



ALTER TABLE project_incident_report ADD COLUMN act_type text default null;
ALTER TABLE project_incident_report ADD COLUMN actual_outcome text default null;

