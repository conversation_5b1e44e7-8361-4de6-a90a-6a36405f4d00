const SUPER_ADMIN_PREFIX = `super-admin/`;
const ReportController = `${SUPER_ADMIN_PREFIX}ReportController`;
const ManageUserController = `${SUPER_ADMIN_PREFIX}ManageUserController`;
const ManageProjectController = `${SUPER_ADMIN_PREFIX}ManageProjectController`;
const ManageCompanyController = `${SUPER_ADMIN_PREFIX}ManageCompanyController`;
const SystemController = `${SUPER_ADMIN_PREFIX}SystemController`;
const PowerBiController = `PowerBiController`;
const CompanySSOConfigurationController = `CompanySSOConfigurationController`;

// Declaring it as fn, so that it doesn't affect rest of sails.config loading.

module.exports = {

    getSuperAdminPolicies: () => {

        return {
            [ReportController]: {
                exportCompanyProjectStats: ['isAuth', 'isSuperAdminAuth', 'v2/isValidDownloadRequest'],
                exportBiometricSettings: ['isAuth', 'isSuperAdminAuth', 'v2/isValidDownloadRequest'],
                getAllOptimaServerStatus: ['isAuth', 'isSuperAdminAuth'],
            },
            [SystemController]: {
                getSysEventsList: ['isAuth', 'isSuperAdminAuth'],
            },
            [ManageProjectController]: {
                copyInductionQuestionBetween: ['isAuth', 'isSuperAdminAuth'],
            },
            [ManageUserController]: {
                getUserById: ['isAuth', 'isSuperAdminAuth'],
                updateUserInfo: ['isAuth', 'isSuperAdminAuth'],
                getUserInductionsAndDocuments: ['isAuth', 'isSuperAdminAuth'],
                associateDocWithInductions: ['isAuth', 'isSuperAdminAuth'],
                associateFileAsProfilePic: ['isAuth', 'isSuperAdminAuth'],
                updateInductionUserProfilePic: ['isAuth', 'isSuperAdminAuth'],
                deleteInductionById: ['isAuth', 'isSuperAdminAuth'],
                wipeAllBadgesFromInductions: ['isAuth', 'isSuperAdminAuth'],
                syncBlackListedBadgesToOptima: ['isAuth', 'isSuperAdminAuth', 'projectHasOptimaSetting'],
                wipeAllFacesFromInductions: ['isAuth', 'isSuperAdminAuth'],
                autoEnrolInductionsIntoFR: ['isAuth', 'isSuperAdminAuth', 'projectHasOptimaSetting'],
                removeMailSuppressionStatus: ['isAuth', 'isSuperAdminAuth'],
            },
            [PowerBiController]: {
                getSuperAdminToolReport: ['isAuth', 'isSuperAdminAuth'],
            },
            [ManageCompanyController]: {
                validateCSCSKey: ['isAuth', 'isSuperAdminAuth'],
            },
            [CompanySSOConfigurationController]: {
                getCompanySSOConfigurations: ['isAuth', 'isSuperAdminAuth'],
                addCompanySSOConfiguration: ['isAuth', 'isSuperAdminAuth'],
                updateCompanySSOConfiguration: ['isAuth', 'isSuperAdminAuth'],
                deleteCompanySSOConfiguration: ['isAuth', 'isSuperAdminAuth'],
                getAvailableProviderKeys: ['isAuth', 'isSuperAdminAuth'],
            }
        };
    },

    getSuperAdminRoutes: () => {

        return {
            'post /api/admin/:employerId/projects/stats': `${ReportController}.exportCompanyProjectStats`,
            'post /api/admin/biometric-settings/export/all': `${ReportController}.exportBiometricSettings`,
            'post /api/admin/optima/all/status': `${ReportController}.getAllOptimaServerStatus`,
            'get /api/admin/system/events/list': `${SystemController}.getSysEventsList`,
            'get /api/admin/user/:userId/profile': `${ManageUserController}.getUserById`,
            'post /api/admin/user/:userId/profile/update': `${ManageUserController}.updateUserInfo`,
            'get /api/admin/user/:userId/inductions': `${ManageUserController}.getUserInductionsAndDocuments`,
            'post /api/admin/user/:userId/inductions/docs/update': `${ManageUserController}.associateDocWithInductions`,
            'post /api/admin/user/:userId/profile-pic/update': `${ManageUserController}.associateFileAsProfilePic`,
            'post /api/admin/user/:userId/induction/:inductionId/delete': `${ManageUserController}.deleteInductionById`,
            'post /api/admin/user/:userId/induction/profile-pic/update': `${ManageUserController}.updateInductionUserProfilePic`,
            'post /api/admin/:projectId/inductions/wipe-badges': `${ManageUserController}.wipeAllBadgesFromInductions`,
            'post /api/admin/:projectId/inductions/sync/blocked-badges': `${ManageUserController}.syncBlackListedBadgesToOptima`,
            'post /api/admin/:projectId/inductions/wipe-faces': `${ManageUserController}.wipeAllFacesFromInductions`,
            'post /api/admin/:projectId/auto-enrol-all-faces': `${ManageUserController}.autoEnrolInductionsIntoFR`,
            'post /api/mail-suppression/check-and-remove': `${ManageUserController}.removeMailSuppressionStatus`,
            'post /api/admin/project/induction-questions/copy': `${ManageProjectController}.copyInductionQuestionBetween`,
            'get /api/admin/tool-report/:toolName': `${PowerBiController}.getSuperAdminToolReport`,
            'post /api/admin/company/:companyId/cscs/validate': `${ManageCompanyController}.validateCSCSKey`,
        };
    },
};
