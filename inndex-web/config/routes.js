/**
 * Route Mappings
 * (sails.config.routes)
 *
 * Your routes tell <PERSON><PERSON> what to do each time it receives a request.
 *
 * For more information on configuring custom routes, check out:
 * https://sailsjs.com/anatomy/config/routes-js
 */

module.exports.routes = {


    //  ╦ ╦╔═╗╔╗ ╔═╗╔═╗╔═╗╔═╗╔═╗
    //  ║║║║╣ ╠╩╗╠═╝╠═╣║ ╦║╣ ╚═╗
    //  ╚╩╝╚═╝╚═╝╩  ╩ ╩╚═╝╚═╝╚═╝

    /***************************************************************************
     *                                                                          *
     * Make the view located at `views/homepage.ejs` your home page.            *
     *                                                                          *
     * (Alternatively, remove this and add an `index.html` file in your         *
     * `assets` directory)                                                      *
     *                                                                          *
     ***************************************************************************/

    'get /api/health-check/:all?': 'AuthController.healthCheck',
    'get /api/status': 'UserController.status',
    'post /api/user/register': 'UserController.signUp',
    'post /api/user/login': 'UserController.login',
    'post /api/user/forgot-password': 'AuthController.forgotPassword',
    'post /api/user/reset-password': 'AuthController.resetPassword',
    'get /api/user/allusers': 'UserController.getAllUsers',
    'get /api/v2/user/list': 'UserController.getAllUsersV2',
    'post /api/user/allow-for-admin-access': 'UserController.updateAdminAccess',
    'post /api/user/register-device': 'AuthController.registerDevice',
    'post /api/user/logout': 'AuthController.logout',
    'post /api/user/refresh-token': 'AuthController.refresh',
    'post /api/user/sso-check': `AuthController.ssoCheck`,

    'post /api/user/change-password': 'AuthController.changePassword',
    'post /api/user/change-email': 'AuthController.changeEmail',

    'get /api/auth/user/mfa/generate-secret': 'MultiFactorAuthController.generateMfaSecret',
    'post /api/auth/user/mfa/activate-device': 'MultiFactorAuthController.activateMfaDevice',

    'get /api/user/token': 'UserController.token',
    'post /api/authorize-project': 'AnonymousUserController.authorizeInTimeUser',
    'get /live': 'AnonymousUserController.authorizeRaspberryDeviceLogin',
    'get /api/inn-time/project': 'AnonymousUserController.getProjectInfoByToken',
    'get /api/inn-time/refresh/token-info': 'AnonymousUserController.reFetchAnonymousTokenInfo',
    'post /api/inn-time/refresh-token': 'AnonymousUserController.refresh',
    'post /api/auth/verify-email-token/:user_id/:token': 'AuthController.verifyEmailToken',
    'get /api/user': 'UserController.profile',
    'post /api/user/update': 'UserController.updateUser',
    'post /api/shadow-user/create': 'UserController.createShadowUser',
    'post /api/shadow-user/:shadowUserId/save-details': 'UserController.saveShadowUserProfileDetails',
    'get /api/user/contact-detail': 'UserController.getUserContactDetail',
    'get /api/company/:companyId/user/contact-detail/:userId': 'UserController.getCompanyUserContactDetail',
    'post /api/user/contact-detail': 'UserController.saveContactDetail',
    'post /api/user/contact-detail/:contactDetailId': 'UserController.saveContactDetail',
    'delete /api/employer/:companyId/delete-user/:companyUserId': 'UserController.deleteEmployerUser',
    'post /api/user/users-detail': 'UserController.getUsersById',
    'post /api/user/document-sign/add': 'UserController.addDocumentForSign',
    'post /api/user/document-sign/submit/:id': 'UserController.submitDocumentOfSign',
    'get /api/user/document-sign/get/:employeeId/:documentId?': 'UserController.getDocumentOfSign',
    'post /api/user/document-sign/resend/:id': 'UserController.resendSignDocument',

    // 'post /api/project/:projectId/facial-recognition/create-collection': 'FacialRecognitionController.createCollection',
    'post /api/admin/project/:projectId/facial-recognition/delete-face': 'FacialRecognitionController.deleteFaceEnrolment',
    // 'post /api/inn-time/facial-recognition/delete-face': 'FacialRecognitionController.deleteFaceEnrolmentInnTime',
    // 'post /api/project/:projectId/facial-recognition/search': 'FacialRecognitionController.searchFacesByImage',
    'post /api/sa/project/:projectId/induction/:inductionRequestId/facial-recognition/check-duplicate': 'InductionController.checkDuplicateFaceIntoFR',
    'post /api/sa/project/:projectId/induction/:inductionRequestId/facial-recognition/index-face': 'InductionController.enrolInductionIntoFR',
    'post /api/inn-time/facial-recognition/search': 'FacialRecognitionController.searchFacesByImageInnTime',

    'get /azure/authorize': 'SsoIntegrationController.azureAuthorizeRedirect',
    'post /azure/auth/callback': 'SsoIntegrationController.azureAuthCallback',
    'get /azure/auth/result': 'SsoIntegrationController.authCallbackResult',

    'get /okta/authorize': 'SsoIntegrationController.oktaAuthorizeRedirect',
    'post /okta/auth/callback': 'SsoIntegrationController.oktaAuthCallback',
    'get /okta/auth/result': 'SsoIntegrationController.authCallbackResult',

    'get /one/authorize': 'SsoIntegrationController.authorizeRedirectOne',
    'post /one/auth/callback': 'SsoIntegrationController.authCallbackOne',
    'get /one/auth/result': 'SsoIntegrationController.authCallbackResult',

    'get /procore/authorize/:projectId/:userId': 'ProCoreIntegrationController.authorizeRedirect',
    'get /procore/auth/callback': 'ProCoreIntegrationController.authCallback',
    'get /procore/auth/result': 'ProCoreIntegrationController.authCallbackResult',
    'get /api/procore/:projectId/companies-list': 'ProCoreIntegrationController.getUserCompaniesList',
    'get /api/procore/:projectId/company/:proCoreCompanyId/project-list': 'ProCoreIntegrationController.getCompanyProjectsList',
    'post /api/procore/:projectId/link-project': 'ProCoreIntegrationController.saveProcoreProjectReference',
    'post /api/procore/:projectId/export-time-entries': 'ProCoreIntegrationController.createUsersTimecardEntries',
    'get /api/procore/:projectId/permission-templates': 'ProCoreIntegrationController.getProjectPermissionTemplates',

    'post /api/admin/company/:companyId/a-site/validate-credentials': 'ASiteIntegrationController.validateLogin',
    'get /api/company/:companyId/project/:projectId/a-site/check-config-status': 'ASiteIntegrationController.checkIfAsiteConfigured',
    'get /api/company/:companyId/project/:projectId/a-site/workspaces': 'ASiteIntegrationController.getWorkspaceList',
    'get /api/company/:companyId/project/:projectId/a-site/workspace/:workspaceId/folders': 'ASiteIntegrationController.getWorkspaceFoldersList',

    'get /api/company/:companyId/project/:projectId/admins': 'UserRoleController.getCompanyProjectAdmins',
    'post /api/company/:companyId/project/:projectId/admin': 'UserRoleController.saveCompanyProjectAdminPermission',
    'post /api/company/:companyId/project/:projectId/admin/update': 'UserRoleController.updateCompanyProjectAdminPermission',
    'post /api/company/:companyId/project/:projectId/admin/remove/:userId': 'UserRoleController.removeUserCompanyProjectAccess',

    'get /api/project/:projectId/admins': { controller: 'UserRole', action: 'getProjectAdmins', tools: ['*'], multiple: true, data_type_key: '' },
    'get /api/company/:companyId/admins': 'UserRoleController.getCompanyAdmins',

    'get /api/:projectId/bio-metric/setting': 'TouchByteController.getBioMetricSetting',
    'get /api/:projectId/touch-byte/user/:userId': 'TouchByteController.getTBUserById',
    'get /api/:projectId/touch-byte/events': 'TouchByteController.getProjectTbEvents',
    'post /api/:projectId/touch-byte/user/:userId/allow-site': 'TouchByteController.grantUserSiteAccess',
    'post /api/:projectId/touch-byte/user/:userId/revoke-site': 'TouchByteController.revokeUserSiteAccess',

    'post /api/visitor/store': 'VisitorController.storeVisitorInfo',
    'post /api/project/:projectId/visitor/store': 'VisitorController.storeVisitorInfoAlias',
    'post /api/visitor/:visitorId/update': 'VisitorController.updateVisitorById',
    'post /api/sa/project/:projectId/visitor/:visitorId/update': 'VisitorController.updateVisitorByIdAlias',
    'get /api/visitor/time-logs/:projectId/:employerId?': 'VisitorController.getVisitorTimeLogs',
    'get /api/visitor/time-logs/by-day/:projectId/:employerId?': 'VisitorController.getVisitorTimeLogsByDay',
    'get /api/inn-time/visitors/on-site/:nowMs': 'VisitorController.getOnSiteVisitorOfProject',
    'get /api/inn-time/visitors/on-site/count/:nowMs': 'VisitorController.getOnSiteVisitorCountOfProject',
    'get /api/project/:projectId/visitors/on-site/:nowMs': { controller: 'Visitor', action: 'getOnSiteVisitorOfProjectAlias', tools: ['*'], multiple: true, data_type_key: '' },
    'get /api/project/:projectId/visitors': 'VisitorController.getVisitorsOfProject',
    'get /api/project/:projectId/visitors/events': { controller: 'Visitor', action: 'getProjectVisitorEvents', tools: ['*'], multiple: true, data_type_key: '' },
    'get /api/project/:projectId/visitor/:visitorId/detail/:eventLogId': 'VisitorController.getVisitorEventDetail',
    'get /api/inn-time/visitors/list': 'VisitorController.getPreviousVisitorsOfProject',

    'post /api/inn-time/vehicle/reg-look-up': 'VehicleController.fetchVehicleRegDetails',
    'post /api/inn-time/vehicle/create': 'VehicleController.storeVehicleInfoInnTime',
    'get /api/inn-time/vehicle/search': 'VehicleController.searchVehicleInnTime',
    'post /api/inn-time/vehicle/time-log/add': 'VehicleController.storeVehicleLogInnTime',
    'get /api/inn-time/vehicles/on-site/:nowMs': 'VehicleController.getOnSiteVehicleInnTime',
    'get /api/vehicles/time-logs/by-day/:projectId': 'VehicleController.getVehicleTimeLogsByDay',
    'post /api/vehicles/time-logs/detail/:projectId': 'VehicleController.getVehicleLogsByID',
    'post /api/project/:projectId/vehicles/time-logs/download': 'VehicleController.downloadVehicleDailyLogsExport',
    'post /api/project/:projectId/vehicle/:vehicleId/log/update': 'VehicleController.updateVehicleAndLogs',
    'post /api/postcode/distance-matrix': 'VehicleController.getDistanceBwPostcodes',

    'get /api/data/health-assessment-questions': 'UserController.getUserHealthQuestions',
    'get /api/user/health-assessment': 'UserController.getUserHealthAssessment',
    'post /api/user/health-assessment': 'UserController.saveUserHealthAssessment',

    'get /api/data/medical-assessment-questions': 'UserController.getUserMedicalQuestions',
    'get /api/user/medical-assessment': 'UserController.getUserMedicalAssessment',
    'post /api/user/medical-assessment': 'UserController.saveUserMedicalAssessment',
    'get /api/company/:companyId/user/health-assessment/:userId': 'UserController.getCompanyUserHealthAssessment',
    'get /api/company/:companyId/user/medical-assessment/:userId': 'UserController.getCompanyUserMedicalAssessment',

    'get /api/user/employment-detail': 'UserController.getUserEmploymentDetail',
    'post /api/user/employment-detail': 'UserController.saveUserEmploymentDetail',
    'post /api/v2/user/employment/update': 'UserController.saveUsrEmplAndPsnlDetail',
    'get /api/user/:userId/time-details/:companyId': 'UserController.getUserTimeDetails',
    'post /api/user/store-setting': 'UserController.storeUserSetting',
    'get /api/user/get-settings/:name?': 'UserController.getUserSettings',
    'post /api/user/user-employment/update/:userId/:empDetailId': 'UserController.updateUserEmpDetail',
    'get /api/user/:userId/employment-detail': 'UserController.getUserEmploymentDetailById',

    'post /api/upload-file': 'FileController.uploadFile',
    'post /api/project/upload-file': 'FileController.storeInnTimeUploadFile',
    'post /api/delete-file': 'FileController.deleteFile',
    'post /api/project/delete-file': 'FileController.deleteInnTimeFile',
    'post /api/download-file': 'FileController.downloadFile',
    'post /api/merge-pdfs': 'FileController.mergePDFs',
    'post /api/convert/worddoc-to-pdf': 'FileController.convertWordDocsToPdf',

    'get /api/user/documents': 'UserDocumentController.getMyDocuments',
    'get /api/company/:companyId/user/documents/:userId': 'UserDocumentController.getCompanyUserDocuments',
    'post /api/user/documents': 'UserDocumentController.saveDocument',
    'post /api/user/documents/:userDocId/update': 'UserDocumentController.updateDocument',
    'post /api/user/documents/delete': 'UserDocumentController.deleteDocument',
    'get /api/user/additional-documents/:docOwnerId': 'UserDocumentController.getAdditionalOwnedDocuments',
    'get /api/user/citb/search-doc': 'UserDocumentController.searchCitbAchievement',
    'post /api/company/:companyId/cscs/documents-info': 'UserDocumentController.getCardInfoFromCSCS',
    'post /api/company/:companyId/cscs/auto-verify': 'UserDocumentController.autoVerifyUserDocuments',
    'post /api/company/:companyId/rtw/check-status': 'UserDocumentController.getPPACIdStatus',

    'get /api/projects': 'ProjectController.getUserProjects',
    'post /api/projects/add': 'ProjectController.createProject',
    'get /api/company/:companyId/projects/status': 'ProjectController.checkCompanyProjectStatus',
    'get /api/projects/:projectId': 'ProjectController.getProject',
    'get /api/sa/projects/:projectId': { controller: 'Project', action: 'siteAdmin_getProject', tools: ['*'], multiple: true, data_type_key: '' },
    'get /api/user/projects/:projectId': { controller: 'Project', action: 'getProjectIfInducted', policy_checks: ['isInductedUserOf'] },
    'post /api/projects/:projectId/update': 'ProjectController.updateProject',
    'post /api/projects/:projectId/update-ltv-footer': 'ProjectController.updateProjectLiveTvFooter',
    'post /api/projects/:projectId/updatestatus': 'ProjectController.projectstatusUpdate',
    'post /api/project/validate-postcode': 'ProjectController.validatePostCode',
    'post /api/inn-time/validate-postcode': 'ProjectController.innTimeValidatePostCode',
    'get /api/projects/:projectId/induction-requests': { controller: 'induction', action: 'getProjectInductionRequests', policy_checks: ['isMoreThanDeliveryManager'] },
    'get /api/ca/:employerId/projects/:projectId/induction-requests': 'InductionController.getProjectInductionRequestsAlias',
    'get /api/projects/:projectId/inductions': 'InductionController.getProjectInductions', // @deprecated: in favor of `getProjectInductedUsers`
    'get /api/v2/project/:projectId/inductions/all': { controller: 'induction', action: 'getProjectInductedUsers', tools: ['*'], multiple: true, data_type_key: '' },
    'get /api/v2/ca/:companyId/project/:projectId/inductions/all': `InductionController.getProjectInductedUsersCA`,
    'get /api/projects/:projectId/inducted/admins': { controller: 'induction', action: 'getProjectInductedAdmins', policy_checks: ['isInductedUserOf'] },
    'get /api/v2/project/:projectId/inductions/search-user': { controller: 'Induction', action: 'searchProjectInductedUsers', tools: ['*'], multiple: true, data_type_key: '' },
    'get /api/inn-time/inductions': 'InductionController.getProjectInductionsInnTime', // @deprecated: in favor of `getInductionsListInnTime`
    'get /api/inn-time/inductions/list': 'InductionController.getInductionsListInnTime',
    'get /api/inn-time/inductions/onsite/status/:nowMs': 'InductionController.getProjectInductionsWithOnSiteStatus',
    'get /api/company/:companyId/users/blacklisted': 'InductionController.getBlackListedUserByCompany',
    'post /api/sa/projects/:projectId/heat-map-data': { controller: 'Induction', action: 'getHeatMapDataSA', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/live-tv/projects/:projectId/heat-map-data': 'InductionController.getHeatMapDataLiveTv',
    'post /api/ca/:employerId/projects/:projectId/heat-map-data': 'InductionController.getHeatMapDataCA',
    'get /api/project/:projectId/setting/induction-booking': 'ProjectController.getProjectInductionBookingSetting',
    'post /api/project/:projectId/setting/induction-booking': 'ProjectController.saveProjectInductionBookingSetting',
    'get /api/project/:projectId/induction-booking/slots': 'ProjectController.getProjectInductionBookingSlots',
    'get /api/all/projects': 'ProjectController.getnewProject',
    'get /api/projects/:projectId/users/on-site/:nowMs': 'ProjectController.getOnSiteUsers',
    'get /api/projects/:projectId/users/on-site': 'ProjectController.getOnSiteUsersOfProject',
    'get /api/project/:projectId/on-site-operatives': { controller: 'Project', action: 'getOnSiteUsersGroupByCompany', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/project/:projectId/company-breakdown/download': 'ProjectController.downloadCompanyBreakdown',
    'get /api/inn-time/users/on-site/:nowMs': 'ProjectController.getOnSiteUsersInnTime',
    'get /api/inn-time/users/on-site/count/:nowMs': 'ProjectController.getOnSiteUsersCountInnTime',
    'get /api/projects/:projectId/entities/on-site/:nowMs/with-location': 'ProjectController.getOnSiteEntitiesOfProjectWithLocation',
    'post /api/projects/:projectId/roll-call/save': 'ProjectController.saveRollCall',
    'post /api/projects/:projectId/update-project-partially': 'ProjectController.updateProjectPartially',
    'post /api/project/:projectId/delete': 'ProjectController.deleteProject',
    'post /api/project/:projectId/gate-supervisors/update': 'ProjectController.addUpdateGateSupervisors',
    'get /api/project/:projectId/gate-supervisors': 'ProjectController.getGateSupervisors',
    'get /api/project/:projectId/weather-log': 'ProjectController.getProjectWeatherInfo',
    'post /api/project/:projectId/enable-smartsheet': 'ProjectController.enableSmartSheetForProjectId',
    'get /api/project/:projectId/clocked-in-users': 'ProjectController.getClockedInUsersOfProject',
    'post /api/project/:projectId/resource-plans': 'ProjectController.saveProjectResourcePlans',
    'post /api/project/:projectId/resource-plans/upload/csv': 'ProjectController.bulkUploadProjectResourcePlans',
    'post /api/project/:projectId/resource-plans/download': 'ProjectController.downloadResourcePlannerExport',
    'get /api/project/:projectId/planned-resources': 'ProjectController.getProjectPlannedResources',
    'post /api/project/:projectId/planned-resources/update/:recordId': 'ProjectController.updateProjectResourcePlanById',
    'post /api/project/:projectId/planned-resources/delete/:recordId': 'ProjectController.deleteProjectResourcePlanById',
    'get /api/project/:projectId/users/inducted-or-to-be': { controller: 'Project', action: 'getProjectInductedOrToBeUsers', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/project/:projectId/setting/save': 'ProjectController.saveProjectSetting',
    'post /api/project/:projectId/roll-call/download': 'ProjectController.downloadProjectRollCallReport',

    'get /api/form-templates': 'FormTemplateController.getFormTemplates',
    // public
    // 'get /form-template/:templateIdentifier/preview': 'FormTemplateController.previewTemplate',
    'post /api/optima/events-listener' : 'OptimaController.optimaEventListener',

    'post /api/employer/add': 'CompanyController.addEmployer',
    'post /api/inn-time/employer/add': 'CompanyController.addEmployerViaInnTime',
    'post /api/employer/:employerId/edit': 'CompanyController.editEmployer',
    'get /api/employer/list': 'CompanyController.getEmployer',
    'get /api/employer/:employerId/timezone': 'CompanyController.getCompanyTimezone',
    'get /api/v2/employer/list': 'CompanyController.companiesList',
    'get /api/v3/employer/list': 'CompanyController.companiesListV3',
    'post /api/v3/employer/searches-list': 'CompanyController.companiesSearchesList',
    'get /api/project/:projectId/supply-chain-companies/list': 'CompanyController.supplyChainCompaniesList',
    'get /api/v2/inn-time/employer/list': 'CompanyController.companiesListForInnTimeApp',
    'get /api/inn-time/employer/list': 'CompanyController.getEmployersListForInnTimeApp',
    'get /api/company/:employerId/projects-list': 'CompanyController.getCompanyProjects',
    'post /api/admin/permit-template/copy': 'PermitController.copyPermitTemplates',
    'get /api/user/inductions/:extended?': 'InductionController.getMyInductions',
    'post /api/user/induction-request': 'InductionController.createInduction',
    'post /api/user/induction-request/:inductionRequestId/:origin?': 'InductionController.updateInductionRequest',
    'post /api/project/:projectId/induction-request/:inductionRequestId/blacklist': 'InductionController.updateInductionToBlacklistUser',
    'get /api/user/induction-request/:inductionRequestId': 'InductionController.getInductionRequest',
    'post /api/user/induction-request/update/documents': 'InductionController.updateInductionWithNewCompetencies',
    'get /api/project/:projectId/induction/:inductionRequestId/medications': 'InductionController.getInductionMedications',
    'post /api/project/:projectId/induction/:inductionRequestId/medications': 'InductionController.updateInductionMedications',
    'post /api/project/:projectId/induction/:inductionRequestId/:createdAt/update/user-employment': 'InductionController.updateUserEmployment',
    'get /api/user/search-projects': 'InductionController.searchProjectsForInduction',
    'get /api/inndex/search-vehicle/:registrationNumber': 'InductionController.fetchVehicleRegDetails',
    'post /api/:projectId/induction-request/:inductionRequestId/over-ride/travel-time': 'InductionController.overrideInductionTravelTime',
    'post /api/user/induction-request/:inductionRequestId/over-ride/travel-time': 'InductionController.updateTravelTimeDetails',
    'get /api/project/:projectId/induction-requests/:status_code/user-employers': 'InductionController.getProjectInductionsUsersEmployer',
    'post /api/project/:projectId/induction-requests/download': 'InductionController.downloadInductionRecords',
    'post /api/company/:companyId/project/:projectId/qr-poster/download': 'InductionController.downloadQRPoster',
    'post /api/project/:projectId/induction-requests/exportXLSX': 'InductionController.downloadInductionRecordsXLSX',
    'post /api/company/:employerId/inductions/exportXLSX': 'InductionController.downloadCompanyInductionsXLSX',
    'get /api/project/search-all-projects': 'ProjectController.searchAllProject',
    'post /api/employer/delete': 'CompanyController.deleteEmployer',
    'get /api/employer/:companyId/delete-user/:uacId': 'CompanyController.deleteCompanyUser',
    'get /api/user/companies': 'CompanyController.getUserCompanies',
    'get /api/company/entities/count/:employerId': 'CompanyController.getEntitiesCountByCompany',
    'post /api/company/merge': 'CompanyController.mergeCompany',
    'post /api/company/company-division/save': 'CompanyController.createOrUpdateCompanyDivisions',
    'post /api/company/:companyId/import-supply-chain-companies': 'CompanyController.importSupplyChainCompanies',
    'get /api/company/company-division/:divisionId/delete': 'CompanyController.deleteCompanyDivision',
    ...(require('./api-routes/super-admin').getSuperAdminRoutes()),
    ...(require('./api-routes/site-admin').getSiteAdminRoutes()),

    'post /api/job-role/add': 'JobRoleController.addJobRole',
    'get /api/job-role/list': 'JobRoleController.getJobRoles',
    'get /api/inn-time/job-role/list': 'JobRoleController.getJobRolesForInnTimeApp',
    'post /api/job-role/delete': 'JobRoleController.deleteJobRole',

    'get /api/user-notifications/getall': 'NotificationController.getUserNotifications',
    'get /api/user-notifications/count/:all': 'NotificationController.getUserNotificationsCount',
    'post /api/user-notifications/update': 'NotificationController.updateNotificationStatus',
    'get /api/user-notifications/:notificationId/delete': 'NotificationController.deleteNotificationById',
    'get /api/user-notifications/mark/seen': 'NotificationController.markAllNotificationsAsSeen',
    // 'get /payment': 'PaymentController.test',
    // 'post /payment/make': 'PaymentController.make',

    // public
    'get /api/induction-request/:inductionRequestId/:type/:createdAt': 'InductionController.viewOrDownloadInduction',

    'post /api/support-ticket': 'UserController.createSupportTicket',

    'get /api/fatigue-records/:projectId': { controller: 'ProjectFatigueViolations', action: 'getProjectViolations', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/fatigue-records/:projectId/download-report': 'ProjectFatigueViolationsController.downloadReport',
    'post /api/fatigue-records/:fatigueRecordId/update-partially': 'ProjectFatigueViolationsController.updatePartially',
    'post /api/project/:projectId/fatigue-records/:fatigueRecordId/update-partially': 'ProjectFatigueViolationsController.updatePartiallyV2',

    'get /api/:projectId/:callerType/total-time': 'OptimaController.getTotalSiteTimeReport',
    'post /api/project/:projectId/report/total-time': 'ReportController.exportTotalTimeReportOfProjectForAll',
    'post /api/project/:projectId/report/emissions-report': 'OptimaController.downloadProjectCarbonEmissionsReportXLSX',

    'post /api/user/:projectId/working-shift': 'OptimaController.createUserWorkingShift',
    'post /api/user/:projectId/working-shift/:configId/delete': 'OptimaController.removeUserShiftConfig',
    'post /api/user/working-shift/:projectId': 'OptimaController.getUserActiveWorkingShift',
    'post /api/biometric/test-connection/:projectId': 'OptimaController.testBiometricConnection',
    'get /api/optima/status/:projectId': 'OptimaController.checkOptimaStatus',
    'get /api/optima/all': 'OptimaController.getAllOptimaSetting',
    'post /api/optima/:projectId/add-badge/:inductionRequestId': 'OptimaController.createOptimaBadge',
    'post /api/sa/:projectId/induction/:inductionRequestId/almas/remote-enrol': 'OptimaController.remoteEnrolUserToOptima',
    'post /api/optima/re-generate-badge/:projectId': 'OptimaController.reGenerateBadges',
    'post /api/project/:projectId/optima/add-or-update': 'OptimaController.addUpdateOptimaSetting',
    'post /api/project/:projectId/optima/remove-setting': 'OptimaController.removeOptimaSetting',
    'get /api/geo-fence/:projectId/events': { controller: 'Optima', action: 'getProjectGeoFenceEvents', tools: ['*'], multiple: true, data_type_key: '' },
    'get /api/geo-fence/:projectId/declaration-events/:userId': 'OptimaController.getClockingDeclarationAnswers',
    'get /api/optima/:projectId/events': 'OptimaController.getProjectBadgeEvents',
    'post /api/optima/:projectId/events/download': 'OptimaController.downloadBadgeEvents',
    // 'get /api/optima/:projectId/members/events': 'OptimaController.getProjectMemberTimeData', // v1
    'get /api/optima/:projectId/members/events-by-days': { controller: 'Optima', action: 'getProjectMemberDataForDays', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/user-time-log/:projectId/update/:userId': 'OptimaController.updateUserDailyLog',
    'post /api/badge-event/:projectId/update/:badgeEventId': 'OptimaController.updateBadgeEventById',
    'post /api/geo-fence/:projectId/update/:userTimeLogId': 'OptimaController.updateGeoFenceEventById',
    'get /api/optima/:projectId/badgedetail': 'OptimaController.getOptimaBadgeDetail',
    'get /api/user/project/:projectId/event': 'OptimaController.getUserEventByDay',
    'get /api/inn-time/user/:userId/project/event': 'OptimaController.getInnTimeUserEventByDay',
    'post /api/optima/:projectId/import/events': 'OptimaController.importMissingOptimaEvents',
    'post /api/competency/add': 'ResourceController.addCompetency',
    'post /api/admin/competency/:competencyId/update': 'ResourceController.updateMetaCompetency',
    'get /api/competency/getlist': 'ResourceController.getCompetencies',
    'post /api/competency/:competencyId/delete': 'ResourceController.deleteCompetency',
    'get /api/typeofwork/add': 'ResourceController.addWork',
    'get /api/typeofwork/getlist': 'ResourceController.getTypeOfWorks',
    'post /api/typeofwork/:workId/delete': 'ResourceController.deleteWork',
    // 'post /api/user/invite-to-induction': 'InductionController.sendInviteToInductionAlert',
    'get /api/project/:projectId/user/:userId/received/briefing': 'InductionController.getReceivedBriefings',
    'post /api/user/invite-to-induct/:projectId': 'NotificationController.inviteToInduction',
    'post /api/report/induction-invites/:projectId': 'ReportController.downloadInductionInviteReport',
    'post /api/report/daily-declaration/:projectId/:userId': 'ReportController.exportDailyDeclarationReportOfUser',
    'post /api/indexsetting/upload_qrcode': 'ResourceController.uploadQRImage',
    'get /api/indexsetting/get_qrcode': 'ResourceController.getQRImage',
    'get /api/timezones/list': 'ResourceController.timezones',
    'get /api/mates-in-mind/list': 'ResourceController.getMatesInMind',
    'get /api/countries/all': 'ResourceController.getCountries',
    'post /api/inndex-setting/fetch/batch': 'ResourceController.getSettings',
    'post /api/inn-time/inndex-setting/fetch/batch': 'ResourceController.getSettingsInnTime',
    'post /api/inndex-setting/fetch': 'ResourceController.getSettingByName',
    'get /api/inndex-setting/fetch/force-update-config': 'ResourceController.getForceUpdateConfig',
    'post /api/inndex-setting/force-update-config/update': 'ResourceController.updateForceUpdateConfig',
    'post /api/inndex-setting/useful-info-files/update': 'ResourceController.updateUsefulInfoFilesData',
    'post /api/user/search-by-email': 'ProjectController.getUserInfo',
    'get /api/visitors/search-by-email': 'VisitorController.searchVisitorByEmail',
    'get /api/project/:projectId/visitors/search-by-email': 'VisitorController.searchVisitorByEmailAlias',
    'post /api/employer/:companyId/save-incident-action-categories': 'ResourceController.saveIncidentActionCategories',
    'post /api/admin/company/:companyId/company-setting': 'ResourceController.saveCompanySettingFromAdmin',
    'post /api/employer/:companyId/company-setting': 'ResourceController.saveCompanySetting',
    'post /api/company-setting/:companyId/fetch': 'ResourceController.getCompanySettingByName',
    'post /api/project/:projectId/settings/fetch': 'ResourceController.getProjectSettingsByName',
    'post /api/project/:projectId/setting/delete': 'ResourceController.deleteProjectSetting',
    'get /api/tools/get-list': 'ResourceController.getToolListData',

    'get /api/report/project/:projectId/kpi-dashboard': 'ReportController.getProjectDashboard',
    'get /api/project/:projectId/info': 'ReportController.getProjectInfo',
    'get /api/user/time-sheet/list': 'ReportController.getUserTimeSheet',
    'get /api/report/user/time-sheet': 'ReportController.sendUserTimeSheet',
    'post /api/report/company-project/time-sheet': 'ReportController.getCompanyProjectTimeSheetByWeek',
    'post /api/company/:companyId/report/company-project/time-sheet': 'ReportController.getCompanyProjectTimeSheetByWeekV2',
    'get /api/report/project/:projectId/users-postcode': 'ReportController.getProjectUsersPostcode',
    'get /api/report/projects/postcode': 'ReportController.getProjectPostcodes',
    'get /api/report/:projectId/project-induction-documents': 'ReportController.getInducteeTrainingsByProject',

    'post /api/user/deactive/:userId': 'UserController.deactiveUser',
    'post /api/user/delete/:userId': 'UserController.deleteUser',
    //'get /api/user/send-email': 'InductionController.sendEmail',
    'post /api/closecall/add': 'CloseCallController.createCloseCall',
    'post /api/closecall/:ccId/update': 'CloseCallController.updateCloseCall',
    'post /api/project/:projectId/closecall/:ccId/update': 'CloseCallController.updateCloseCallV2',
    'get /api/closecall/:ccId': 'CloseCallController.getCloseCall',
    'get /api/project/:projectId/close-call/filters': { controller: 'closecall', action: 'getProjectBasedCloseCallUtils', tools: ['close_calls'], multiple: false, data_type_key: '' },
    'get /api/project-close-call/:projectId/:companyId?': { controller: 'closecall', action: 'getprojectclosecalls', tools: ['close_calls'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/close-call/:ccId': { controller: 'closecall', action: 'getprojectclosecall', tools: ['close_calls'], multiple: false, data_type_key: '' },
    'get /api/lightingconditions/getlist': 'ResourceController.getLightingConditions',
    'get /closecall/download/:ccId/:timestamp': 'CloseCallController.downloadCloseCall',
    'get /api/user-close-call/:userId/:projectId?': 'CloseCallController.getUserCloseCalls',
    'get /api/v2/user/:projectId/close-call/list': { controller: 'closecall', action: 'getuserclosecalllist', tools: ['close_calls'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/close-call/:type/dashboard': { controller: 'closecall', action: 'projectclosecalldashboard', tools: ['close_calls'], multiple: false, data_type_key: '' },
    'post /api/company/:companyId/close-call/:type/dashboard': 'CloseCallController.companyCloseCallDashboard',
    'post /api/closecall/download/:ccId': 'CloseCallController.downloadCloseCallV1',
    'post /api/project/:projectId/close-call/report/download': 'CloseCallController.downloadCloseCallReport',

    'post /api/project-gate-booking/add': 'ProjectGateBookingController.createBooking',
    'post /api/project/:projectId/gate-booking/add': 'ProjectGateBookingController.createBookingV2',
    'post /api/project-gate-booking/:bookingId/update': 'ProjectGateBookingController.updateBooking',
    'post /api/project/:projectId/gate-booking/:bookingId/update': 'ProjectGateBookingController.updateBookingV2',
    'get /api/projects/:projectId/gate-by-project': 'ProjectController.getGateByProject',
    'get /api/project/:projectId/project-gates': { controller: 'Project', action: 'getGateByProjectV1', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/project-gate-booking/:bookingId/delete': 'ProjectGateBookingController.deleteBooking',
    'post /api/project/:projectId/gate-booking/:bookingId/delete': 'ProjectGateBookingController.deleteBookingV2',
    'post /api/project-gate/:projectId/update/:gateId': 'ProjectGateBookingController.updateProjectGate',
    'post /api/project-gate/:projectId/bookings/export': 'ProjectGateBookingController.exportBookings',
    'post /api/project-gate-booking/status/:bookingId': 'ProjectGateBookingController.updateBookingStatus',
    'post /api/project/:projectId/gate-booking/status/:bookingId': 'ProjectGateBookingController.updateBookingStatusV2',
    'post /api/project-gate-booking/:projectId/status-update': 'ProjectGateBookingController.updateMultipleBookingsStatus',
    'post /api/project-gate-booking/:projectId/bookings/pdfexport': 'ProjectGateBookingController.exportBookingsReportPDF',

    'post /api/take5/add': 'Take5sController.createTake5',
    'get /api/take5/:t5Id': 'Take5sController.getTake5',
    'get /api/conversationcategory/getlist': 'ResourceController.getConversationCategory',
    //'get /take5/download/:t5Id/:timestamp': 'Take5sController.downloadTake5',
    'get /api/user-take5s/:userId/:projectId?': { controller: 'take5s', action: 'getusertake5s', tools: ['take_5s'], multiple: false, data_type_key: '' },
    'get /take5-view/:t5Id/:type/:timestamp/:isSiteAdmin': 'Take5sController.downloadTake5', // @todo: Satyam Hardia: deprecated as of Jun 15, 2021.
    'post /api/v2/take5-view/:projectId/xlsx': 'Take5sController.downloadTake5XlSX',
    'post /api/v2/take5-view/:t5Id': 'Take5sController.downloadTake5V2',
    'post /api/toolbox-talk/add': 'ToolboxTalkController.createToolboxTalk',
    'get /api/project-toolbox-talks/:projectId?': { controller: 'toolboxtalk', action: 'getprojecttoolboxtalks', tools: ['toolbox_talks'], multiple: false, data_type_key: '' },
    'get /api/company-toolbox-talks/:employerId?': 'ToolboxTalkController.getCompanyToolboxTalks',
    'get /api/company-available-toolbox-talks/:employerId': 'ToolboxTalkController.getCompanyTBTForInvite',
    'post /api/toolbox-talk/:tbTalkId/update': 'ToolboxTalkController.updateToolboxTalk',
    'get /api/toolbox-talk/search-talks': 'ToolboxTalkController.searchTalks',
    'get /toolbox-talk-view/:tbTalkId/:type/:timestamp/:projectId?': 'ToolboxTalkController.downloadToolboxTalk', // @todo: Satyam Hardia: deprecated as of Jun 16, 2021.
    'post /api/v2/toolbox-talk-view/:tbTalkId': 'ToolboxTalkController.downloadToolboxTalkV2',
    'get /api/user/:employerId/users-by-employer': 'UserController.getUsersByEmployer',
    'get /api/employer/:employerId/users-by-employer': 'UserController.fetchUsersByEmployer',
    'post /api/user/:employerId/users-documents-by-employer': 'UserController.getUserDocumentsByEmployer',
    'get /api/employer/:employerId': 'CompanyController.getEmployerById',
    'post /api/employer/:employerId/save-company-user': 'CompanyController.saveNewCompanyUsers',
    ...(require('./api-routes/company-admin').getCaRoutes()),
    'get /api/employer/:employerId/projects': 'ProjectController.getCompanyProjects',
    'get /api/employer/:employerId/projects/:userId': 'ProjectController.getCompanyProjectsForUser',

    'post /api/toolbox-talk/:projectId/invite': { controller: 'toolboxtalk', action: 'invitetotoolboxtalk', tools: ['toolbox_talks'], multiple: false, data_type_key: '' },
    'post /api/toolbox-talk/:companyId/invite-for-company': 'ToolboxTalkController.inviteToToolboxTalkForCompanyTBTs',
    'post /api/toolbox-talk/:projectId/id/:id/download': { controller: 'toolboxtalk', action: 'downloadtoolboxtalkxlsx', tools: ['toolbox_talks'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/toolbox-talk/register/download': 'ToolboxTalkController.downloadToolboxTalkRegister',

    'post /api/user-time-log/add': 'OptimaController.storeUserTimeLog',
    'post /api/project/inn-time-log/add': 'OptimaController.storeInnTimeAppLog',
    'post /api/project/inn-time-log/bulk-add': 'OptimaController.storeBulkInnTimeAppLogs',
    'post /api/project/:projectId/inn-time-log/add-manual-entries' : 'OptimaController.addManualGeoFenceEntries',
    'post /api/project/inn-time-log/add/visitor': 'OptimaController.storeVisitorInnTimeAppLog',
    'get /api/geo-fence/:employerId/time-logs/:projectId': 'OptimaController.getProjectUsersGeoFenceTimeLogs', // v2
    'get /api/v3/project/:projectId/members/:employerId?': { controller: 'Optima', action: 'getProjectMembers', tools: ['*'], multiple: true, data_type_key: '' },
    'get /api/project/:projectId/on-site/members/locations': 'OptimaController.getProjectOnSiteUsersAccessPoints',
    'post /api/v3/project/:projectId/member/all-events/:employerId?': { controller: 'Optima', action: 'getProjectMemberDailyEvents', tools: ['*'], multiple: true, data_type_key: '' },
    'get /api/geo-fence/:projectId/time-log/:userId/:interactionType/:eventLogId?': 'OptimaController.getProjectUserGeoFenceLog',
    'post /api/project/:projectId/time-log/:eventLogId/delete': 'OptimaController.deleteProjectTimeLogById',
    'get /api/company/:employerId/time-log/:projectId': 'OptimaController.getCompanyProjectTimeLogs',
    'post /api/favorite/add/project': 'UserFavoriteController.addProjectToFavorite',
    'post /api/favorite/delete/project': 'UserFavoriteController.deleteFavoriteProject',
    'get /api/favorite/projects': 'UserFavoriteController.getUserFavoriteProjects',
    'get /api/favorite/search/project': 'UserFavoriteController.searchProject',
    'get /api/user/project/:userId?': 'UserController.getUserCurrentProject',
    // 'get /api/employer/logo/:employerName': 'CompanyController.getEmployerLogoByName',
    // 'get /api/employer/logo-list': 'CompanyController.getEmployersLogo',

    // 'post /api/authorize-livetv': 'AnonymousUserController.authorizeInTimeUser',
    'get /api/report/project/:projectId/livetv-kpi-dashboard': 'ReportController.getProjectDashboardLiveTv',
    'get /api/optima/:projectId/:callerType/livetv-total-time': 'OptimaController.getLiveTvTotalSiteTimeReport',
    'get /api/optima/:projectId/get-optima': 'OptimaController.getOptimaSettingByProjectId',

    'post /api/progress-photos/add': 'ProgressPhotosController.addProgressPhotos',
    'get /api/progress-photos/:ppId': 'ProgressPhotosController.getProgressPhotos',
    'post /api/progress-photos/:ppId/update': 'ProgressPhotosController.updateProgressPhotos',
    'get /api/project-progress-photos/:projectId/:companyId?': { controller: 'progressphotos', action: 'getallprogressphotos', tools: ['progress_photos'], multiple: false, data_type_key: '' },
    'get /api/v3/project/:projectId/progress-photos/list': { controller: 'progressphotos', action: 'getprogressphotoslist', tools: ['progress_photos'], multiple: false, data_type_key: '' },
    'get /api/user-progress-photos/:userId/:projectId?': { controller: 'progressphotos', action: 'getuserprogressphotos', tools: ['progress_photos'], multiple: false, data_type_key: '' },
    'get /progress-photos/download/:type?/:ppImgid?/:ppId/:timestamp/:isSiteAdmin': 'ProgressPhotosController.downloadProgressPhotos', // @todo: Satyam Hardia: deprecated as of Jun 15, 2021.
    'post /api/v2/progress-photos/download/:ppId': 'ProgressPhotosController.downloadProgressPhotosV2',
    'post /api/download-progress-photos-report': 'ProgressPhotosController.downloadProgressPhotosReport',
    'post /api/project-progress-photos/:projectId/between-duration': { controller: 'progressphotos', action: 'fetchprogressphotosreportrecords', tools: ['progress_photos'], multiple: false, data_type_key: '' },
    'post /api/project-progress-photos-xlsx': 'ProgressPhotosController.downloadProgressPhotosXLSX',
    'post /api/progress-photos/:projectId/album/add': 'ProgressPhotosController.createProgressPhotosAlbum',
    'post /api/progress-photos/:projectId/album/:id/update': 'ProgressPhotosController.updateProgressPhotosAlbum',
    'post /api/progress-photos/:projectId/album/:id/delete': 'ProgressPhotosController.deleteProgressPhotosAlbum',
    'get /api/progress-photos/:projectId/album/list': { controller: 'progressphotos', action: 'getallprogressphotosalbums', tools: ['progress_photos'], multiple: false, data_type_key: '' },
    'post /api/progress-photos/:projectId/album/user-preference': 'ProgressPhotosController.savePhotosAlbumsUserPreference',


    'post /api/delivery-notes/add': 'DeliveryNotesController.addDeliveryNote',
    'post /api/delivery-notes/:id/update': 'DeliveryNotesController.updateDeliveryNote',
    'post /api/project/:projectId/delivery-notes/:id/update': 'DeliveryNotesController.updateDeliveryNoteV2',
    'get /api/delivery-notes/:ppId': 'DeliveryNotesController.getDeliveryNote',
    'get /api/project/:projectId/delivery-notes/:ppId': 'DeliveryNotesController.getDeliveryNoteV2',
    'get /api/project-delivery-notes/:projectId/:companyId?': { controller: 'deliverynotes', action: 'getprojdeliverynotes', tools: ['delivery_notes', 'collection_note'], multiple: true, data_type_key: 'data_type' },
    'get /api/user-delivery-notes/:userId/:projectId?': { controller: 'deliverynotes', action: 'getuserdeliverynotes', tools: ['delivery_notes', 'collection_note'], multiple: true, data_type_key: 'data_type' },
    'post /api/v2/delivery-notes/download/:ppId': 'DeliveryNotesController.downloadDeliveryNote',
    'post /api/project/:projectId/delivery-notes/download/:ppId': 'DeliveryNotesController.downloadDeliveryNoteV2',
    'post /api/download-delivery-notes-report-xlsx': 'DeliveryNotesController.downloadDeliveryNotesReportXLSX',
    'post /api/project/:projectId/download-delivery-notes-report-xlsx': 'DeliveryNotesController.downloadDeliveryNotesReportXLSXV2',
    'post /api/download-delivery-notes-report': 'DeliveryNotesController.downloadDeliveryNotesReport',
    'post /api/project/:projectId/download-delivery-notes-report': 'DeliveryNotesController.downloadDeliveryNotesReportV2',
    'post /api/project-delivery-notes/:projectId/between-duration': 'DeliveryNotesController.fetchDeliveryNotesReportRecords',

    'get /api/validate-user-project/:userId': 'UserController.validateUserProjectRelation',
    'post /api/daily-activities/create': 'ProjectDailyActivitiesController.createDailyActivity',
    'post /api/project/:projectId/daily-activities/create': 'ProjectDailyActivitiesController.createDailyActivityV2',
    'post /api/daily-activities/:id/update': 'ProjectDailyActivitiesController.updateDailyActivity',
    'post /api/project/:projectId/daily-activities/:id/update': 'ProjectDailyActivitiesController.updateDailyActivityV2',
    'get  /api/daily-activities/:projectId/:userId?': { controller: 'projectdailyactivities', action: 'getdailyactivities', tools: ['daily_activities'], multiple: false, data_type_key: '' },
    'get  /api/daily-activity/:id': 'ProjectDailyActivitiesController.getDailyActivity',
    'get /api/project/:projectId/daily-activity/:id': 'ProjectDailyActivitiesController.getDailyActivityV2',
    'post /api/daily-activity/delete/:id': 'ProjectDailyActivitiesController.deleteDailyActivity',
    'post /api/project/:projectId/daily-activity/delete/:id': 'ProjectDailyActivitiesController.deleteDailyActivityV2',
    'get  /daily-activity/download/:id/:updatedAt': 'ProjectDailyActivitiesController.downloadDailyActivitiesReport',
    'post /api/daily-activity/download/:id': { controller: 'ProjectDailyActivities', action: 'downloadDailyActivity', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/project/:projectId/daily-activity/download/:id': { controller: 'ProjectDailyActivities', action: 'downloadDailyActivityV2', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/daily-activity/download-delivery-notes-report-xlsx': 'ProjectDailyActivitiesController.downloadDailyActivitiesReportXLSX',
    'post /api/project/:projectId/daily-activity/download-delivery-notes-report-xlsx': 'ProjectDailyActivitiesController.downloadDailyActivitiesReportXLSXV2',
    'post /api/project/:projectId/daily-activity/workforce-hours/download': { controller: 'projectdailyactivities', action: 'downloadworkforcehoursreport', tools: ['daily_activities'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/daily-activity/hours-comparison/download': { controller: 'projectdailyactivities', action: 'downloadhourscomparisonreport', tools: ['daily_activities'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/daily-activities/download': { controller: 'projectdailyactivities', action: 'downloaddailyactivities', tools: ['daily_activities'], multiple: false, data_type_key: '' },
    'post /api/daily-activity/:id/share': 'ProjectDailyActivitiesController.shareDailyActivityReport',
    'post /api/project/:projectId/daily-activity/activity-breakdown/download': 'ProjectDailyActivitiesController.downloadActivityHoursBreakdownReport',

    'get /api/optima/members-time-logs/:projectId/:companyId?': { controller: 'Optima', action: 'getMembersTimeLogs', tools: ['*'], multiple: true, data_type_key: '' },
    'get  /employee-detail/download/:employeeId/:companyId/:updatedAt?': 'UserController.downloadCompanyEmployeeInfo',
    'post /api/employee-detail/download/:employeeId': 'UserController.downloadCompanyEmployeeInfoV1',
    'get /api/optima-access-group-and-reader/:projectId': 'OptimaController.getOptimaAccessGroupAndReader',
    'post /api/optima/:projectId/unlock-all-readers': 'OptimaController.unlockAllReaders',
    'post /api/toggle-optima-access/:badge_number/:projectId': 'OptimaController.toggleOptimaAccess',
    'post /api/optima-fingerprint-enrolment/:projectId': { controller: 'optima', action: 'optimafingerprintenrolment', tools: ['e_and_a_control'], multiple: false, data_type_key: '' },
    'get /api/retrieve-badge-information/:badge_number/:projectId': 'OptimaController.retrieveBadgeInformation',
    'post /api/:projectId/optima/delete-enrolment/:badge_number/:finger_index': { controller: 'optima', action: 'deletefingerprintenrolment', tools: ['e_and_a_control'], multiple: false, data_type_key: '' },
    'get /api/:projectId/optima/enrollment-status/:badge_number': 'OptimaController.getEnrollmentStatus',
    'post /api/delete-badge-number/:badge_number/:projectId': 'OptimaController.deleteBadgeNumber',
    'post /api/powra/create': 'ProjectPowraController.createPowra',
    'post /api/project/:projectId/powra/create': 'ProjectPowraController.createPowraV2',
    'post /api/powra/:id/update': 'ProjectPowraController.updatePowra',
    'post /api/project/:projectId/powra/:id/update': 'ProjectPowraController.updatePowraV2',
    'get  /api/all-powra/:projectId/:userId?': { controller: 'projectpowra', action: 'getallpowra', tools: ['powra'], multiple: false, data_type_key: '' },
    'get  /api/powra/:id': 'ProjectPowraController.getPowra',
    'get /api/project/:projectId/powra/:id': 'ProjectPowraController.getPowraV2',
    'post /api/powra/delete/:id': 'ProjectPowraController.deletePowra',
    'post /api/project/:projectId/powra/delete/:id': 'ProjectPowraController.deletePowraV2',
    'get  /api/powra/download/:id/:type/:updatedAt?': 'ProjectPowraController.downloadPowraReport',
    'post /api/powra/:id/share': 'ProjectPowraController.sharePowraReport',

    'post /api/clerk-of-works/add': 'ClerkOfWorksController.createClerkOfWorks',
    'post /api/clerk-of-works/:cowId/update': 'ClerkOfWorksController.updateClerkOfWorks',
    'post /api/clerk-of-works-comment/add': 'ClerkOfWorksController.getAddComment',
    'get /api/clerk-of-works/:cowId': 'ClerkOfWorksController.getClerkOfWorks',
    'get /api/project-clerk-of-works/:projectId/:companyId?': { controller: 'clerkofworks', action: 'getprojectclerkofworks', tools: ['clerk_of_works'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/clerk-of-works/filters': { controller: 'clerkofworks', action: 'getProjectCowFilterData', tools: ['clerk_of_works'], multiple: false, data_type_key: '' },
    'get /api/v3/project/:projectId/clerk-of-works/list': { controller: 'clerkofworks', action: 'getprojectclerkofworkslist', tools: ['clerk_of_works'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/cow-report/download/:companyId?': { controller: 'clerkofworks', action: 'downloadclerkofworksreport', tools: ['clerk_of_works'], multiple: false, data_type_key: '' },
    'get /clerk-of-works/download/:cowId/:timestamp': 'ClerkOfWorksController.downloadClerkOfWorks', // @todo: Satyam Hardia: deprecated as of Jun 11, 2021.
    'post /api/v2/clerk-of-works/download/:cowId': 'ClerkOfWorksController.downloadCowV2',
    'get /api/user-clerk-of-works/:userId/:projectId?': 'ClerkOfWorksController.getUserClerkOfWorks', // @todo: Satyam Hardia: deprecated as of Jun 14, 2021.
    'get /api/v2/user-clerk-of-works/:projectId?': { controller: 'clerkofworks', action: 'getusercowsv2', tools: ['clerk_of_works'], multiple: false, data_type_key: '' },
    'post /api/clerk-of-works/delete': 'ClerkOfWorksController.deleteCow',
    'post /api/clerk-of-works/populate-tagged-data': 'ClerkOfWorksController.populateTaggedNameEmail',
    'get /api/clerk-of-works/get-site-level-drawing/:projectId/:level': { controller: 'clerkofworks', action: 'getlevelsitedrawing', tools: ['clerk_of_works'], multiple: false, data_type_key: '' },
    'post /api/clerk-of-works/drop-pin-on-drawing/:cowId': 'ClerkOfWorksController.savePinCoordinates',
    'post /api/project/:projectId/clerk-of-works/drop-pin-on-drawing/:cowId': 'ClerkOfWorksController.savePinCoordinatesV2',
    'get /api/clerk-of-works/download-pinned-map/:projectId/:level/:companyId?': { controller: 'clerkofworks', action: 'downloadcowpinnedlevelmap', tools: ['clerk_of_works'], multiple: false, data_type_key: '' },

    'post /api/project/:projectId/incident-report/add': { controller: 'projectincidentreport', action: 'createincidentreport', tools: ['incident_report'], multiple: false, data_type_key: '' },
    'post /api/project/incident-report/:id/update': 'ProjectIncidentReportController.updateIncidentReport',
    'post /api/project/:projectId/incident-report/:id/update-partially': { controller: 'projectincidentreport', action: 'partiallyUpdateIncidentReport', tools: ['incident_report'], multiple: false, data_type_key: '' },
    'post /api/ca/company/:companyId/incident-report/:id/update-partially': { controller: 'projectincidentreport', action: 'partiallyUpdateIncidentReportCA', tools: ['incident_report'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/incident-report/save': { controller: 'projectincidentreport', action: 'createorupdateincidentreport', tools: ['incident_report'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/incident-report/get-pending': { controller: 'projectincidentreport', action: 'getpendingincidentreport', tools: ['incident_report'], multiple: false, data_type_key: '' },
    'get  /api/project/:projectId/incident-report/:id': { controller: 'projectincidentreport', action: 'getincidentreport', tools: ['incident_report'], multiple: false, data_type_key: '' },
    'get /api/v3/project/:projectId/incident-report/list': `ProjectIncidentReportController.getProjectIncidentList`,
    'get  /api/project/:projectId/incident-reports/:companyId?': { controller: 'projectincidentreport', action: 'getprojectincidentreports', tools: ['incident_report'], multiple: false, data_type_key: '' },
    'get  /api/company/:companyId/incident-reports': 'ProjectIncidentReportController.getCompanyIncidentReports',
    'get /api/company/:companyId/incident-report/list': 'ProjectIncidentReportController.getCompanyIncidentList',
    'get  /api/project/:projectId/user-incident-reports/:userId': { controller: 'projectincidentreport', action: 'getuserincidentreports', tools: ['incident_report'], multiple: false, data_type_key: '' },
    'get /incident-report/download/:type/:irId/:timestamp': 'ProjectIncidentReportController.downloadIncidentReport', // @todo: Satyam Hardia: deprecated as of Jun 15, 2021.
    'post /api/v2/incident-report/download/:irId': 'ProjectIncidentReportController.downloadIncidentReportV2',
    'post /api/incident-report/:id/share': 'ProjectIncidentReportController.shareIncidentReport',
    'post /api/project/incident-report/:id/close-out': 'ProjectIncidentReportController.closeOutIncidentReport',
    'post /api/project/:projectId/incident-report/:id/close-out': 'ProjectIncidentReportController.closeOutIncidentReportV2',
    'post /api/project/:projectId/incident-action/:irId/close-out': { controller: 'projectincidentreport', action: 'closeoutincidentaction', tools: ['incident_report'], multiple: false, data_type_key: '' },
    'post /api/employer/:employerId/save-incident-alert-recipients': 'ProjectIncidentReportController.createOrUpdateIncidentAlertRecipients',
    'get /api/employer/:employerId/get-incident-alert-recipients': 'ProjectIncidentReportController.getIncidentAlertRecipients',
    'post /api/employer/:employerId/delete-incident-alert-recipient': 'ProjectIncidentReportController.deleteIncidentAlertRecipient',
    'get /api/project/:projectId/incident-report/actions-to-closeout/:userId': { controller: 'projectincidentreport', action: 'getincidentactionstocloseout', tools: ['incident_report'], multiple: false, data_type_key: '' },

    'post /api/project/:projectId/inspection-tour/add': { controller: 'projectinspectiontour', action: 'createinspectiontour', tools: ['inspections'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/inspection-tour/:id/update': { controller: 'projectinspectiontour', action: 'updateinspectiontour', tools: ['inspections'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/inspection-tour/save': { controller: 'projectinspectiontour', action: 'partialcreateorupdate', tools: ['inspections'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/inspection-tour/:id/item-closeout': { controller: 'projectinspectiontour', action: 'updatechecklisttocloseoutitem', tools: ['inspections'], multiple: false, data_type_key: '' },
    'get  /api/project/:projectId/inspection-tour/:id': { controller: 'projectinspectiontour', action: 'getinspectiontour', tools: ['inspections'], multiple: false, data_type_key: '' },
    'get  /api/project/:projectId/inspection-tour/get-pending': { controller: 'projectinspectiontour', action: 'getpendinginspectiontour', tools: ['inspections'], multiple: false, data_type_key: '' },
    'get  /api/project/:projectId/project-inspection-tours/:companyId?': { controller: 'projectinspectiontour', action: 'getprojectinspectiontours', tools: ['inspections'], multiple: false, data_type_key: '' },
    'get  /api/v2/project/:projectId/inspection-tour/list/:companyId?': { controller: 'projectinspectiontour', action: 'getProjectInspectionToursList', tools: ['inspections'], multiple: false, data_type_key: '' },
    'get  /api/project/:projectId/inspection-tours/:userId/:companyId?': { controller: 'projectinspectiontour', action: 'getprojectinspectiontoursbyuser', tools: ['inspections'], multiple: false, data_type_key: '' },
    'get  /project/:projectId/inspection-tour/:id/:type/:updatedAt': 'ProjectInspectionTourController.downloadInspectionTour',
    'post /api/project/:projectId/inspection-tour/download/:id': { controller: 'projectinspectiontour', action: 'downloadinspectiontourv1', tools: ['inspections'], multiple: false, data_type_key: '' },
    'post /api/company/:companyId/inspection-tour/participants/download': 'ProjectInspectionTourController.downloadParticipantsList',
    'get  /api/project/inspection-tour/meta-checklist': 'ProjectInspectionTourController.getInspectionTourMetaChecklist',
    'get /api/project/:projectId/inspection-tour/meta-checklist': 'ProjectInspectionTourController.getInspectionTourMetaChecklistV2',
    'post /api/project/:projectId/inspection-tour/:type/dashboard': { controller: 'projectinspectiontour', action: 'dashboardofinspectiontour', tools: ['inspections'], multiple: false, data_type_key: '' },
    'post /api/company/:companyId/inspection-tour/:type/dashboard': 'ProjectInspectionTourController.companyDashboardOfInspectionTour',
    'post /api/company/:companyId/subcontractors/:type/dashboard': 'ProjectInspectionTourController.subContractorDashboard',
    'post /api/company/:companyId/tagged-owner/:ownerId/:type/dashboard': 'ProjectInspectionTourController.taggedOwnerDashboard',
    'get /api/project/:projectId/inspection-tour/items-to-closeout/:userId': { controller: 'projectinspectiontour', action: 'getitemstocloseout', tools: ['inspections'], multiple: false, data_type_key: '' },
    'post /api/inspection-tour/:id/share': 'ProjectInspectionTourController.shareInspectionTourReport',

    'get /api/induction-questions/:projectId/list': 'InductionQuestionsController.getAllQuestionsOfProject',
    'get /api/induction-questions/:projectId/list/active': 'InductionQuestionsController.getActiveInductionQuestionsOfProject',
    'post /api/induction-questions/:projectId/save': 'InductionQuestionsController.saveInductionQuestions',
    'post /api/induction-questions/:projectId/create': 'InductionQuestionsController.createOrUpdateInductionQuestions',
    'get /api/induction-questions/:projectId/project-inductions': 'InductionQuestionsController.getProjectInductionQuestions',
    'get /api/induction-questions/:inductionQuestionsId': 'InductionQuestionsController.getInductionQuestions',
    'post /api/induction-questions/:projectId/check-answer': 'InductionQuestionsController.correctAnswer',
    'get /api/induction-questions/:projectId/admins-project-inductions': 'InductionQuestionsController.getProjectInductionQuestionsForAdmins',

    'post /api/elearning/module/:companyId/add': 'ELearningController.createELearningModule',
    'post /api/elearning/module/:companyId/update/:moduleId': 'ELearningController.updateELearningModule',
    'get  /api/elearning/modules/:companyId': 'ELearningController.getAllELearningModule',
    'get  /api/elearning/module/:companyId/:id': 'ELearningController.getELearningModule',
    'post  /api/elearning/module/invitation/:companyId': 'ELearningController.inviteToELearningModule',

    'post /api/project/:projectId/good-call/add': { controller: 'goodcall', action: 'creategoodcall', tools: ['good_calls'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/good-call/update/:id': { controller: 'goodcall', action: 'updategoodcall', tools: ['good_calls'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/good-call/:id': { controller: 'goodcall', action: 'getgoodcall', tools: ['good_calls'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/good-calls/:companyId?': { controller: 'goodcall', action: 'getprojectgoodcalls', tools: ['good_calls', 'observations'], multiple: true, data_type_key: 'data_type' },
    'get /api/project/:projectId/user-good-calls/:userId/:companyId?': { controller: 'goodcall', action: 'getusergoodcalls', tools: ['good_calls'], multiple: false, data_type_key: '' },
    'get /api/v2/user/:projectId/good-calls/:dataType/list': { controller: 'goodcall', action: 'getusergoodcalllist', tools: ['good_calls', 'observations'], multiple: true, data_type_key: 'dataType' },
    'get /api/v3/user/:projectId/good-calls/:dataType/list': 'GoodCallController.getUserGoodCallListV3',
    'get /api/project/:projectId/good-calls/filters': { controller: 'goodcall', action: 'getProjectBasedGoodCallUtils', tools: ['good_calls', 'observations'], multiple: true, data_type_key: 'dataType' },
    'post /api/project/:projectId/good-call/download/:id': { controller: 'goodcall', action: 'downloadgoodcallv1', tools: ['good_calls'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/observation/:type/dashboard': 'GoodCallController.projectObservationDashboard',

    'post /api/task-briefing/:projectId/add': { controller: 'taskbriefings', action: 'createtaskbriefing', tools: ['task_briefings'], multiple: false, data_type_key: '' },
    'post /api/task-briefing/:projectId/:id/update': { controller: 'taskbriefings', action: 'updatetaskbriefing', tools: ['task_briefings'], multiple: false, data_type_key: '' },
    'get  /api/task-briefings/:projectId/:companyId?': { controller: 'taskbriefings', action: 'getprojecttaskbriefings', tools: ['task_briefings'], multiple: false, data_type_key: '' },
    'get  /api/task-briefing/:projectId/search-briefings/:companyId?': { controller: 'taskbriefings', action: 'searchbriefings', tools: ['task_briefings'], multiple: false, data_type_key: '' },
    'get  /api/task-briefings/:projectId/user/:userId/:companyId?': { controller: 'taskbriefings', action: 'getprojecttaskbriefingsbyuser', tools: ['task_briefings'], multiple: false, data_type_key: '' },
    'get  /task-briefing/download/:projectId/:id/:type/:timestamp': 'TaskBriefingsController.downloadTaskBriefing',
    'post /api/project/:projectId/task-briefing/download/:id': { controller: 'taskbriefings', action: 'downloadtaskbriefingv1', tools: ['task_briefings'], multiple: false, data_type_key: '' },
    'post /api/task-briefing/:projectId/invite': { controller: 'taskbriefings', action: 'invitetotaskbriefing', tools: ['task_briefings'], multiple: false, data_type_key: '' },
    'post /api/task-briefing/:projectId/download-task-briefing-report': { controller: 'taskbriefings', action: 'downloadtaskbriefingreport', tools: ['task_briefings'], multiple: false, data_type_key: '' },
    'post /api/task-briefing/:projectId/id/:id/download': { controller: 'taskbriefings', action: 'downloadtaskbriefingxlsx', tools: ['task_briefings'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/task-briefing/register/download': 'TaskBriefingsController.downloadTaskBriefingRegister',
    'post /api/v2/tool-briefing/:projectId/save': { controller: 'taskbriefings', action: 'addtoolbriefing', tools: ['toolbox_talks', 'task_briefings', 'rams', 'work_package_plan', 'permit_register', 'take_5s'], multiple: true, data_type_key: 'tool_key' },
    'get /api/v2/tool-briefing/:toolKey/fetch/:projectId': { controller: 'taskbriefings', action: 'gettoolbriefingslist', tools: ['toolbox_talks', 'task_briefings', 'rams', 'work_package_plan'], multiple: true, data_type_key: 'toolKey' },
    'get /api/v2/tool-briefing/:toolKey/:projectId/tool-record/:recordId': { controller: 'taskbriefings', action: 'getbriefingtoolrecord', tools: ['toolbox_talks', 'task_briefings', 'rams', 'work_package_plan', 'take_5s'], multiple: true, data_type_key: 'toolKey' },
    'get /api/v2/tool-briefing/:toolKey/:projectId/tool-records': { controller: 'taskbriefings', action: 'getprojectbriefingtoolrecords', tools: ['toolbox_talks', 'task_briefings', 'rams', 'work_package_plan', 'take_5s'], multiple: true, data_type_key: 'toolKey' },
    'get /api/tool-briefing/:toolKey/available-briefing-tools/:projectId': { controller: 'taskbriefings', action: 'getBriefingToolRecordForInvite', tools: ['toolbox_talks', 'task_briefings', 'rams', 'work_package_plan', 'take_5s'], multiple: true, data_type_key: 'toolKey' },

    'post /api/work-package-plans/:projectId/add': { controller: 'workpackageplans', action: 'createworkpackageplan', tools: ['work_package_plan'], multiple: false, data_type_key: '' },
    'post /api/work-package-plans/:projectId/:id/update': { controller: 'workpackageplans', action: 'updateworkpackageplan', tools: ['work_package_plan'], multiple: false, data_type_key: '' },
    'get  /api/work-package-plans/:projectId/:companyId?': { controller: 'workpackageplans', action: 'getprojectworkpackageplans', tools: ['work_package_plan'], multiple: false, data_type_key: '' },
    'get  /api/work-package-plans/:projectId/search-briefings/:companyId?': 'WorkPackagePlansController.searchWorkPackagePlans',
    'get  /api/work-package-plans/:projectId/user/:userId/:companyId?': { controller: 'workpackageplans', action: 'getprojectworkpackageplansbyuser', tools: ['work_package_plan'], multiple: false, data_type_key: '' },
    'get  /api/work-package-plans/download/:projectId/:id/:type/:timestamp': { controller: 'workpackageplans', action: 'downloadworkpackageplans', tools: ['work_package_plan'], multiple: false, data_type_key: '' },
    'post /api/work-package-plans/:projectId/invite': { controller: 'workpackageplans', action: 'invitetoworkpackageplans', tools: ['work_package_plan'], multiple: false, data_type_key: '' },
    'post /api/work-package-plans/:projectId/download-report': { controller: 'workpackageplans', action: 'downloadworkpackageplansreport', tools: ['work_package_plan'], multiple: false, data_type_key: '' },
    'post /api/work-package-plans/:projectId/id/:id/download': { controller: 'workpackageplans', action: 'downloadworkpackageplansxlsx', tools: ['work_package_plan'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/work-package-plans/register/download': 'WorkPackagePlansController.downloadWorkPackagePlansRegister',

    'post /api/project/:projectId/rams/add': { controller: 'ProjectRams', action: 'createRams', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/project/:projectId/rams/:id/update': { controller: 'ProjectRams', action: 'updateRams', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/project/:projectId/rams/:id/assessment-form/save': 'ProjectRamsController.partiallySaveAssessmentForm',
    'get  /api/project/:projectId/rams/:ramsId/detail': 'ProjectRamsController.getProjectRamsById',
    'get  /api/project/:projectId/rams/:companyId?': { controller: 'projectrams', action: 'getprojectrams', tools: ['rams'], multiple: false, data_type_key: '' },
    'get  /api/project/:projectId/rams/user/:userId/:companyId?': { controller: 'projectrams', action: 'getprojectramsbyuser', tools: ['rams'], multiple: false, data_type_key: '' },
    'get  /api/project/:projectId/search-rams/:companyId?': 'ProjectRamsController.searchRams',
    'post /api/project/:projectId/invite-to-rams': { controller: 'ProjectRams', action: 'inviteToRams', tools: ['*'], multiple: true, data_type_key: '' },
    'get  /api/project/:projectId/rams/:id/download/:type': 'ProjectRamsController.downloadRams',
    'post /api/project/:projectId/rams/download-report': 'ProjectRamsController.downloadRamsReport',
    'post /api/project/:projectId/rams/:id/download-xlsx': 'ProjectRamsController.downloadRamsXLSX',
    'post /api/project/:projectId/rams/register/download': 'ProjectRamsController.downloadRamsRegister',
    'post /api/project/:projectId/rams/:id/status/update': 'ProjectRamsController.approveDeclineRams',
    'post /api/project/:projectId/rams/:id/preview': 'ProjectRamsController.downloadDocumentPreview',
    'get /api/project/:projectId/induction/rams': 'ProjectRamsController.getRamsForInduction',
    'post /api/admin/rams/copy': 'ProjectRamsController.copyAssessmentRamsForm',
    'get /api/v2/project/:projectId/available-rams': { controller: 'projectrams', action: 'getprojectramsv2', tools: ['rams'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/rams/list': { controller: 'projectrams', action: 'getProjectRamsList', tools: ['rams', 'permit_tool'], multiple: true, data_type_key: '' },
    'post /api/project/:projectId/rams/update-archive/:id': { controller: 'projectrams', action: 'archiveUnarchiveRams', tools: ['rams'], multiple: false, data_type_key: '' },
    'get  /api/project/:projectId/archived-rams': { controller: 'projectrams', action: 'getarchivedrams', tools: ['rams'], multiple: false, data_type_key: '' },
    'get  /api/project/:projectId/rams/:id/revisions': { controller: 'projectrams', action: 'ramsRevisionList', tools: ['rams'], multiple: false, data_type_key: '' },
    'get  /api/project/:projectId/rams-group/:groupId/revisions': { controller: 'projectrams', action: 'searchRamsByRevision', tools: ['rams'], multiple: false, data_type_key: '' },
    'get  /api/project/:projectId/rams/:id/recent-revision': { controller: 'projectrams', action: 'ramsRecentRevision', tools: ['rams'], multiple: false, data_type_key: '' },


    'post /api/project/:projectId/daily-activity/add': { controller: 'MetaDailyActivities', action: 'addActivity', tools: ['*'], multiple: true, data_type_key: '' },
    'get /api/project/:projectId/daily-activities/:companyId?': { controller: 'MetaDailyActivities', action: 'getProjectActivitiesList', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/project/:projectId/daily-activities/import/:companyId?': 'MetaDailyActivitiesController.importProjectActivities',
    'post /api/project/:projectId/daily-activities/delete-all/:companyId?': 'MetaDailyActivitiesController.deleteAllMetaActivities',
    'post /api/project/:projectId/daily-activity/:id/delete': 'MetaDailyActivitiesController.deleteMetaActivity',

    'post /api/project/:projectId/plant-machinery/add': 'MetaProjectPlantMachineryController.addPlant',
    'get /api/project/:projectId/plant-machinery/:companyId?': 'MetaProjectPlantMachineryController.getProjectPlantsList',
    'post /api/project/:projectId/plant-machinery/import/:companyId?': 'MetaProjectPlantMachineryController.importProjectPlants',
    'post /api/project/:projectId/plant-machinery/delete-all/:companyId?': 'MetaProjectPlantMachineryController.deleteAllMetaPlants',
    'post /api/project/:projectId/plant-machinery/:id/delete': 'MetaProjectPlantMachineryController.deleteMetaPlant',

    'post /api/project/:projectId/asset-vehicle/add': 'ProjectAssetsController.addVehicleAsset',
    'post /api/project/:projectId/asset-vehicle/update/:id': 'ProjectAssetsController.updateVehicleAsset',
    // 'get  /api/project/:projectId/asset-vehicle/:id': 'ProjectAssetsController.getVehicleAsset',
    'get  /api/project/:projectId/asset/:assetType/filters-list': { controller: 'projectassets', action: 'getAssetTaggedOwners', tools: ['assets'], multiple: false, data_type_key: '' },
    'get  /api/project/:projectId/project-asset-vehicles': { controller: 'projectassets', action: 'getProjectAssetVehicles', tools: ['assets'], multiple: false, data_type_key: '' },
    'get  /api/v2/project/:projectId/project-asset-vehicles/list': { controller: 'projectassets', action: 'getProjectAssetVehiclesV2', tools: ['assets'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/asset-vehicle/:vehicleId/inspection/add': 'ProjectAssetsController.addAssetVehicleInspection',
    // 'post /api/project/:projectId/asset-vehicle/:vehicleId/inspection/:id/update': 'ProjectAssetsController.updateAssetVehicleInspection',
    // 'get  /api/project/:projectId/asset-vehicle/:vehicleId/inspections': 'ProjectAssetsController.getAssetVehicleInspections',
    'post /api/project/:projectId/asset-vehicle/archive/:id': 'ProjectAssetsController.archiveUnarchiveVehicleAsset',
    'get /api/asset-vehicle/:vehicleId/recent-inspection': 'ProjectAssetsController.getMostRecentVehicleInspection',
    // 'post /api/project/:projectId/asset-vehicle/:vehicleId/inspections/download': 'ProjectAssetsController.downloadWeeklyInspections',
    'post /api/project/:projectId/asset-equipment/add': { controller: 'ProjectAssets', action: 'addEquipmentAsset', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/project/:projectId/asset-equipment/update/:id': 'ProjectAssetsController.updateEquipmentAsset',
    // 'post /api/project/:projectId/asset-equipment/:equipmentId/inspection/:id/update': 'ProjectAssetsController.updateAssetEquipmentInspection',
    // 'get  /api/project/:projectId/asset-equipment/:id': 'ProjectAssetsController.getEquipmentAsset',
    'get  /api/project/:projectId/project-asset-equipments': { controller: 'projectassets', action: 'getProjectAssetEquipments', tools: ['assets'], multiple: false, data_type_key: '' },
    'get  /api/v2/project/:projectId/project-asset-equipments/list': { controller: 'projectassets', action: 'getProjectAssetEquipmentsV2', tools: ['assets'], multiple: false, data_type_key: '' },

    'post /api/project/:projectId/asset-equipment/:equipmentId/inspection/add': 'ProjectAssetsController.addAssetEquipmentInspection',
    // 'get  /api/project/:projectId/asset-equipment/:equipmentId/inspections': 'ProjectAssetsController.getAssetEquipmentInspections',
    'post /api/project/:projectId/asset-equipment/archive/:id': 'ProjectAssetsController.archiveUnarchiveEquipmentAsset',
    //leaving this for time being for it to be able to serve the old mobile app users
    'post /api/project/:projectId/asset/:assetId/:assetType/inspections/download': 'ProjectAssetsController.downloadAssetWeeklyInspections',
    'post /api/project/:projectId/:assetType(asset-temporary|vehicle|equipment)/register-download': 'ProjectAssetsController.downloadRegisterXLSX',

    'get /api/project/:projectId/asset-temporary-works/list': { controller: 'projectassets', action: 'getProjectAssetTemporaryWorks', tools: ['assets'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/:assetType(asset-temporary|asset-vehicle|asset-equipment)/faults/:id': { controller: 'projectassets', action: 'getProjectAssetFaults', tools: ['assets'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/asset-temporary/add/:employerId?': 'ProjectAssetsController.addTemporaryWorkAsset',
    'post /api/project/:projectId/asset-temporary/update/:id/:employerId?': 'ProjectAssetsController.updateTemporayrWorkAsset',
    'post /api/project/:projectId/asset-temporary/:temporaryWorkId/inspection/add': 'ProjectAssetsController.addAssetTemporaryWorkInspection',
    'post /api/project/:projectId/assets/get-asset-from-qrcode': 'ProjectAssetsController.getAssetFromCodeString',

    'get  /api/project/:projectId/:assetType(asset-temporary|asset-vehicle|asset-equipment)/:id(\\d+)': 'ProjectAssetsController.getAsset',
    'get  /api/project/:projectId/:assetType(asset-temporary|asset-vehicle|asset-equipment)/:assetId/inspections': 'ProjectAssetsController.getAssetInspections',
    'post /api/project/:projectId/:assetType(asset-temporary|asset-vehicle|asset-equipment)/:assetId/inspection/:id/update': 'ProjectAssetsController.updateAssetInspection',
    'post /api/project/:projectId/:assetType(asset-temporary|asset-vehicle|asset-equipment|equipment)/:assetId/inspections/download': 'ProjectAssetsController.downloadAssetWeeklyInspections',

    'post /api/quality-checklist/add': { controller: 'qualitychecklist', action: 'createChecklist', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/quality-checklist/update/:qclId': { controller: 'qualitychecklist', action: 'updateChecklist', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/quality-checklist/section/sign-off/:clId': { controller: 'qualitychecklist', action: 'signOffInspectionSection', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/quality-checklist/sign-offs/:assignedTo': { controller: 'qualitychecklist', action: 'getAssignedSignOffList', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/company/:companyId/quality-checklist/update/:qclId': { controller: 'qualitychecklist', action: 'updateCompanyChecklist', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/quality-checklist/add': { controller: 'qualitychecklist', action: 'createChecklistV2', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/company/:companyId/quality-checklist/add': { controller: 'qualitychecklist', action: 'createCompanyChecklist', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/quality-checklist/update/:qclId': { controller: 'qualitychecklist', action: 'updateChecklistV2', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/quality-checklist/add-inspection': { controller: 'qualitychecklist', action: 'createChecklistInspection', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/quality-checklist/:projectId/update-inspection/:reportId': { controller: 'qualitychecklist', action: 'updatechecklistinspection', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/quality-checklist/:projectId/delete-inspection/:reportId': { controller: 'qualitychecklist', action: 'deletechecklistinspection', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get /api/quality-checklist/inspection-report/:projectId/:reportId': { controller: 'qualitychecklist', action: 'getchecklistinspectionreport', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/quality-checklist/:qclId/partial-reports/':{ controller: 'qualitychecklist', action: 'getPartialCompletedReportCountSA', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get /api/company/:companyId/quality-checklist/:qclId/partial-reports/':{ controller: 'qualitychecklist', action: 'getPartialCompletedReportCountCA', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/quality-checklist/add-inspection': { controller: 'qualitychecklist', action: 'createChecklistInspectionV2', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/itp-inspection/:itpId/item-close-out': { controller: 'qualitychecklist', action: 'updateitptocloseoutitem', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get  /api/quality-checklist/search-checklist': 'QualityChecklistController.searchChecklist',
    'get /api/project/:projectId/quality-checklist/search-checklist': 'QualityChecklistController.searchChecklistV2',
    'get /api/product-quality-checklist/:projectId/:companyId?': { controller: 'qualitychecklist', action: 'getprojectchecklists', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get /api/company-quality-checklist/:employerId': { controller: 'qualitychecklist', action: 'getCompanyChecklists', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get /api/product-checklist-inspections/:projectId/:companyId?': { controller: 'qualitychecklist', action: 'getchecklistinspections', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get /api/user-checklist-inspections/:userId/:projectId': { controller: 'qualitychecklist', action: 'getuserclinspections', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get /api/v2/checklist-inspections/:projectId/:userId/:qclId?': { controller: 'qualitychecklist', action: 'getuserclinspectionsV2', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get /api/checklist-inspections/by-qcl-id/:qclId/:projectId': { controller: 'qualitychecklist', action: 'getclinspectionsbyqclid', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get /api/checklist-report/:id/:type/:updatedAt/:companyId?': 'QualityChecklistController.downloadQclReport',
    'get /api/v2/project/:projectId/quality-checklist/:qclId/checklist-report/:reportId/:type/:updatedAt?':  { controller: 'qualitychecklist', action : 'downloadQclReportV2', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/qualtiy-checklist/closeout/list': { controller: 'qualitychecklist', action : 'reviewAndCloseoutList', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/qualtiy-checklist/open-defects/count': { controller: 'qualitychecklist', action: 'getItpOpenDefectsCount', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/itp-report/:id/share': 'QualityChecklistController.shareItpReport',

    'post /api/inspection-builder/checklist/add': 'InspectionBuilderController.createIBChecklist',
    'post /api/project/:projectId/inspection-builder/checklist/add': 'InspectionBuilderController.createIBChecklistV2',
    'post /api/company/:companyId/inspection-builder/checklist/add': 'InspectionBuilderController.createCompanyIBChecklistV2',
    'post /api/inspection-builder/checklist/update/:ibId': 'InspectionBuilderController.updateIBChecklist',
    'post /api/project/:projectId/inspection-builder/checklist/update/:ibId': 'InspectionBuilderController.updateIBChecklistV2',
    'post /api/company/:companyId/inspection-builder/checklist/update/:ibId': 'InspectionBuilderController.updateCompanyIBChecklistV2',
    'post /api/inspection-builder/report/add': 'InspectionBuilderController.createIBReport',
    'post /api/inspection-builder/report/:projectId/update/:reportId': { controller: 'inspectionbuilder', action: 'updateIBReport', tools: ['inspections'], multiple: false, data_type_key: '' },
    'get /api/inspection-builder/report/:projectId/:reportId': { controller: 'inspectionbuilder', action: 'getProjectIBReport', tools: ['inspections'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/inspection-builder/report/add': { controller: 'inspectionbuilder', action: 'createIBReportV2', tools: ['inspections'], multiple: false, data_type_key: '' },
    'get /api/inspection-builder/project-checklists/:projectId/:companyId?': { controller: 'inspectionbuilder', action: 'getProjectIBChecklists', tools: ['inspections'], multiple: false, data_type_key: '' },
    'get /api/inspection-builder/company-checklists/:companyId': 'InspectionBuilderController.getCompanyIBChecklists',
    'post /api/inspection-builder/project-reports/:projectId/:reportId': { controller: 'inspectionbuilder', action: 'deleteInspectionReport', tools: ['inspections'], multiple: false, data_type_key: '' },
    'get /api/inspection-builder/project-reports/:projectId/:ibId/:companyId?': { controller: 'InspectionBuilder', action: 'getProjectIBReports', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/inspection-builder/project-reports/:projectId/delete-reports': { controller: 'inspectionbuilder', action: 'deleteInspectionReports', tools: ['inspections'], multiple: false, data_type_key: '' },
    'get /api/inspection-builder/user-reports/:ibId/:projectId': { controller: 'InspectionBuilder', action: 'getUserIBReports', tools: ['*'], multiple: true, data_type_key: '' },
    'post /api/inspection-builder/report/:id/item-closeout': 'InspectionBuilderController.closeOutIbClReportItem',
    'post /api/project/:projectId/inspection-builder/report/:id/item-closeout': 'InspectionBuilderController.closeOutIbClReportItemV2',
    'post /api/inspection-builder/report/download/:ibId': 'InspectionBuilderController.downloadIbClReport',
    'post /api/project/:projectId/inspection-builder/report/download/:ibId': 'InspectionBuilderController.downloadIbClReportV2',
    'get /api/project/:projectId/inspection-builder/items-to-closeout/:userId': 'InspectionBuilderController.getIBItemsToCloseout',
    'post /api/inspection-builder/invite-to-nm': 'InspectionBuilderController.inviteToNMsOnInspection',
    'post /api/company/:companyId/inspection-builder/invite-to-nm': 'InspectionBuilderController.inviteToNMsOnInspectionV2',
    'post /api/project/:projectId/inspection-builder/:ibId/:type/dashboard': 'InspectionBuilderController.dashboardOfInspectionBuilder',
    'post /api/company/:companyId/inspection-builder/:ibId/:type/dashboard': 'InspectionBuilderController.companyDashboardOfInspectionBuilder',
    'post /api/company/:companyId/inspection-builder/:ibId/participants/download': 'InspectionBuilderController.downloadParticipantsList',
    'post /api/company/:companyId/ib/:ibId/subcontractors/:type/dashboard': 'InspectionBuilderController.subContractorDashboard',
    'post /api/company/:companyId/ib/:ibId/tagged-owner/:ownerId/download/dashboard': 'InspectionBuilderController.taggedOwnerDashboard',
    'post /api/inspection-builder/:id/share': 'InspectionBuilderController.shareInspectionBuilderReport',

    'get /api/project/:projectId/user-actions': 'ActionsController.getAllActions',
    'post /api/user-actions/inspection-builder/:id/item-closeout': 'InspectionBuilderController.closeOutIbClReportAction',
    'post /api/user-actions/inspection-tour/:id/item-closeout': 'ProjectInspectionTourController.closeOutChecklistItemAction',
    'post /api/user-actions/itp-inspection/:itpId/item-closeout': { controller: 'qualitychecklist', action: 'closeoutitpitemaction', tools: ['quality_checklist'], multiple: false, data_type_key: '' },
    'post /api/user-actions/close-call/:ccId/closeout': 'CloseCallController.closeOutCloseCallAction',
    'post /api/user-actions/good-call/:id/closeout': { controller: 'goodcall', action: 'closeoutgoodcallaction', tools: ['good_calls'], multiple: false, data_type_key: '' },
    'post /api/user-actions/incident-report/:irId/action-closeout': { controller: 'projectincidentreport', action: 'closeoutassignedincidentaction', tools: ['incident_report'], multiple: false, data_type_key: '' },


    'post /api/project/:projectId/site-messaging/send': { controller: 'sitemessaging', action: 'sendmessage', tools: ['site_messaging'], multiple: false, data_type_key: '' },
    'post /api/company/:employerId/site-messaging/send':{ controller: 'sitemessaging', action: 'sendCompanyMessage', tools: ['site_messaging'], multiple: false, data_type_key: '' },
    'post /api/company/:employerId/site-messaging/:messageId/resend':{ controller: 'sitemessaging', action: 'resendCompanyMessage', tools: ['site_messaging'], multiple: false, data_type_key: '' },
    'post /api/company/:employerId/site-messaging/message/:messageId/edit':{ controller: 'sitemessaging', action: 'editCompanyMessage', tools: ['site_messaging'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/site-messaging/resend': { controller: 'sitemessaging', action: 'resendmessage', tools: ['site_messaging'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/site-messaging/:id/mark/:markas': 'SiteMessagingController.markAsMessage',
    'get  /api/project/:projectId/site-messaging/messages': 'SiteMessagingController.getProjectMessages',
    'get  /api/ca/company/:employerId/site-messaging/messages/list': 'SiteMessagingController.getCompanyMessagesCA',
    'get  /api/user/company/:employerId/site-messaging/messages/list': 'SiteMessagingController.getCompanyMessagesRecievedByUser',
    'get  /api/user/site-messaging/messages/list/:type': 'SiteMessagingController.getConsolidatedMessagesList',
    'get  /api/user/site-messaging/messages/unread/count': 'SiteMessagingController.getConsolidatedUnreadMessageCount',
    'get  /api/user/:userId/site-messaging/type/:type/messages/:projectId?': 'SiteMessagingController.getUserMessages',
    'get  /api/user/:userId/site-messaging/sent/messages/:projectId?': 'SiteMessagingController.getUserSentMessages',
    'get  /api/user/:userId/site-messaging/received/messages/:projectId?': 'SiteMessagingController.getUserReceivedMessages',
    'get  /api/project/:projectId/active-users': { controller: 'User', action: 'getProjectActiveUsers', tools: ['*'], multiple: true, data_type_key: '' },
    'get  /api/project/:projectId/site-messaging/:messageId': 'SiteMessagingController.getMessage',
    'get  /api/project/:projectId/site-messaging/messages/count/:all': 'SiteMessagingController.getUserMessagesCount',
    'post  /api/company/:employerId/site-messaging/message/delete': 'SiteMessagingController.deleteCompanyMessage',
    'post  /api/company/:employerId/site-messaging/:id/mark/:markas': 'SiteMessagingController.markAsCompanyMessage',
    'get /api/company/:employerId/site-messaging/:messageId/:type':'SiteMessagingController.getCompanyMessage',
    'get /api/company/:employerId/site-messaging/:messageId/recipients':'SiteMessagingController.getCompanyMessageRecipientsDetails',
    'post /api/company-induction/create': 'CompanyInductionController.createCompanyInduction',
    'post /api/admin/company/:companyId/import/competency-matrix': `SkillMatrixController.processBulkImportCompetencyMatrix`,
    'get /api/company/:employerId/skills-matrix/list': 'SkillMatrixController.getCompanySkillMatrixList',
    'get /api/company/:employerId/skills/remaining': 'SkillMatrixController.companySkillsToAdd',
    'get /api/company/:employerId/skills/list': 'SkillMatrixController.companySkillsList',
    'post /api/ca/:employerId/skills-matrix/save': 'SkillMatrixController.saveSkillMatrixRecord',
    'post /api/ca/:employerId/skills-matrix/:ruleId/delete': 'SkillMatrixController.deleteSkillMatrixRecordById',
    'post /api/ca/:employerId/company-induction/:ciId/update-status': 'CompanyInductionController.updateCompanyInductionStatus',
    'get /api/ca/:employerId/company-induction/:ciId': 'CompanyInductionController.getCompanyInductionById',
    'get /api/ca/:employerId/company-inductions/list': `CompanyInductionController.getCompanyInductionsList`,

    'post /api/ca/:employerId/card-conduct/create': 'ConductCardController.createCard',
    'post /api/ca/:employerId/card-conduct/update/:id': 'ConductCardController.updateCard',
    'get /api/ca/:employerId/card-conducts/get': 'ConductCardController.getCards',
    'get /api/ca/:employerId/card-conduct/get/:id': 'ConductCardController.getCardById',
    'get /api/ca/:employerId/card-conduct/delete/:id': 'ConductCardController.deleteCard',
    'post /api/ca/:employerId/card-conduct/unassign/:id': 'ConductCardController.unassignedConductCard',
    'get /api/ca/:employerId/card-conduct/assigned/card': 'ConductCardController.getAssignedConductCard',
    'post /api/ca/:employerId/card-conduct/assigned/card/download': 'ConductCardController.downloadAssignedConductCard',
    'post /api/project/:projectId/card-conducts/download': 'ConductCardController.downloadAssignedProjectConductCard',
    'get /api/project/:projectId/card-conducts': 'ConductCardController.getCardsByProjectCompany',
    'post /api/project/:projectId/card-conduct/assign/:id': 'ConductCardController.assignConductCard',
    'post /api/project/:projectId/card-conduct/unassign/:id': 'ConductCardController.unassignedConductCardPp',

    'get /api/ca/:employerId/asset-config/list': 'CompanyAssetConfigController.getCompanyAssetConfig',
    'post /api/ca/:employerId/asset-config/:assetId/save': 'CompanyAssetConfigController.saveCompanyAssetConfig',
    'post /api/admin/:employerId/asset-config/seed': 'CompanyAssetConfigController.seedCompanyAssetConfig',
    'get /api/project/:projectId/asset-config/list': 'CompanyAssetConfigController.getProjectAssetConfig',

    'get /api/inndex/meta-district': 'ResourceController.getMetaDistrict',

    'get /api/project/:projectId/active/permits': { controller: 'permit', action: 'getProjectActivePermits', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/permit/request/create': { controller: 'permit', action: 'createPermitRequest', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/permit/request/:id/resubmit': { controller: 'permit', action: 'resubmitPermitRequest', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/permit/request/:id/sign-off': { controller: 'permit', action: 'signOffPermitRequest', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/permit/request/:id/closeout/sign-off': { controller: 'permit', action: 'closeoutSignOffPermitRequest', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/user/permit-request/list': { controller: 'permit', action: 'getPermitRequestsByRequestor', tools: ['permit_tool'], policy_checks: ['isInductedUserOf'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/permit-request/list': { controller: 'permit', action: 'getPermitRequestsByProject', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/permit-request/:prId': { controller: 'permit', action: 'getPermitRequestById', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/sign-off-users/:configId': { controller: 'permit', action: 'fetchPermitSignOffUsers', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/pending/signoff/permit-requests': { controller: 'permit', action: 'permitRequestsToSignOff', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/permit-request/:id/:action': { controller: 'permit', action: 'permitRequestAction', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/permit-request/:id/closeout-notify': { controller: 'permit', action: 'requestCloseoutNotify', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/permit-request/:id/next-signoff': { controller: 'permit', action: 'getNextSignOffUsers', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/permit-requests/download': { controller: 'permit', action: 'downloadPermitRequests', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'post /api/project/:projectId/permit-request/:id/share': { controller: 'permit', action: 'sharePermit', tools: ['permit_tool'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/permit-request/user/briefed': { controller: 'permit', action: 'getBriefedPermitsByUser', tools: ['permit_tool'], policy_checks: ['isInductedUserOf'], multiple: false, data_type_key: '' },
    'get /api/project/:projectId/permit-request/:prId/status': { controller: 'permit', action: 'getPermitStatus', tools: ['permit_tool'], multiple: false, data_type_key: '' },


    'get /api/seed/asset-temporary-work-inspection-checklist':'SeedingController.seedTemporaryWorksAssetInspectionChecklist',
    'get /api/seed-user-health-assessment': 'SeedingController.seedUserHealthAssessment',
    'get /api/seed/file-to-img-translation/:userId/:userFileId?': 'FileController.seedGenerateFileTranslationById',
    'post /api/user/send-verification-mail/:userId': 'UserController.sendEmailVerificationMail',
    'get /api/user/projects-and-companies/list':'InductionController.getProjectsAndCompaniesAsSeparateLists',
    'post /api/seed-qrcode-image': 'SeedingController.attachQrImageWithProjects',
    'post /api/seed/projects/location-key': 'SeedingController.attachProjectLocationKey',
    'post /api/seed/refresh-user-travel-distance': 'SeedingController.refreshUserTravelDistance',
    'get /api/seed-category/:settingName': 'SeedingController.seedHazardCategory',
    'get /api/seed-lightingconditions': 'SeedingController.seedLightingConditions',
    'get /api/seed-user-medical-assessment': 'SeedingController.seedUserMedicalAssessment',
    'post /api/remove-user-medical-assessment-answer': 'SeedingController.removeMedicalAssessmentQuestion',
    'post /api/seed/export-inductions-to-smartsheet/:projectId': 'SeedingController.exportInductionsToSmartsheet',
    'post /api/seed/export-timesheet-to-smartsheet/:projectId': 'SeedingController.exportOldTimeLogsToSmartsheet',
    'post /api/seed/induction-booking/slots': 'SeedingController.seedProjectInductionSlots',
    'POST /api/seed/induction-request/:projectId/docs': 'SeedingController.seedInductionsWithUserDocs',
    'POST /api/seed/procore/inductions/export': 'SeedingController.exportInductedUsersToProcore',
    'POST /api/seed/procore/manpower-logs/export': 'SeedingController.exportManpowerLogsToProcore',
    'post /api/seed/reprocess-time-logs/:source': 'SeedingController.reprocessPunchLogs',
    'get /api/seed-inspection-tour-checklist': 'SeedingController.seedInspectionTourChecklist',
    'post /api/seed/populate-meta-data-by-key': 'SeedingController.seedMetaDataByKey',
    'get /api/seed/vehicle-asset-inspection-checklist': 'SeedingController.seedVehicleAssetInspectionChecklist',
    'get /api/seed/correct-vehicle-asset-records': 'SeedingController.correctVehicleAssetRecords',
    'get /api/seed/asset-equipment-items': 'SeedingController.seedAssetEquipmentItems',
    'post /api/seed/type-of-assets': 'SeedingController.seedTypeOfAssets',
    'get /api/seed/equipment-asset-inspection-checklist': 'SeedingController.seedEquipmentAssetInspectionChecklist',
    'get /api/seed/fill-alt-phrase-fields': 'SeedingController.fillPhraseFields',
    'get /api/seed/fix-company-merge-records': 'SeedingController.fixCompanyMergeRecords',
    'get /api/seed/convert-number-asset-photo-ref-to-array/:type': 'SeedingController.seedingAssetTables',
    'get /api/seed/ir-action-categories': 'SeedingController.incidentReportActionCategories',
    'get /api/seed/cow-categories': 'SeedingController.clerkOfWorksCategory',
    'get /api/seed/itp-custom-fields': 'SeedingController.seedItpCustomFields',
    'get /api/seed/ib/update/score-configuration': 'SeedingController.seedingIBScoreType',
    'get /api/inspection/export-items-with-missing-category': 'SeedingController.importExportItems',
    'get /api/update/scoring-wording/ib/:id': 'SeedingController.replaceIbWording',
    'post /api/seed/incident-report-cause-type-options': 'SeedingController.seedIncidentCauseTypAndOptions',
    'get /api/seed/fix-good-call-record-refs': 'SeedingController.fixGCRecordRefs',
    'get /api/update/ib/:ibId/ib-inspection-rating': 'SeedingController.fixingIbInspectionRating',
    'get /api/update/ib-cl/:inspectionId/question': 'SeedingController.updateIbClQuestion',
    'get /api/update/asset-inspection/:assetType/faults': 'SeedingController.assetFaultCorrection',
    'get /api/associate/fault-id/:assetType/inspection-fault': 'SeedingController.associateFaultIdToAssetInspectionFaults',
    'get /api/ib-report/rating/calculate': 'SeedingController.ibReportRatingPercentage',
    'get /api/inspection-tour/rating/calculate': 'SeedingController.inspectionTourRatingPercentage',
    'get /api/seed/ib-report/correction/tagged/company': 'SeedingController.seedMultiTaggedCompanies',
    'get /api/seed/convert-supply-chain-company-names-to-ids': 'SeedingController.convertSupplyChainCompanyNamesToIds',
    'get /api/seed/fix-induction-question-ids': 'SeedingController.inductionQueIdsCorrection',
    'get /api/access-logs': 'ResourceController.getAccessLogs',
    'get /api/interaction-logs': 'ResourceController.getInteractionLogs',
    'post /api/transfer/observation/to/close-call/project/:projectId/user/:userId': 'SeedingController.transferObservationToCloseCall',
    'post /api/asset/inspections/correction': 'SeedingController.assetInspectionRecordCorrection',
    'post /api/seed/old-briefings/:toolKey/save-to-new-tables': 'SeedingController.saveOldBriefingsToNewTables',
    'post /api/seed/old-briefings/:toolKey/save-take_5s-to-new-tables': 'SeedingController.saveOldTake5sBriefingsToNewTables',
    'post /api/seed/weather-logs-for-activities': 'SeedingController.seedWeatherLogsForDailyActivities',
    'post /api/seed/save-induction-request-coordinates':'SeedingController.saveInductionRequestCoordinate',
    'post /api/seed/countries-nationality-meta':'SeedingController.seedCountriesAndNationalityMetaData',
    /*'get /api/seed/oto/uk-districts': 'SeedingController.importDistricts',*/
    'get /api/seed/oto/district-into-inductions/:limit': 'SeedingController.seedDistrictIntoInductions',
    'post /api/seed/move-project-data-for-company-change': 'SeedingController.updateProjectDataForCompanyChange',
    'get /api/seed/oto/get-ib-checklist-enabled-projects': 'SeedingController.seedIbChecklistIntoProject',
    'post /api/seed/oto/create-face-collection-for-projects': 'SeedingController.seedFaceCollectionForProjects',
    'get /api/seed/permit-types': 'SeedingController.seedPermitTypes',
    'post /api/seed/induction-request/mobile-number': 'seedingController.seedMobileNumberWithCodeInInductionRequest',
    'post /api/seed/travel-time-data':'SeedingController.seedTravelTimeData',
    'post /api/seed/valid-project-postcodes':'SeedingController.updateInvalidPostcodeProjects',
    'get /api/seed/parent-company/to/project':'SeedingController.seedingParentCompanyToProject',
    'get /api/seed/convert/doc-to-pdf':'SeedingController.convertDocToPdf',
    'post /api/seed/incident-report/person-affected/insert': 'SeedingController.incidentReportInsertUserRefInPersonAffected',
    'get /api/seed/backfill/rams': 'SeedingController.backFillRamsForRevision',
    'get /api/seed/update/rollcall': 'SeedingController.rollCallUpdateUserColumns',
    'get /api/company/:companyId/sso-configurations': 'CompanySSOConfigurationController.getCompanySSOConfigurations',
    'post /api/company/:companyId/sso-configurations': 'CompanySSOConfigurationController.addCompanySSOConfiguration',
    'post /api/company/:companyId/sso-configurations/:id/update': 'CompanySSOConfigurationController.updateCompanySSOConfiguration',
    'post /api/company/:companyId/sso-configurations/:id/delete': 'CompanySSOConfigurationController.deleteCompanySSOConfiguration',
    'get /api/sso-configurations/provider-keys': 'CompanySSOConfigurationController.getAvailableProviderKeys',
    'post /api/seed/update/ib-reports': 'SeedingController.correctingIbReports',
    'get /api/seed/close-call/default-custom-fields': 'SeedingController.seedCloseCallDefaults',
    'get /api/seed/update/project-permit-config': 'SeedingController.updateProjectPermitConfig',
    'get /api/seed/update/permit-requests': 'SeedingController.updatePermitRequests',
    'get /api/seed/materials': 'SeedingController.seedMaterials',
    '/': {
        action: 'index',
        controller: 'AuthController',
        //view: 'pages/site-home'
    },
    '/*': {
        action: 'index',
        controller: 'AuthController',
        //view: 'pages/homepage',
        skipAssets: true,
        skipRegex: /^\/api\/.*|\/$/,
    },

    /***************************************************************************
     *                                                                          *
     * More custom routes here...                                               *
     * (See https://sailsjs.com/config/routes for examples.)                    *
     *                                                                          *
     * If a request to a URL doesn't match any of the routes in this file, it   *
     * is matched against "shadow routes" (e.g. blueprint routes).  If it does  *
     * not match any of those, it is matched against static assets.             *
     *                                                                          *
     ***************************************************************************/


    //  ╔═╗╔═╗╦  ╔═╗╔╗╔╔╦╗╔═╗╔═╗╦╔╗╔╔╦╗╔═╗
    //  ╠═╣╠═╝║  ║╣ ║║║ ║║╠═╝║ ║║║║║ ║ ╚═╗
    //  ╩ ╩╩  ╩  ╚═╝╝╚╝═╩╝╩  ╚═╝╩╝╚╝ ╩ ╚═╝


    //  ╦ ╦╔═╗╔╗ ╦ ╦╔═╗╔═╗╦╔═╔═╗
    //  ║║║║╣ ╠╩╗╠═╣║ ║║ ║╠╩╗╚═╗
    //  ╚╩╝╚═╝╚═╝╩ ╩╚═╝╚═╝╩ ╩╚═╝


    //  ╔╦╗╦╔═╗╔═╗
    //  ║║║║╚═╗║
    //  ╩ ╩╩╚═╝╚═╝


};
