/**
 * Created by spatel on 18/3/19.
 */

const moment = require('moment');
const npid = require('npid');
const {SharedCronFn: { shouldISkipCronExecution, executionTimeLogger}} = require('./crons/shared.fn');
const refreshBadgeEventCountsLockFile = './refreshBadgeEventCountsLockFile.pid';
npid.remove(refreshBadgeEventCountsLockFile);
const weatherSyncLockFile = './weatherSyncLockFile.pid';
npid.remove(weatherSyncLockFile);
const hourlyWatherSyncLockFile = './hourlyWatherSyncLockFile.pid';
npid.remove(hourlyWatherSyncLockFile);
const competencyLockFile = './processCompetencyChecks.pid';
npid.remove(competencyLockFile);
const rejectedInductionsLockFile = './processRejectedInductions.pid';
npid.remove(rejectedInductionsLockFile);
const weeklyTimesheetLockCheckFile = './weeklyTimesheetLockCheckFile.pid';
npid.remove(weeklyTimesheetLockCheckFile);
const weeklyCCnotificationsLockCheckFile = './weeklyCCnotificationsLockCheckFile.pid';
npid.remove(weeklyCCnotificationsLockCheckFile);
const fatigueDailyDutyEndReminderLockCheckFile = './fatigueDailyDutyEndReminderLockCheckFile.pid';
npid.remove(fatigueDailyDutyEndReminderLockCheckFile);
const closeoutDueReminderLockCheckFile = './closeoutDueReminderLockCheckFile.pid';
npid.remove(closeoutDueReminderLockCheckFile);
const assetVehicleDocExpireNotificationLockCheckFile = './assetVehicleDocExpireNotificationLockCheckFile.pid';
npid.remove(assetVehicleDocExpireNotificationLockCheckFile);
const assetEquipmentDocExpireNotificationLockCheckFile = './assetEquipmentDocExpireNotificationLockCheckFile.pid';
npid.remove(assetEquipmentDocExpireNotificationLockCheckFile);
const ibDraftReportAutoDeleteLockCheckFile = './ibDraftReportAutoDeleteLockCheckFile.pid';
npid.remove(ibDraftReportAutoDeleteLockCheckFile);
const itpChecklistDraftReportAutoDeleteLockCheckFile = './itpChecklistDraftReportAutoDeleteLockCheckFile.pid';
npid.remove(itpChecklistDraftReportAutoDeleteLockCheckFile);


const { FakeDataCrons } = require('./crons/fake-data.cron');
const { SmartSheetCron } = require('./crons/smartsheet.cron');
const { ProCoreCron } = require('./crons/procore.cron');
const { OptimaCron } = require('./crons/optima.cron');
const { TouchByteCron } = require('./crons/touch-byte.cron');
const { InductionCron } = require('./crons/induction.cron');

module.exports.cron = {
    ...InductionCron,
    ...TouchByteCron,
    ...SmartSheetCron,
    ...ProCoreCron,
    ...OptimaCron,
    createFakeInLog: FakeDataCrons.createFakeInLog,
    createFakeOutLog: FakeDataCrons.createFakeOutLog,
    refreshBadgeEventCounts: {
        schedule: '10 20,40 01,02 * * *', // Run @ daily 01:20:10AM / 01:40:10AM / 02:20:10AM / 02:40:10AM

        onTick: function (cb) {

            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('refreshBadgeEventCounts')) {
                return false;
            }

            const startedOn = moment();
            sails.log.info('Starting cron `refreshBadgeEventCounts` on: ', startedOn.format());

            try {
                // mark process as in process
                let pid = npid.create(refreshBadgeEventCountsLockFile);

                // Refresh badge count cache
                CacheService.refreshBadgeEventsCountCache().then(() => {
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                }).catch(e => {
                    sails.log.error('`refreshBadgeEventCounts` cron failure', e);
                    pid.remove();
                    cb(startedOn);
                });


            } catch (err) {
                // Skip existing process is running
                sails.log.error(`=====> Lock file already exists, skipping refreshBadgeEventCounts execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },

        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('Refresh badge count cache task completed..!', completedOn.format());
            executionTimeLogger('refreshBadgeEventCounts', startedOn, completedOn);
        },
    },
    dailyWeatherSync: {
        // ['seconds', 'minutes', 'hours', 'dayOfMonth', 'month', 'dayOfWeek']
        schedule: '10 1 6 * * *', // Run @ 10th second of daily 6AM

        onTick: function (cb) {

            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('dailyWeatherSync')) {
                return false;
            }

            const startedOn = moment();
            sails.log.info('Starting cron `dailyWeatherSync` on: ', startedOn.format());

            try {
                // mark process as in process
                let pid = npid.create(weatherSyncLockFile);

                // call weather Sync service
                WeatherSyncService.sync().then((done) => {
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                }).catch(e => {
                    sails.log.error('dailyWeatherSync cron failure ', e);
                    pid.remove();
                    cb(startedOn);
                });


            } catch (err) {
                // Skip existing process is running
                sails.log.info(`=====> Lock file already exists, skipping dailyWeatherSync execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },


        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('dailyWeatherSync task completed..!', completedOn.format());
            executionTimeLogger('dailyWeatherSync', startedOn, completedOn);
        },
    },

    hourlyWeatherSync: {
        // ['seconds', 'minutes', 'hours', 'dayOfMonth', 'month', 'dayOfWeek']
        schedule: '0 15 */12 * * *', // Run @ 15th minute every 12th hour

        onTick: function (cb) {

            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('hourlyWeatherSync')) {
                return false;
            }

            const startedOn = moment();
            sails.log.info('Starting cron `hourlyWeatherSync` on: ', startedOn.format());

            try {
                // mark process as in process
                let pid = npid.create(hourlyWatherSyncLockFile);

                // call weather Sync service
                WeatherSyncService.syncHourly().then((done) => {
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                }).catch(e => {
                    sails.log.error('hourlyWeatherSync cron failure ', e);
                    pid.remove();
                    cb(startedOn);
                });


            } catch (err) {
                // Skip existing process is running
                sails.log.info(`=====> Lock file already exists, skipping the hourly-weather-cron execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },


        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('Sync Hourly Weather task completed..!', completedOn.format());
            executionTimeLogger('hourlyWeatherSync', startedOn, completedOn);
        },
    },

    processCompetencyChecks: {
        // ['seconds', 'minutes', 'hours', 'dayOfMonth', 'month', 'dayOfWeek']
        // schedule: '1 */3 * * * *',
        schedule: '7 */30 * * * *', // twice in an hour.
        // schedule: '1 * * * * *',

        onTick: function (cb) {

            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('processCompetencyChecks')) {
                return false;
            }

            const startedOn = moment();
            sails.log.info('Starting cron `processCompetencyChecks` on: ', startedOn.format());

            try {
                // mark process as in process.
                let pid = npid.create(competencyLockFile);

                // call Competency Expire Service
                CompetencyExpireService.competencyExpire((syncError, syncResults) => {
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                });
            } catch (err) {
                // Skip existing process is running
                sails.log.info(`=====> Lock file already exists, skipping the cron execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },

        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('Competency expiration task completed..!', completedOn.format());
            executionTimeLogger('processCompetencyChecks', startedOn, completedOn);
        },
    },
    processRejectedInductions: {
        // ['seconds', 'minutes', 'hours', 'dayOfMonth', 'month', 'dayOfWeek']
        schedule: '30 1 0 * * *', // at 10th minute every hour.
        // schedule: '1 * * * * *',

        onTick: function (cb) {

            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('processRejectedInductions')) {
                return false;
            }

            const startedOn = moment();
            sails.log.info('Starting cron `processRejectedInductions` on: ', startedOn.format());

            try {
                // mark process as in process.
                let pid = npid.create(rejectedInductionsLockFile);

                // call Competency Expire Service
                DataProcessingService.removeRejectedInductions((syncError, syncResults) => {
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                });
            } catch (err) {
                // Skip existing process is running
                sails.log.info(`=====> Lock file already exists, skipping the cron execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },

        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('Process Rejected Inductions task completed..!', completedOn.format());
            executionTimeLogger('processRejectedInductions', startedOn, completedOn);
        },
    },
    sendWeeklyTimeSheet: {
        schedule: '0 0 9 * * 1', // At 09:00 on Monday.

        onTick: function (cb) {

            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('sendWeeklyTimesheet')) {
                return false;
            }

            const startedOn = moment();
            sails.log.info('Starting cron `sendWeeklyTimesheet` on: ', startedOn.format());

            try {
                // mark process as in process.
                let pid = npid.create(weeklyTimesheetLockCheckFile);

                // send weekly report to users
                DataProcessingService.sendWeeklyTimesheet(() => {

                }).then(response => {
                    sails.log.info('Response from weekly report call', response);
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                });

            } catch (err) {
                // Skip existing process is running
                sails.log.info(`=====> Lock file already exists, skipping the cron execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },

        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('Weekly timesheet send task completed..!', completedOn.format());
            executionTimeLogger('sendWeeklyTimesheet', startedOn, completedOn);
        },
        start: false,
    },

    sendWeeklyCCnotifications: {
        schedule: '0 0 9 * * *', // At 09:00 everyday.

        onTick: function (cb) {
            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('sendWeeklyCCnotifications')) {
                return false;
            }

            const startedOn = moment();
            sails.log.info('Starting cron `sendWeeklyCCnotifications` on: ', startedOn.format());

            try {
                // mark process as in process.
                let pid = npid.create(weeklyCCnotificationsLockCheckFile);

                // send weekly report to users
                DataProcessingService.sendWeeklyCCemail(() => {

                }).then(response => {
                    sails.log.info('Response from `sendWeeklyCCnotifications`', response);
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                });

            } catch (err) {
                // Skip existing process is running
                sails.log.info(`=====> Lock file already exists, skipping the cron execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },

        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('`sendWeeklyCCnotifications` task completed..!', completedOn.format());
            executionTimeLogger('sendWeeklyCCnotifications', startedOn, completedOn);
        },
    },

    fatigueDailyDutyEndReminder: {
        schedule: '0 */5 * * * *',

        onTick: function (cb) {
            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('fatigueDailyDutyEndReminder')) {
                return false;
            }

            const startedOn = moment();
            sails.log.info('Starting cron `fatigueDailyDutyEndReminder` on: ', startedOn.format());

            try {
                // mark process as in process.
                let pid = npid.create(fatigueDailyDutyEndReminderLockCheckFile);

                // send weekly report to users
                FatigueManagementService.dailyDutyEndsReminderProcess(() => {

                }).then(response => {
                    sails.log.info('Response from `fatigueDailyDutyEndReminder`', response);
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                });

            } catch (err) {
                // Skip existing process is running
                sails.log.info(`=====> Lock file already exists, skipping the cron execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },

        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('`fatigueDailyDutyEndReminder ` task completed..!', completedOn.format());
            executionTimeLogger('fatigueDailyDutyEndReminder', startedOn, completedOn);
        },
    },

    //Inspection Tour Item Closeout Due Reminder
    itemCloseoutDueReminder: {
        schedule: '0 1 8 * * *', //at 08:01AM everyday
        onTick: function (cb) {
            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('itemCloseoutDueReminder')) {
                return false;
            }

            const startedOn = moment();
            sails.log.info('Starting cron `itemCloseoutDueReminder` on: ', startedOn.format());

            try {
                // mark process as in process.
                let pid = npid.create(closeoutDueReminderLockCheckFile);

                // send weekly report to users
                DataProcessingService.itemCloseoutDueReminder(() => {

                }).then(response => {
                    sails.log.info('Response from `itemCloseoutDueReminder`', response);
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                });

            } catch (err) {
                // Skip existing process is running
                sails.log.info(`=====> Lock file already exists, skipping the cron execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },

        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('`itemCloseoutDueReminder ` task completed..!', completedOn.format());
            executionTimeLogger('itemCloseoutDueReminder', startedOn, completedOn);
        },
    },

    //Vehicle's certificates expiring notification
    assetVehicleDocExpireNotification: {
        schedule: '10 10 0 * * *', //at 12:10:10AM everyday
        onTick: function (cb) {
            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('assetVehicleDocExpireNotification')) {
                return false;
            }

            const startedOn = moment();
            sails.log.info('Starting cron `assetVehicleDocExpireNotification` on: ', startedOn.format());

            try {
                // mark process as in process.
                let pid = npid.create(assetVehicleDocExpireNotificationLockCheckFile);

                // send weekly report to users
                EmailService.assetVehicleDocExpireEmail(() => {

                }).then(response => {
                    sails.log.info('Response from `assetVehicleDocExpireNotification`', response);
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                });

            } catch (err) {
                // Skip existing process is running
                sails.log.info(`=====> Lock file already exists, skipping the cron execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },

        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('`assetVehicleDocExpireNotification ` task completed..!', completedOn.format());
            executionTimeLogger('assetVehicleDocExpireNotification', startedOn, completedOn);
        },
    },

    //Equipment's certificates expiring notification
    assetEquipmentDocExpireNotification: {
        schedule: '10 2 0 * * *', //at 12:02:10AM everyday
        onTick: function (cb) {
            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('assetEquipmentDocExpireNotification')) {
                return false;
            }

            const startedOn = moment();
            sails.log.info('Starting cron `assetEquipmentDocExpireNotification` on: ', startedOn.format());

            try {
                // mark process as in process.
                let pid = npid.create(assetEquipmentDocExpireNotificationLockCheckFile);

                // send weekly report to users
                EmailService.assetEquipmentDocExpireEmail(() => {

                }).then(response => {
                    sails.log.info('Response from `assetEquipmentDocExpireNotification`', response);
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                });

            } catch (err) {
                // Skip existing process is running
                sails.log.info(`=====> Lock file already exists, skipping the cron execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },

        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('`assetEquipmentDocExpireNotification ` task completed..!', completedOn.format());
            executionTimeLogger('assetEquipmentDocExpireNotification', startedOn, completedOn);
        },
    },
    //Delete Inspection Draft report older then 30 days
    ibDraftReportAutoDelete: {
        schedule: '0 20 0 * * *', //at 12:20:00 AM everyday
        onTick: function (cb) {
            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('ibDraftReportAutoDelete')) {
                return false;
            }

            const startedOn = moment();
            sails.log.info('Starting cron `ibDraftReportAutoDelete` on: ', startedOn.format());

            try {
                // mark process as in process.
                let pid = npid.create(ibDraftReportAutoDeleteLockCheckFile);

                // Deleting inspection draft reports
                DataProcessingService.autoDeleteIbReports(() => {

                }).then(response => {
                    sails.log.info('Response from `ibDraftReportAutoDelete`', response);
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                });
            } catch (err) {
                // Skip existing process is running
                sails.log.info(`=====> Lock file already exists, skipping the cron execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },
        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('`ibDraftReportAutoDelete ` task completed..!', completedOn.format());
            executionTimeLogger('ibDraftReportAutoDelete', startedOn, completedOn);
        },
    },

    //Delete Itp Draft report older then 30 days
    itpDraftReportAutoDelete: {
        schedule: '0 20 0 * * *', //at 12:20:00 AM everyday
        onTick: function (cb) {
            // Skip if host is local or mode is development or on cluster
            if (shouldISkipCronExecution('itpDraftReportAutoDelete')) {
                return false;
            }
            const startedOn = moment();
            sails.log.info('Starting cron `itpDraftReportAutoDelete` on: ', startedOn.format());
            try {
                // mark process as in process.
                let pid = npid.create(itpChecklistDraftReportAutoDeleteLockCheckFile);
                // Deleting inspection draft reports
                DataProcessingService.autoDeleteITPChecklistReports(() => {
                }).then(response => {
                    sails.log.info('Response from `itpDraftReportAutoDelete`', response);
                    // mark process as completed
                    pid.remove();
                    cb(startedOn);
                });
            } catch (err) {
                // Skip existing process is running
                sails.log.info(`=====> Lock file already exists, skipping the cron execution. (${moment().format()}) <=====`);
                if (err.code !== `EEXIST`) {
                    sails.log.error(err);
                }
                cb(startedOn);
            }
        },
        onComplete: function (startedOn) {
            let completedOn = moment();
            sails.log.info('`itpDraftReportAutoDelete ` task completed..!', completedOn.format());
            executionTimeLogger('itpDraftReportAutoDelete', startedOn, completedOn);
        },
        start: false,
    },
};
