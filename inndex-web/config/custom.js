/**
 * Custom configuration
 * (sails.config.custom)
 *
 * One-off settings specific to your application.
 *
 * For more information on custom configuration, visit:
 * https://sailsjs.com/config/custom
 */

module.exports.custom = {
    APP_ENV: process.env.APP_ENV ? process.env.APP_ENV : 'unknown',
    REFRESH_TOKEN_SECRET: process.env.REFRESH_TOKEN_SECRET ? process.env.REFRESH_TOKEN_SECRET : 'KKVZWeer5P8DCYvSIBZPLk1y689Q98ImcfuGeG9u',
    REFRESH_COUNTRIES_LIST_META: process.env.REFRESH_COUNTRIES_LIST_META === 'true',
    POPPLER_BIN_PATH: process.env.POPPLER_BIN_PATH || '/usr/bin',
    DVLA_API_HOST_URL: process.env.DVLA_API_HOST_URL || 'https://driver-vehicle-licensing.api.gov.uk',
    DVLA_API_KEY: process.env.DVLA_API_KEY || 'innDexTest',
    PRO_CORE_LIVE_MODE: process.env.PRO_CORE_LIVE_MODE === 'true',
    PRO_CORE_APP_CLIENT_ID: process.env.PRO_CORE_APP_CLIENT_ID || "-na-",
    PRO_CORE_APP_CLIENT_SECRET: process.env.PRO_CORE_APP_CLIENT_SECRET || "-na-",
    CLEAN_UP_OLD_API_ACCESS_LOGS: process.env.CLEAN_UP_OLD_API_ACCESS_LOGS === 'true',
    INTERACTION_LOG_ENABLED: process.env.INTERACTION_LOG_ENABLED === 'true',
    INNDEX_SLACK_BOT_HOOK_URL: process.env.INNDEX_SLACK_BOT_HOOK_URL || null,
    INNDEX_FIRST_INDUCTION_HOOK_URL: process.env.INNDEX_FIRST_INDUCTION_HOOK_URL || null,
    OPTIMA_LISTENER_KEY: process.env.OPTIMA_LISTENER_KEY || 'tataucszounvkkctl6n2',
    OPTIMA_INTEGRATION_SERVER: process.env.OPTIMA_INTEGRATION_SERVER || 'https://integrations.almas-industries.com/optima/',
    OPTIMA_GATEWAY_SECRET_KEY: process.env.OPTIMA_GATEWAY_SECRET_KEY || '-',

    CITB_V2_API_SERVER: process.env.CITB_V2_API_SERVER || "https://cardchecker.cscsonline.uk.com",
    RTW_CHECK_API_HOST: process.env.RTW_CHECK_API_HOST || "https://inndex-test.compliant101.co.uk",
    WEBHOOK_LISTENER_KEY: process.env.WEBHOOK_LISTENER_KEY || 'TSC3g4y2ejAdt8YtmqL7PA2cfT7kl',
    RTW_STATUS_UPDATE_LISTENING_FN_HOST: process.env.RTW_STATUS_UPDATE_LISTENING_FN_HOST || "https://7zq0nzzyd3.execute-api.eu-west-2.amazonaws.com/dev",

    AZURE_APP_CLIENT_ID: process.env.AZURE_APP_CLIENT_ID || "-na-",
    AZURE_APP_CLIENT_SECRET: process.env.AZURE_APP_CLIENT_SECRET || "-na-",
    AZURE_INNDEX_TENANT_ID: process.env.AZURE_INNDEX_TENANT_ID || "-na-",
    AZURE_SSO_TENANT_IDs: (process.env.AZURE_SSO_TENANT_IDs || "-na-").split(','),

    POWERBI_INNDEX_TENANT_ID: process.env.POWERBI_INNDEX_TENANT_ID || "-na-",
    POWERBI_CLIENT_ID: process.env.POWERBI_CLIENT_ID || "-na-",
    POWERBI_CLIENT_SECRET: process.env.POWERBI_CLIENT_SECRET || "-na-",
    POWERBI_GROUP_ID: process.env.POWERBI_GROUP_ID || "-na-",
    POWERBI_IDENTITY_ROLE: process.env.POWERBI_IDENTITY_ROLE || 'inndex_admin',
    POWERBI_IDENTITY_COMPANY_ROLE: process.env.POWERBI_IDENTITY_COMPANY_ROLE || 'inndex_company',
    // base url is for API sever domain
    // public url is for web ui app / assets domain
    PUBLIC_URL: (process.env.PUBLIC_URL ? process.env.PUBLIC_URL : '') + (process.env.URL_SUFFIX ? process.env.URL_SUFFIX : ''),
    PUBLIC_DOMAIN: (process.env.PUBLIC_URL ? process.env.PUBLIC_URL : ''),  // for cases where we don't want default URL suffix
    baseUrl: process.env.BASE_URL ? process.env.BASE_URL : '',
    AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID ? process.env.AWS_ACCESS_KEY_ID : '',
    AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY ? process.env.AWS_SECRET_ACCESS_KEY : '',
    AWS_REGION_SLS: process.env.AWS_REGION_SLS ? process.env.AWS_REGION_SLS : 'eu-west-2',
    LAMBDA_SINGAL_PDF_FN: process.env.LAMBDA_SINGAL_PDF_FN ? process.env.LAMBDA_SINGAL_PDF_FN : 'inndex-sls-dev-html-to-pdf-v1',
    NOTIFICATION_FN_KEY: process.env.NOTIFICATION_FN_KEY ? process.env.NOTIFICATION_FN_KEY : '-',
    LAMBDA_QUEUE_PUSH_MESSAGE_MAILS_FN: process.env.LAMBDA_QUEUE_PUSH_MESSAGE_MAILS_FN ? process.env.LAMBDA_QUEUE_PUSH_MESSAGE_MAILS_FN : 'inndex-notification-sls-dev-queue-push-notification-v1',
    LAMBDA_QUEUE_SITE_MESSAGE_MAILS_FN: process.env.LAMBDA_QUEUE_SITE_MESSAGE_MAILS_FN ? process.env.LAMBDA_QUEUE_SITE_MESSAGE_MAILS_FN : 'inndex-notification-sls-dev-queue-email-message-v1',
    LAMBDA_EB_CRON_SCHEDULER: process.env.LAMBDA_EB_CRON_SCHEDULER ? process.env.LAMBDA_EB_CRON_SCHEDULER : 'inndex-sls-dev-event-bridge-cron-scheduler-v1',
    LAMBDA_EB_CRON_DELETE: process.env.LAMBDA_EB_CRON_DELETE ? process.env.LAMBDA_EB_CRON_DELETE : 'inndex-sls-dev-event-bridge-delete-schedule-v1',
    LAMBDA_ONE_TIME_EVENT_SCHEDULER: process.env.LAMBDA_ONE_TIME_EVENT_SCHEDULER ? process.env.LAMBDA_ONE_TIME_EVENT_SCHEDULER : 'inndex-sls-dev-onetime-scheduler-v1',
    LAMBDA_MULTIPLE_PDF_FN: process.env.LAMBDA_MULTIPLE_PDF_FN ? process.env.LAMBDA_MULTIPLE_PDF_FN : 'inndex-sls-dev-html-to-multi-pdf-v1',
    LAMBDA_ZIPPED_IMGS_FN: process.env.LAMBDA_ZIPPED_IMGS_FN ? process.env.LAMBDA_ZIPPED_IMGS_FN : 'inndex-sls-dev-archive-images-and-download-v1',
    LAMBDA_MERGE_PDF_FN: process.env.LAMBDA_MERGE_PDF_FN ? process.env.LAMBDA_MERGE_PDF_FN : 'inndex-sls-dev-merge-pdf-v1',
    LAMBDA_DOC_TO_PDF_FN: process.env.LAMBDA_DOC_TO_PDF_FN ? process.env.LAMBDA_DOC_TO_PDF_FN : 'inndex-sls-dev-doc-to-pdf-v1',
    LAMBDA_VIDEO_THUMBNAIL_FN: process.env.LAMBDA_VIDEO_THUMBNAIL_FN ? process.env.LAMBDA_VIDEO_THUMBNAIL_FN : 'inndex-sls-dev-video-thumbnail-v1',
    INNDEX_S3_BUCKET: process.env.INNDEX_S3_BUCKET ? process.env.INNDEX_S3_BUCKET : 'inndex',
    AUTO_CLEAN_S3_BUCKET: process.env.AUTO_CLEAN_S3_BUCKET ? process.env.AUTO_CLEAN_S3_BUCKET : 'gc',
    S3_BUCKET: process.env.S3_BUCKET ? process.env.S3_BUCKET : '',
    CDN_DOMAIN_HOST: process.env.CDN_DOMAIN_HOST ? process.env.CDN_DOMAIN_HOST : 'cdn.inndex.co.uk',
    TMP_CF_DOMAIN_HOST: process.env.TMP_CF_DOMAIN_HOST ? process.env.TMP_CF_DOMAIN_HOST : 'tmp.inndex.co.uk',
    NEW_PROJECT_MAIL_ALERT_ADDRESSES: process.env.NEW_PROJECT_MAIL_ALERT_ADDRESSES ? process.env.NEW_PROJECT_MAIL_ALERT_ADDRESSES : '<EMAIL>',
    SOURCE_MAIL_ADDRESS: process.env.SOURCE_MAIL_ADDRESS ? process.env.SOURCE_MAIL_ADDRESS : '<EMAIL>',
    CONTACT_MAIL_ADDRESS: process.env.CONTACT_MAIL_ADDRESS ? process.env.CONTACT_MAIL_ADDRESS : '',
    SUPPORT_MAIL_ADDRESSES: process.env.SUPPORT_MAIL_ADDRESSES ? process.env.SUPPORT_MAIL_ADDRESSES : '',

    EXCLUDED_EMAIL_ADDRESSES: process.env.EXCLUDED_EMAIL_ADDRESSES ? process.env.EXCLUDED_EMAIL_ADDRESSES : '',
    TEST_USER_EMAIL_ADDRESSES: process.env.TEST_USER_EMAIL_ADDRESSES ? process.env.TEST_USER_EMAIL_ADDRESSES : '',
    CUT_OFF_HOUR: process.env.CUT_OFF_HOUR ? parseInt(process.env.CUT_OFF_HOUR) : 0,

    SECURE_AUTH_KEY: process.env.SECURE_AUTH_KEY ? process.env.SECURE_AUTH_KEY : '',
    STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY ? process.env.STRIPE_PUBLISHABLE_KEY : '',
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY ? process.env.STRIPE_SECRET_KEY : '',
    MAX_THUMBNAIL_ITERATION_PER_CRON: process.env.MAX_THUMBNAIL_ITERATION_PER_CRON ? process.env.MAX_THUMBNAIL_ITERATION_PER_CRON : '',
    MAX_ITERATION_PER_CRON: process.env.MAX_ITERATION_PER_CRON ? process.env.MAX_ITERATION_PER_CRON : '',
    OPTIMA_SERVER_TIMEZONE: process.env.OPTIMA_SERVER_TIMEZONE ? process.env.OPTIMA_SERVER_TIMEZONE : 'Europe/London',
    CAPTCHA_SECRET_KEY: process.env.CAPTCHA_SECRET_KEY ? process.env.CAPTCHA_SECRET_KEY : '',
    DISTANCE_METRIX_KEY: process.env.DISTANCE_METRIX_KEY ? process.env.DISTANCE_METRIX_KEY : '', // Google Distance Metrix Key, Geocode key
    COUNTRY_LAYER_KEY: process.env.COUNTRY_LAYER_KEY ? process.env.COUNTRY_LAYER_KEY : '-na-',
    CITB_API_SERVER: process.env.CITB_API_SERVER ? process.env.CITB_API_SERVER : '',
    CITB_API_P_KEY: process.env.CITB_API_P_KEY ? process.env.CITB_API_P_KEY : '',
    ACCU_WEATHER_API_KEY: process.env.ACCU_WEATHER_API_KEY ? process.env.ACCU_WEATHER_API_KEY : '',
    HOURLY_ACCU_WEATHER_API_KEY: process.env.HOURLY_ACCU_WEATHER_API_KEY ? process.env.HOURLY_ACCU_WEATHER_API_KEY : 'QAeVY3nY65nqlIQTOaYoS1gReAppAZJy',
    MOBILE_APP_REQUEST_KEY: process.env.MOBILE_APP_REQUEST_KEY ? process.env.MOBILE_APP_REQUEST_KEY : 'feb6fa0217efa912ae9ef847293c06b0',
    INTENT_URL_PREFIX: process.env.INTENT_URL_PREFIX ? process.env.INTENT_URL_PREFIX : 'inndex://',
    INNDEX_IOS_APP: process.env.INNDEX_IOS_APP ? process.env.INNDEX_IOS_APP : 'https://itunes.apple.com/gb/app/inndex/id1457696057?mt=8',
    INNDEX_ANDROID_APP: process.env.INNDEX_ANDROID_APP ? process.env.INNDEX_ANDROID_APP : 'https://play.google.com/store/apps/details?id=me.innDex.mobile',
    ENC_SECRET_KEY: process.env.ENC_SECRET_KEY ? process.env.ENC_SECRET_KEY : 'sXJrmB0jiDrc5PRexY/CKqfmEzqrZQKmTppwKd93waI=',
    MAPBOX_SECRET_ACCESS_KEY:process.env.MAPBOX_SECRET_ACCESS_KEY,
};
